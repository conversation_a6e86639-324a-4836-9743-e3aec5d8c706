html {
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  font-family: sans-serif;
}

body {
  margin: 0;
}

article, aside, details, figcaption, figure, footer, header, hgroup, main, menu, nav, section, summary {
  display: block;
}

audio, canvas, progress, video {
  vertical-align: baseline;
  display: inline-block;
}

audio:not([controls]) {
  height: 0;
  display: none;
}

[hidden], template {
  display: none;
}

a {
  background-color: #0000;
}

a:active, a:hover {
  outline: 0;
}

abbr[title] {
  border-bottom: 1px dotted;
}

b, strong {
  font-weight: bold;
}

dfn {
  font-style: italic;
}

h1 {
  margin: .67em 0;
  font-size: 2em;
}

mark {
  color: #000;
  background: #ff0;
}

small {
  font-size: 80%;
}

sub, sup {
  vertical-align: baseline;
  font-size: 75%;
  line-height: 0;
  position: relative;
}

sup {
  top: -.5em;
}

sub {
  bottom: -.25em;
}

img {
  border: 0;
}

svg:not(:root) {
  overflow: hidden;
}

hr {
  box-sizing: content-box;
  height: 0;
}

pre {
  overflow: auto;
}

code, kbd, pre, samp {
  font-family: monospace;
  font-size: 1em;
}

button, input, optgroup, select, textarea {
  color: inherit;
  font: inherit;
  margin: 0;
}

button {
  overflow: visible;
}

button, select {
  text-transform: none;
}

button, html input[type="button"], input[type="reset"] {
  -webkit-appearance: button;
  cursor: pointer;
}

button[disabled], html input[disabled] {
  cursor: default;
}

button::-moz-focus-inner, input::-moz-focus-inner {
  border: 0;
  padding: 0;
}

input {
  line-height: normal;
}

input[type="checkbox"], input[type="radio"] {
  box-sizing: border-box;
  padding: 0;
}

input[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

input[type="search"] {
  -webkit-appearance: none;
}

input[type="search"]::-webkit-search-cancel-button, input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

legend {
  border: 0;
  padding: 0;
}

textarea {
  overflow: auto;
}

optgroup {
  font-weight: bold;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td, th {
  padding: 0;
}

@font-face {
  font-family: webflow-icons;
  src: url("data:application/x-font-ttf;charset=utf-8;base64,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") format("truetype");
  font-weight: normal;
  font-style: normal;
}

[class^="w-icon-"], [class*=" w-icon-"] {
  speak: none;
  font-variant: normal;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  font-family: webflow-icons !important;
}

.w-icon-slider-right:before {
  content: "";
}

.w-icon-slider-left:before {
  content: "";
}

.w-icon-nav-menu:before {
  content: "";
}

.w-icon-arrow-down:before, .w-icon-dropdown-toggle:before {
  content: "";
}

.w-icon-file-upload-remove:before {
  content: "";
}

.w-icon-file-upload-icon:before {
  content: "";
}

* {
  box-sizing: border-box;
}

html {
  height: 100%;
}

body {
  color: #333;
  background-color: #fff;
  min-height: 100%;
  margin: 0;
  font-family: Arial, sans-serif;
  font-size: 14px;
  line-height: 20px;
}

img {
  vertical-align: middle;
  max-width: 100%;
  display: inline-block;
}

html.w-mod-touch * {
  background-attachment: scroll !important;
}

.w-block {
  display: block;
}

.w-inline-block {
  max-width: 100%;
  display: inline-block;
}

.w-clearfix:before, .w-clearfix:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-clearfix:after {
  clear: both;
}

.w-hidden {
  display: none;
}

.w-button {
  color: #fff;
  line-height: inherit;
  cursor: pointer;
  background-color: #3898ec;
  border: 0;
  border-radius: 0;
  padding: 9px 15px;
  text-decoration: none;
  display: inline-block;
}

input.w-button {
  -webkit-appearance: button;
}

html[data-w-dynpage] [data-w-cloak] {
  color: #0000 !important;
}

.w-code-block {
  margin: unset;
}

pre.w-code-block code {
  all: inherit;
}

.w-optimization {
  display: contents;
}

.w-webflow-badge, .w-webflow-badge > img {
  box-sizing: unset;
  width: unset;
  height: unset;
  max-height: unset;
  max-width: unset;
  min-height: unset;
  min-width: unset;
  margin: unset;
  padding: unset;
  float: unset;
  clear: unset;
  border: unset;
  border-radius: unset;
  background: unset;
  background-image: unset;
  background-position: unset;
  background-size: unset;
  background-repeat: unset;
  background-origin: unset;
  background-clip: unset;
  background-attachment: unset;
  background-color: unset;
  box-shadow: unset;
  transform: unset;
  direction: unset;
  font-family: unset;
  font-weight: unset;
  color: unset;
  font-size: unset;
  line-height: unset;
  font-style: unset;
  font-variant: unset;
  text-align: unset;
  letter-spacing: unset;
  -webkit-text-decoration: unset;
  text-decoration: unset;
  text-indent: unset;
  text-transform: unset;
  list-style-type: unset;
  text-shadow: unset;
  vertical-align: unset;
  cursor: unset;
  white-space: unset;
  word-break: unset;
  word-spacing: unset;
  word-wrap: unset;
  transition: unset;
}

.w-webflow-badge {
  white-space: nowrap;
  cursor: pointer;
  box-shadow: 0 0 0 1px #0000001a, 0 1px 3px #0000001a;
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 2147483647 !important;
  color: #aaadb0 !important;
  overflow: unset !important;
  background-color: #fff !important;
  border-radius: 3px !important;
  width: auto !important;
  height: auto !important;
  margin: 0 !important;
  padding: 6px !important;
  font-size: 12px !important;
  line-height: 14px !important;
  text-decoration: none !important;
  display: inline-block !important;
  position: fixed !important;
  inset: auto 12px 12px auto !important;
  transform: none !important;
}

.w-webflow-badge > img {
  position: unset;
  visibility: unset !important;
  opacity: 1 !important;
  vertical-align: middle !important;
  display: inline-block !important;
}

h1, h2, h3, h4, h5, h6 {
  margin-bottom: 10px;
  font-weight: bold;
}

h1 {
  margin-top: 20px;
  font-size: 38px;
  line-height: 44px;
}

h2 {
  margin-top: 20px;
  font-size: 32px;
  line-height: 36px;
}

h3 {
  margin-top: 20px;
  font-size: 24px;
  line-height: 30px;
}

h4 {
  margin-top: 10px;
  font-size: 18px;
  line-height: 24px;
}

h5 {
  margin-top: 10px;
  font-size: 14px;
  line-height: 20px;
}

h6 {
  margin-top: 10px;
  font-size: 12px;
  line-height: 18px;
}

p {
  margin-top: 0;
  margin-bottom: 10px;
}

blockquote {
  border-left: 5px solid #e2e2e2;
  margin: 0 0 10px;
  padding: 10px 20px;
  font-size: 18px;
  line-height: 22px;
}

figure {
  margin: 0 0 10px;
}

figcaption {
  text-align: center;
  margin-top: 5px;
}

ul, ol {
  margin-top: 0;
  margin-bottom: 10px;
  padding-left: 40px;
}

.w-list-unstyled {
  padding-left: 0;
  list-style: none;
}

.w-embed:before, .w-embed:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-embed:after {
  clear: both;
}

.w-video {
  width: 100%;
  padding: 0;
  position: relative;
}

.w-video iframe, .w-video object, .w-video embed {
  border: none;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

fieldset {
  border: 0;
  margin: 0;
  padding: 0;
}

button, [type="button"], [type="reset"] {
  cursor: pointer;
  -webkit-appearance: button;
  border: 0;
}

.w-form {
  margin: 0 0 15px;
}

.w-form-done {
  text-align: center;
  background-color: #ddd;
  padding: 20px;
  display: none;
}

.w-form-fail {
  background-color: #ffdede;
  margin-top: 10px;
  padding: 10px;
  display: none;
}

label {
  margin-bottom: 5px;
  font-weight: bold;
  display: block;
}

.w-input, .w-select {
  color: #333;
  vertical-align: middle;
  background-color: #fff;
  border: 1px solid #ccc;
  width: 100%;
  height: 38px;
  margin-bottom: 10px;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.42857;
  display: block;
}

.w-input::placeholder, .w-select::placeholder {
  color: #999;
}

.w-input:focus, .w-select:focus {
  border-color: #3898ec;
  outline: 0;
}

.w-input[disabled], .w-select[disabled], .w-input[readonly], .w-select[readonly], fieldset[disabled] .w-input, fieldset[disabled] .w-select {
  cursor: not-allowed;
}

.w-input[disabled]:not(.w-input-disabled), .w-select[disabled]:not(.w-input-disabled), .w-input[readonly], .w-select[readonly], fieldset[disabled]:not(.w-input-disabled) .w-input, fieldset[disabled]:not(.w-input-disabled) .w-select {
  background-color: #eee;
}

textarea.w-input, textarea.w-select {
  height: auto;
}

.w-select {
  background-color: #f3f3f3;
}

.w-select[multiple] {
  height: auto;
}

.w-form-label {
  cursor: pointer;
  margin-bottom: 0;
  font-weight: normal;
  display: inline-block;
}

.w-radio {
  margin-bottom: 5px;
  padding-left: 20px;
  display: block;
}

.w-radio:before, .w-radio:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-radio:after {
  clear: both;
}

.w-radio-input {
  float: left;
  margin: 3px 0 0 -20px;
  line-height: normal;
}

.w-file-upload {
  margin-bottom: 10px;
  display: block;
}

.w-file-upload-input {
  opacity: 0;
  z-index: -100;
  width: .1px;
  height: .1px;
  position: absolute;
  overflow: hidden;
}

.w-file-upload-default, .w-file-upload-uploading, .w-file-upload-success {
  color: #333;
  display: inline-block;
}

.w-file-upload-error {
  margin-top: 10px;
  display: block;
}

.w-file-upload-default.w-hidden, .w-file-upload-uploading.w-hidden, .w-file-upload-error.w-hidden, .w-file-upload-success.w-hidden {
  display: none;
}

.w-file-upload-uploading-btn {
  cursor: pointer;
  background-color: #fafafa;
  border: 1px solid #ccc;
  margin: 0;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: normal;
  display: flex;
}

.w-file-upload-file {
  background-color: #fafafa;
  border: 1px solid #ccc;
  flex-grow: 1;
  justify-content: space-between;
  margin: 0;
  padding: 8px 9px 8px 11px;
  display: flex;
}

.w-file-upload-file-name {
  font-size: 14px;
  font-weight: normal;
  display: block;
}

.w-file-remove-link {
  cursor: pointer;
  width: auto;
  height: auto;
  margin-top: 3px;
  margin-left: 10px;
  padding: 3px;
  display: block;
}

.w-icon-file-upload-remove {
  margin: auto;
  font-size: 10px;
}

.w-file-upload-error-msg {
  color: #ea384c;
  padding: 2px 0;
  display: inline-block;
}

.w-file-upload-info {
  padding: 0 12px;
  line-height: 38px;
  display: inline-block;
}

.w-file-upload-label {
  cursor: pointer;
  background-color: #fafafa;
  border: 1px solid #ccc;
  margin: 0;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: normal;
  display: inline-block;
}

.w-icon-file-upload-icon, .w-icon-file-upload-uploading {
  width: 20px;
  margin-right: 8px;
  display: inline-block;
}

.w-icon-file-upload-uploading {
  height: 20px;
}

.w-container {
  max-width: 940px;
  margin-left: auto;
  margin-right: auto;
}

.w-container:before, .w-container:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-container:after {
  clear: both;
}

.w-container .w-row {
  margin-left: -10px;
  margin-right: -10px;
}

.w-row:before, .w-row:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-row:after {
  clear: both;
}

.w-row .w-row {
  margin-left: 0;
  margin-right: 0;
}

.w-col {
  float: left;
  width: 100%;
  min-height: 1px;
  padding-left: 10px;
  padding-right: 10px;
  position: relative;
}

.w-col .w-col {
  padding-left: 0;
  padding-right: 0;
}

.w-col-1 {
  width: 8.33333%;
}

.w-col-2 {
  width: 16.6667%;
}

.w-col-3 {
  width: 25%;
}

.w-col-4 {
  width: 33.3333%;
}

.w-col-5 {
  width: 41.6667%;
}

.w-col-6 {
  width: 50%;
}

.w-col-7 {
  width: 58.3333%;
}

.w-col-8 {
  width: 66.6667%;
}

.w-col-9 {
  width: 75%;
}

.w-col-10 {
  width: 83.3333%;
}

.w-col-11 {
  width: 91.6667%;
}

.w-col-12 {
  width: 100%;
}

.w-hidden-main {
  display: none !important;
}

@media screen and (max-width: 991px) {
  .w-container {
    max-width: 728px;
  }

  .w-hidden-main {
    display: inherit !important;
  }

  .w-hidden-medium {
    display: none !important;
  }

  .w-col-medium-1 {
    width: 8.33333%;
  }

  .w-col-medium-2 {
    width: 16.6667%;
  }

  .w-col-medium-3 {
    width: 25%;
  }

  .w-col-medium-4 {
    width: 33.3333%;
  }

  .w-col-medium-5 {
    width: 41.6667%;
  }

  .w-col-medium-6 {
    width: 50%;
  }

  .w-col-medium-7 {
    width: 58.3333%;
  }

  .w-col-medium-8 {
    width: 66.6667%;
  }

  .w-col-medium-9 {
    width: 75%;
  }

  .w-col-medium-10 {
    width: 83.3333%;
  }

  .w-col-medium-11 {
    width: 91.6667%;
  }

  .w-col-medium-12 {
    width: 100%;
  }

  .w-col-stack {
    width: 100%;
    left: auto;
    right: auto;
  }
}

@media screen and (max-width: 767px) {
  .w-hidden-main, .w-hidden-medium {
    display: inherit !important;
  }

  .w-hidden-small {
    display: none !important;
  }

  .w-row, .w-container .w-row {
    margin-left: 0;
    margin-right: 0;
  }

  .w-col {
    width: 100%;
    left: auto;
    right: auto;
  }

  .w-col-small-1 {
    width: 8.33333%;
  }

  .w-col-small-2 {
    width: 16.6667%;
  }

  .w-col-small-3 {
    width: 25%;
  }

  .w-col-small-4 {
    width: 33.3333%;
  }

  .w-col-small-5 {
    width: 41.6667%;
  }

  .w-col-small-6 {
    width: 50%;
  }

  .w-col-small-7 {
    width: 58.3333%;
  }

  .w-col-small-8 {
    width: 66.6667%;
  }

  .w-col-small-9 {
    width: 75%;
  }

  .w-col-small-10 {
    width: 83.3333%;
  }

  .w-col-small-11 {
    width: 91.6667%;
  }

  .w-col-small-12 {
    width: 100%;
  }
}

@media screen and (max-width: 479px) {
  .w-container {
    max-width: none;
  }

  .w-hidden-main, .w-hidden-medium, .w-hidden-small {
    display: inherit !important;
  }

  .w-hidden-tiny {
    display: none !important;
  }

  .w-col {
    width: 100%;
  }

  .w-col-tiny-1 {
    width: 8.33333%;
  }

  .w-col-tiny-2 {
    width: 16.6667%;
  }

  .w-col-tiny-3 {
    width: 25%;
  }

  .w-col-tiny-4 {
    width: 33.3333%;
  }

  .w-col-tiny-5 {
    width: 41.6667%;
  }

  .w-col-tiny-6 {
    width: 50%;
  }

  .w-col-tiny-7 {
    width: 58.3333%;
  }

  .w-col-tiny-8 {
    width: 66.6667%;
  }

  .w-col-tiny-9 {
    width: 75%;
  }

  .w-col-tiny-10 {
    width: 83.3333%;
  }

  .w-col-tiny-11 {
    width: 91.6667%;
  }

  .w-col-tiny-12 {
    width: 100%;
  }
}

.w-widget {
  position: relative;
}

.w-widget-map {
  width: 100%;
  height: 400px;
}

.w-widget-map label {
  width: auto;
  display: inline;
}

.w-widget-map img {
  max-width: inherit;
}

.w-widget-map .gm-style-iw {
  text-align: center;
}

.w-widget-map .gm-style-iw > button {
  display: none !important;
}

.w-widget-twitter {
  overflow: hidden;
}

.w-widget-twitter-count-shim {
  vertical-align: top;
  text-align: center;
  background: #fff;
  border: 1px solid #758696;
  border-radius: 3px;
  width: 28px;
  height: 20px;
  display: inline-block;
  position: relative;
}

.w-widget-twitter-count-shim * {
  pointer-events: none;
  -webkit-user-select: none;
  user-select: none;
}

.w-widget-twitter-count-shim .w-widget-twitter-count-inner {
  text-align: center;
  color: #999;
  font-family: serif;
  font-size: 15px;
  line-height: 12px;
  position: relative;
}

.w-widget-twitter-count-shim .w-widget-twitter-count-clear {
  display: block;
  position: relative;
}

.w-widget-twitter-count-shim.w--large {
  width: 36px;
  height: 28px;
}

.w-widget-twitter-count-shim.w--large .w-widget-twitter-count-inner {
  font-size: 18px;
  line-height: 18px;
}

.w-widget-twitter-count-shim:not(.w--vertical) {
  margin-left: 5px;
  margin-right: 8px;
}

.w-widget-twitter-count-shim:not(.w--vertical).w--large {
  margin-left: 6px;
}

.w-widget-twitter-count-shim:not(.w--vertical):before, .w-widget-twitter-count-shim:not(.w--vertical):after {
  content: " ";
  pointer-events: none;
  border: solid #0000;
  width: 0;
  height: 0;
  position: absolute;
  top: 50%;
  left: 0;
}

.w-widget-twitter-count-shim:not(.w--vertical):before {
  border-width: 4px;
  border-color: #75869600 #5d6c7b #75869600 #75869600;
  margin-top: -4px;
  margin-left: -9px;
}

.w-widget-twitter-count-shim:not(.w--vertical).w--large:before {
  border-width: 5px;
  margin-top: -5px;
  margin-left: -10px;
}

.w-widget-twitter-count-shim:not(.w--vertical):after {
  border-width: 4px;
  border-color: #fff0 #fff #fff0 #fff0;
  margin-top: -4px;
  margin-left: -8px;
}

.w-widget-twitter-count-shim:not(.w--vertical).w--large:after {
  border-width: 5px;
  margin-top: -5px;
  margin-left: -9px;
}

.w-widget-twitter-count-shim.w--vertical {
  width: 61px;
  height: 33px;
  margin-bottom: 8px;
}

.w-widget-twitter-count-shim.w--vertical:before, .w-widget-twitter-count-shim.w--vertical:after {
  content: " ";
  pointer-events: none;
  border: solid #0000;
  width: 0;
  height: 0;
  position: absolute;
  top: 100%;
  left: 50%;
}

.w-widget-twitter-count-shim.w--vertical:before {
  border-width: 5px;
  border-color: #5d6c7b #75869600 #75869600;
  margin-left: -5px;
}

.w-widget-twitter-count-shim.w--vertical:after {
  border-width: 4px;
  border-color: #fff #fff0 #fff0;
  margin-left: -4px;
}

.w-widget-twitter-count-shim.w--vertical .w-widget-twitter-count-inner {
  font-size: 18px;
  line-height: 22px;
}

.w-widget-twitter-count-shim.w--vertical.w--large {
  width: 76px;
}

.w-background-video {
  color: #fff;
  height: 500px;
  position: relative;
  overflow: hidden;
}

.w-background-video > video {
  object-fit: cover;
  z-index: -100;
  background-position: 50%;
  background-size: cover;
  width: 100%;
  height: 100%;
  margin: auto;
  position: absolute;
  inset: -100%;
}

.w-background-video > video::-webkit-media-controls-start-playback-button {
  -webkit-appearance: none;
  display: none !important;
}

.w-background-video--control {
  background-color: #0000;
  padding: 0;
  position: absolute;
  bottom: 1em;
  right: 1em;
}

.w-background-video--control > [hidden] {
  display: none !important;
}

.w-slider {
  text-align: center;
  clear: both;
  -webkit-tap-highlight-color: #0000;
  tap-highlight-color: #0000;
  background: #ddd;
  height: 300px;
  position: relative;
}

.w-slider-mask {
  z-index: 1;
  white-space: nowrap;
  height: 100%;
  display: block;
  position: relative;
  left: 0;
  right: 0;
  overflow: hidden;
}

.w-slide {
  vertical-align: top;
  white-space: normal;
  text-align: left;
  width: 100%;
  height: 100%;
  display: inline-block;
  position: relative;
}

.w-slider-nav {
  z-index: 2;
  text-align: center;
  -webkit-tap-highlight-color: #0000;
  tap-highlight-color: #0000;
  height: 40px;
  margin: auto;
  padding-top: 10px;
  position: absolute;
  inset: auto 0 0;
}

.w-slider-nav.w-round > div {
  border-radius: 100%;
}

.w-slider-nav.w-num > div {
  font-size: inherit;
  line-height: inherit;
  width: auto;
  height: auto;
  padding: .2em .5em;
}

.w-slider-nav.w-shadow > div {
  box-shadow: 0 0 3px #3336;
}

.w-slider-nav-invert {
  color: #fff;
}

.w-slider-nav-invert > div {
  background-color: #2226;
}

.w-slider-nav-invert > div.w-active {
  background-color: #222;
}

.w-slider-dot {
  cursor: pointer;
  background-color: #fff6;
  width: 1em;
  height: 1em;
  margin: 0 3px .5em;
  transition: background-color .1s, color .1s;
  display: inline-block;
  position: relative;
}

.w-slider-dot.w-active {
  background-color: #fff;
}

.w-slider-dot:focus {
  outline: none;
  box-shadow: 0 0 0 2px #fff;
}

.w-slider-dot:focus.w-active {
  box-shadow: none;
}

.w-slider-arrow-left, .w-slider-arrow-right {
  cursor: pointer;
  color: #fff;
  -webkit-tap-highlight-color: #0000;
  tap-highlight-color: #0000;
  -webkit-user-select: none;
  user-select: none;
  width: 80px;
  margin: auto;
  font-size: 40px;
  position: absolute;
  inset: 0;
  overflow: hidden;
}

.w-slider-arrow-left [class^="w-icon-"], .w-slider-arrow-right [class^="w-icon-"], .w-slider-arrow-left [class*=" w-icon-"], .w-slider-arrow-right [class*=" w-icon-"] {
  position: absolute;
}

.w-slider-arrow-left:focus, .w-slider-arrow-right:focus {
  outline: 0;
}

.w-slider-arrow-left {
  z-index: 3;
  right: auto;
}

.w-slider-arrow-right {
  z-index: 4;
  left: auto;
}

.w-icon-slider-left, .w-icon-slider-right {
  width: 1em;
  height: 1em;
  margin: auto;
  inset: 0;
}

.w-slider-aria-label {
  clip: rect(0 0 0 0);
  border: 0;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  position: absolute;
  overflow: hidden;
}

.w-slider-force-show {
  display: block !important;
}

.w-dropdown {
  text-align: left;
  z-index: 900;
  margin-left: auto;
  margin-right: auto;
  display: inline-block;
  position: relative;
}

.w-dropdown-btn, .w-dropdown-toggle, .w-dropdown-link {
  vertical-align: top;
  color: #222;
  text-align: left;
  white-space: nowrap;
  margin-left: auto;
  margin-right: auto;
  padding: 20px;
  text-decoration: none;
  position: relative;
}

.w-dropdown-toggle {
  -webkit-user-select: none;
  user-select: none;
  cursor: pointer;
  padding-right: 40px;
  display: inline-block;
}

.w-dropdown-toggle:focus {
  outline: 0;
}

.w-icon-dropdown-toggle {
  width: 1em;
  height: 1em;
  margin: auto 20px auto auto;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
}

.w-dropdown-list {
  background: #ddd;
  min-width: 100%;
  display: none;
  position: absolute;
}

.w-dropdown-list.w--open {
  display: block;
}

.w-dropdown-link {
  color: #222;
  padding: 10px 20px;
  display: block;
}

.w-dropdown-link.w--current {
  color: #0082f3;
}

.w-dropdown-link:focus {
  outline: 0;
}

@media screen and (max-width: 767px) {
  .w-nav-brand {
    padding-left: 10px;
  }
}

.w-lightbox-backdrop {
  cursor: auto;
  letter-spacing: normal;
  text-indent: 0;
  text-shadow: none;
  text-transform: none;
  visibility: visible;
  white-space: normal;
  word-break: normal;
  word-spacing: normal;
  word-wrap: normal;
  color: #fff;
  text-align: center;
  z-index: 2000;
  opacity: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -webkit-tap-highlight-color: transparent;
  background: #000000e6;
  outline: 0;
  font-family: Helvetica Neue, Helvetica, Ubuntu, Segoe UI, Verdana, sans-serif;
  font-size: 17px;
  font-style: normal;
  font-weight: 300;
  line-height: 1.2;
  list-style: disc;
  position: fixed;
  inset: 0;
  -webkit-transform: translate(0);
}

.w-lightbox-backdrop, .w-lightbox-container {
  -webkit-overflow-scrolling: touch;
  height: 100%;
  overflow: auto;
}

.w-lightbox-content {
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.w-lightbox-view {
  opacity: 0;
  width: 100vw;
  height: 100vh;
  position: absolute;
}

.w-lightbox-view:before {
  content: "";
  height: 100vh;
}

.w-lightbox-group, .w-lightbox-group .w-lightbox-view, .w-lightbox-group .w-lightbox-view:before {
  height: 86vh;
}

.w-lightbox-frame, .w-lightbox-view:before {
  vertical-align: middle;
  display: inline-block;
}

.w-lightbox-figure {
  margin: 0;
  position: relative;
}

.w-lightbox-group .w-lightbox-figure {
  cursor: pointer;
}

.w-lightbox-img {
  width: auto;
  max-width: none;
  height: auto;
}

.w-lightbox-image {
  float: none;
  max-width: 100vw;
  max-height: 100vh;
  display: block;
}

.w-lightbox-group .w-lightbox-image {
  max-height: 86vh;
}

.w-lightbox-caption {
  text-align: left;
  text-overflow: ellipsis;
  white-space: nowrap;
  background: #0006;
  padding: .5em 1em;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: hidden;
}

.w-lightbox-embed {
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0;
}

.w-lightbox-control {
  cursor: pointer;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 24px;
  width: 4em;
  transition: all .3s;
  position: absolute;
  top: 0;
}

.w-lightbox-left {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9Ii0yMCAwIDI0IDQwIiB3aWR0aD0iMjQiIGhlaWdodD0iNDAiPjxnIHRyYW5zZm9ybT0icm90YXRlKDQ1KSI+PHBhdGggZD0ibTAgMGg1djIzaDIzdjVoLTI4eiIgb3BhY2l0eT0iLjQiLz48cGF0aCBkPSJtMSAxaDN2MjNoMjN2M2gtMjZ6IiBmaWxsPSIjZmZmIi8+PC9nPjwvc3ZnPg==");
  display: none;
  bottom: 0;
  left: 0;
}

.w-lightbox-right {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9Ii00IDAgMjQgNDAiIHdpZHRoPSIyNCIgaGVpZ2h0PSI0MCI+PGcgdHJhbnNmb3JtPSJyb3RhdGUoNDUpIj48cGF0aCBkPSJtMC0waDI4djI4aC01di0yM2gtMjN6IiBvcGFjaXR5PSIuNCIvPjxwYXRoIGQ9Im0xIDFoMjZ2MjZoLTN2LTIzaC0yM3oiIGZpbGw9IiNmZmYiLz48L2c+PC9zdmc+");
  display: none;
  bottom: 0;
  right: 0;
}

.w-lightbox-close {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9Ii00IDAgMTggMTciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxNyI+PGcgdHJhbnNmb3JtPSJyb3RhdGUoNDUpIj48cGF0aCBkPSJtMCAwaDd2LTdoNXY3aDd2NWgtN3Y3aC01di03aC03eiIgb3BhY2l0eT0iLjQiLz48cGF0aCBkPSJtMSAxaDd2LTdoM3Y3aDd2M2gtN3Y3aC0zdi03aC03eiIgZmlsbD0iI2ZmZiIvPjwvZz48L3N2Zz4=");
  background-size: 18px;
  height: 2.6em;
  right: 0;
}

.w-lightbox-strip {
  white-space: nowrap;
  padding: 0 1vh;
  line-height: 0;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: auto hidden;
}

.w-lightbox-item {
  box-sizing: content-box;
  cursor: pointer;
  width: 10vh;
  padding: 2vh 1vh;
  display: inline-block;
  -webkit-transform: translate3d(0, 0, 0);
}

.w-lightbox-active {
  opacity: .3;
}

.w-lightbox-thumbnail {
  background: #222;
  height: 10vh;
  position: relative;
  overflow: hidden;
}

.w-lightbox-thumbnail-image {
  position: absolute;
  top: 0;
  left: 0;
}

.w-lightbox-thumbnail .w-lightbox-tall {
  width: 100%;
  top: 50%;
  transform: translate(0, -50%);
}

.w-lightbox-thumbnail .w-lightbox-wide {
  height: 100%;
  left: 50%;
  transform: translate(-50%);
}

.w-lightbox-spinner {
  box-sizing: border-box;
  border: 5px solid #0006;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin-top: -20px;
  margin-left: -20px;
  animation: .8s linear infinite spin;
  position: absolute;
  top: 50%;
  left: 50%;
}

.w-lightbox-spinner:after {
  content: "";
  border: 3px solid #0000;
  border-bottom-color: #fff;
  border-radius: 50%;
  position: absolute;
  inset: -4px;
}

.w-lightbox-hide {
  display: none;
}

.w-lightbox-noscroll {
  overflow: hidden;
}

@media (min-width: 768px) {
  .w-lightbox-content {
    height: 96vh;
    margin-top: 2vh;
  }

  .w-lightbox-view, .w-lightbox-view:before {
    height: 96vh;
  }

  .w-lightbox-group, .w-lightbox-group .w-lightbox-view, .w-lightbox-group .w-lightbox-view:before {
    height: 84vh;
  }

  .w-lightbox-image {
    max-width: 96vw;
    max-height: 96vh;
  }

  .w-lightbox-group .w-lightbox-image {
    max-width: 82.3vw;
    max-height: 84vh;
  }

  .w-lightbox-left, .w-lightbox-right {
    opacity: .5;
    display: block;
  }

  .w-lightbox-close {
    opacity: .8;
  }

  .w-lightbox-control:hover {
    opacity: 1;
  }
}

.w-lightbox-inactive, .w-lightbox-inactive:hover {
  opacity: 0;
}

.w-richtext:before, .w-richtext:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-richtext:after {
  clear: both;
}

.w-richtext[contenteditable="true"]:before, .w-richtext[contenteditable="true"]:after {
  white-space: initial;
}

.w-richtext ol, .w-richtext ul {
  overflow: hidden;
}

.w-richtext .w-richtext-figure-selected.w-richtext-figure-type-video div:after, .w-richtext .w-richtext-figure-selected[data-rt-type="video"] div:after, .w-richtext .w-richtext-figure-selected.w-richtext-figure-type-image div, .w-richtext .w-richtext-figure-selected[data-rt-type="image"] div {
  outline: 2px solid #2895f7;
}

.w-richtext figure.w-richtext-figure-type-video > div:after, .w-richtext figure[data-rt-type="video"] > div:after {
  content: "";
  display: none;
  position: absolute;
  inset: 0;
}

.w-richtext figure {
  max-width: 60%;
  position: relative;
}

.w-richtext figure > div:before {
  cursor: default !important;
}

.w-richtext figure img {
  width: 100%;
}

.w-richtext figure figcaption.w-richtext-figcaption-placeholder {
  opacity: .6;
}

.w-richtext figure div {
  color: #0000;
  font-size: 0;
}

.w-richtext figure.w-richtext-figure-type-image, .w-richtext figure[data-rt-type="image"] {
  display: table;
}

.w-richtext figure.w-richtext-figure-type-image > div, .w-richtext figure[data-rt-type="image"] > div {
  display: inline-block;
}

.w-richtext figure.w-richtext-figure-type-image > figcaption, .w-richtext figure[data-rt-type="image"] > figcaption {
  caption-side: bottom;
  display: table-caption;
}

.w-richtext figure.w-richtext-figure-type-video, .w-richtext figure[data-rt-type="video"] {
  width: 60%;
  height: 0;
}

.w-richtext figure.w-richtext-figure-type-video iframe, .w-richtext figure[data-rt-type="video"] iframe {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.w-richtext figure.w-richtext-figure-type-video > div, .w-richtext figure[data-rt-type="video"] > div {
  width: 100%;
}

.w-richtext figure.w-richtext-align-center {
  clear: both;
  margin-left: auto;
  margin-right: auto;
}

.w-richtext figure.w-richtext-align-center.w-richtext-figure-type-image > div, .w-richtext figure.w-richtext-align-center[data-rt-type="image"] > div {
  max-width: 100%;
}

.w-richtext figure.w-richtext-align-normal {
  clear: both;
}

.w-richtext figure.w-richtext-align-fullwidth {
  text-align: center;
  clear: both;
  width: 100%;
  max-width: 100%;
  margin-left: auto;
  margin-right: auto;
  display: block;
}

.w-richtext figure.w-richtext-align-fullwidth > div {
  padding-bottom: inherit;
  display: inline-block;
}

.w-richtext figure.w-richtext-align-fullwidth > figcaption {
  display: block;
}

.w-richtext figure.w-richtext-align-floatleft {
  float: left;
  clear: none;
  margin-right: 15px;
}

.w-richtext figure.w-richtext-align-floatright {
  float: right;
  clear: none;
  margin-left: 15px;
}

.w-nav {
  z-index: 1000;
  background: #ddd;
  position: relative;
}

.w-nav:before, .w-nav:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-nav:after {
  clear: both;
}

.w-nav-brand {
  float: left;
  color: #333;
  text-decoration: none;
  position: relative;
}

.w-nav-link {
  vertical-align: top;
  color: #222;
  text-align: left;
  margin-left: auto;
  margin-right: auto;
  padding: 20px;
  text-decoration: none;
  display: inline-block;
  position: relative;
}

.w-nav-link.w--current {
  color: #0082f3;
}

.w-nav-menu {
  float: right;
  position: relative;
}

[data-nav-menu-open] {
  text-align: center;
  background: #c8c8c8;
  min-width: 200px;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  overflow: visible;
  display: block !important;
}

.w--nav-link-open {
  display: block;
  position: relative;
}

.w-nav-overlay {
  width: 100%;
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  overflow: hidden;
}

.w-nav-overlay [data-nav-menu-open] {
  top: 0;
}

.w-nav[data-animation="over-left"] .w-nav-overlay {
  width: auto;
}

.w-nav[data-animation="over-left"] .w-nav-overlay, .w-nav[data-animation="over-left"] [data-nav-menu-open] {
  z-index: 1;
  top: 0;
  right: auto;
}

.w-nav[data-animation="over-right"] .w-nav-overlay {
  width: auto;
}

.w-nav[data-animation="over-right"] .w-nav-overlay, .w-nav[data-animation="over-right"] [data-nav-menu-open] {
  z-index: 1;
  top: 0;
  left: auto;
}

.w-nav-button {
  float: right;
  cursor: pointer;
  -webkit-tap-highlight-color: #0000;
  tap-highlight-color: #0000;
  -webkit-user-select: none;
  user-select: none;
  padding: 18px;
  font-size: 24px;
  display: none;
  position: relative;
}

.w-nav-button:focus {
  outline: 0;
}

.w-nav-button.w--open {
  color: #fff;
  background-color: #c8c8c8;
}

.w-nav[data-collapse="all"] .w-nav-menu {
  display: none;
}

.w-nav[data-collapse="all"] .w-nav-button, .w--nav-dropdown-open, .w--nav-dropdown-toggle-open {
  display: block;
}

.w--nav-dropdown-list-open {
  position: static;
}

@media screen and (max-width: 991px) {
  .w-nav[data-collapse="medium"] .w-nav-menu {
    display: none;
  }

  .w-nav[data-collapse="medium"] .w-nav-button {
    display: block;
  }
}

@media screen and (max-width: 767px) {
  .w-nav[data-collapse="small"] .w-nav-menu {
    display: none;
  }

  .w-nav[data-collapse="small"] .w-nav-button {
    display: block;
  }

  .w-nav-brand {
    padding-left: 10px;
  }
}

@media screen and (max-width: 479px) {
  .w-nav[data-collapse="tiny"] .w-nav-menu {
    display: none;
  }

  .w-nav[data-collapse="tiny"] .w-nav-button {
    display: block;
  }
}

.w-tabs {
  position: relative;
}

.w-tabs:before, .w-tabs:after {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-tabs:after {
  clear: both;
}

.w-tab-menu {
  position: relative;
}

.w-tab-link {
  vertical-align: top;
  text-align: left;
  cursor: pointer;
  color: #222;
  background-color: #ddd;
  padding: 9px 30px;
  text-decoration: none;
  display: inline-block;
  position: relative;
}

.w-tab-link.w--current {
  background-color: #c8c8c8;
}

.w-tab-link:focus {
  outline: 0;
}

.w-tab-content {
  display: block;
  position: relative;
  overflow: hidden;
}

.w-tab-pane {
  display: none;
  position: relative;
}

.w--tab-active {
  display: block;
}

@media screen and (max-width: 479px) {
  .w-tab-link {
    display: block;
  }
}

.w-ix-emptyfix:after {
  content: "";
}

@keyframes spin {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.w-dyn-empty {
  background-color: #ddd;
  padding: 10px;
}

.w-dyn-hide, .w-dyn-bind-empty, .w-condition-invisible {
  display: none !important;
}

.wf-layout-layout {
  display: grid;
}

:root {
  --all-color--background: #04050e;
  --_typography---body: Poppins, sans-serif;
  --all-color--white: white;
  --_typography---h6--all-paragraph--p-16: 16px;
  --_typography---all-line-height--line-height-160: 160%;
  --_typography---all-font-weight--font-weight-400: 400;
  --_typography---h1--font-size: 56px;
  --_typography---all-line-height--line-height-130: 130%;
  --_typography---all-font-weight--font-weight-600: 600;
  --_typography---h2--font-size: 48px;
  --_typography---h3--font-size: 40px;
  --_typography---all-line-height--line-height-140: 140%;
  --_typography---h4--font-size: 32px;
  --_typography---all-line-height--line-height-137: 137%;
  --_typography---h5--font-size: 24px;
  --_typography---all-line-height--line-height-150: 150%;
  --_typography---h6--font-size: 20px;
  --_typography---all-line-height--line-height-100: 100%;
  --_typography---all-font-weight--font-weight-500: 500;
  --_typography---all-font-weight--all-border--border-32: 32px;
  --all-color--transparent: transparent;
  --all-color--sea-green-20: #03f7b533;
  --_typography---all-font-weight--all-border--border-40: 40px;
  --all-color--black: black;
  --all-color--primary: #03f7b5;
  --all-color--primary-black: #080911;
  --all-color--sea-green-8: #03f7b514;
  --_typography---all-font-weight--all-border--border-999: 999px;
  --_typography---all-font-weight--all-border--border-24: 24px;
  --_typography---h6--all-paragraph--p-14: 14px;
  --all-color--gray-light: #cecfd1;
  --all-color--gray: #85868b;
  --all-color--yellow: #fbbc04;
  --_typography---h6--all-paragraph--p-12: 12px;
  --_typography---all-font-weight--all-border--border-20: 20px;
  --_typography---all-font-weight--all-border--border-12: 12px;
  --_typography---all-font-weight--all-border--border-10: 10px;
  --_typography---all-font-weight--all-border--border-16: 16px;
  --_typography---all-font-weight--all-border--border-80: 80px;
  --_typography---all-font-weight--all-border--border-60: 60px;
}

.w-layout-blockcontainer {
  max-width: 940px;
  margin-left: auto;
  margin-right: auto;
  display: block;
}

@media screen and (max-width: 991px) {
  .w-layout-blockcontainer {
    max-width: 728px;
  }
}

@media screen and (max-width: 767px) {
  .w-layout-blockcontainer {
    max-width: none;
  }
}

body {
  background-color: var(--all-color--background);
  font-family: var(--_typography---body);
  color: var(--all-color--white);
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
  text-decoration: none;
}

h1 {
  color: var(--all-color--white);
  font-size: var(--_typography---h1--font-size);
  line-height: var(--_typography---all-line-height--line-height-130);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
  margin-top: 0;
  margin-bottom: 0;
  overflow: hidden;
}

h2 {
  color: var(--all-color--white);
  font-size: var(--_typography---h2--font-size);
  line-height: var(--_typography---all-line-height--line-height-130);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
  margin-top: 0;
  margin-bottom: 0;
}

h3 {
  font-size: var(--_typography---h3--font-size);
  line-height: var(--_typography---all-line-height--line-height-140);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
  margin-top: 0;
  margin-bottom: 0;
}

h4 {
  font-size: var(--_typography---h4--font-size);
  line-height: var(--_typography---all-line-height--line-height-137);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
  margin-top: 0;
  margin-bottom: 0;
}

h5 {
  font-size: var(--_typography---h5--font-size);
  line-height: var(--_typography---all-line-height--line-height-150);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
  margin-top: 0;
  margin-bottom: 0;
}

h6 {
  font-size: var(--_typography---h6--font-size);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
  margin-top: 0;
  margin-bottom: 0;
}

p {
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
  margin-bottom: 0;
}

a {
  color: var(--all-color--white);
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-100);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
  text-decoration: none;
  display: inline-block;
}

ul {
  margin-top: 0;
  margin-bottom: 0;
  padding-left: 30px;
}

img {
  max-width: 100%;
  display: inline-block;
}

figure {
  border-radius: var(--_typography---all-font-weight--all-border--border-32);
  margin-bottom: 0;
  overflow: hidden;
}

.utility-page-form {
  flex-direction: column;
  align-items: stretch;
  display: flex;
}

.navbar {
  background-color: var(--all-color--transparent);
  padding-top: 24px;
  padding-bottom: 24px;
  position: fixed;
  inset: 0% 0% auto;
}

.container {
  max-width: 1168px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 15px;
  padding-right: 15px;
}

.navbar-wrapper {
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.nav-menu-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  border: 1px solid var(--all-color--sea-green-20);
  border-radius: var(--_typography---all-font-weight--all-border--border-40);
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
  background-color: #ffffff0a;
  justify-content: flex-start;
  align-items: center;
  padding: 11px 20px;
  display: flex;
}

.nav-single-menu {
  color: var(--all-color--white);
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
  padding: 0;
  transition: color .3s;
}

.nav-single-menu:hover, .nav-single-menu.w--current {
  color: var(--all-color--primary);
}

.nav-button-wrap {
  grid-column-gap: 15px;
  grid-row-gap: 15px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.primary-button {
  border-radius: var(--_typography---h3--font-size);
  background-color: var(--all-color--primary);
  color: var(--all-color--primary-black);
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-100);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
  justify-content: center;
  align-items: center;
  padding: 16px 24px;
  display: flex;
  position: relative;
  overflow: hidden;
}

.primary-button.white {
  border: 1px solid var(--all-color--white);
  background-color: var(--all-color--transparent);
  color: var(--all-color--white);
}

.primary-button.primary {
  border: 1px solid var(--all-color--sea-green-8);
  background-color: var(--all-color--primary-black);
  color: var(--all-color--primary);
  transition: color .4s;
}

.primary-button.primary:hover {
  color: var(--all-color--primary-black);
}

.primary-btn-wrap {
  z-index: 1;
  height: 16px;
  position: relative;
  overflow: hidden;
}

.primary-button-dot {
  border-radius: var(--_typography---all-font-weight--all-border--border-999);
  background-color: var(--all-color--white);
  width: 0;
  height: 0;
  position: absolute;
}

.primary-button-dot.primary {
  background-color: var(--all-color--primary);
}

.mobile-on {
  display: none;
}

.section {
  padding-top: 140px;
  padding-bottom: 140px;
}

.section.style {
  background-image: url("../images/68662a8d794a916bfa575380_Section%20BG.webp");
  background-position: 50% 0;
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 168px;
}

.section.footer {
  background-image: url("../images/686665ed566fbba9ad9f91a8_Footer%20BG.avif");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding-top: 72px;
  padding-bottom: 40px;
}

.section.changelog {
  padding-top: 168px;
}

.section.privacy, .section.document, .section.contact {
  background-image: url("../images/68662a8d794a916bfa575380_Section%20BG.webp");
  background-position: 50% 0;
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 168px;
}

.section._404 {
  padding-top: 168px;
}

.section.blog {
  background-image: url("../images/68662a8d794a916bfa575380_Section%20BG.webp");
  background-position: 50% 0;
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 168px;
  padding-bottom: 0;
}

.section.blog-single {
  padding-top: 140px;
  padding-bottom: 0;
}

.section.pricing {
  background-image: url("../images/68662a8d794a916bfa575380_Section%20BG.webp");
  background-position: 50% 0;
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 168px;
  padding-bottom: 0;
}

.section.faq {
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
  padding-top: 0;
}

.section.about {
  background-image: url("../images/68662a8d794a916bfa575380_Section%20BG.webp");
  background-position: 0 0;
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 168px;
  padding-bottom: 0;
}

.section.why {
  padding-bottom: 0;
}

.section.built {
  background-image: url("../images/68692bd5cd229205d9ed6cc0_Number%20BG.svg");
  background-position: 100%;
  background-repeat: no-repeat;
  background-size: contain;
}

.section.team {
  padding-top: 0;
  padding-bottom: 0;
}

.section.testimonial {
  padding-bottom: 0;
}

.section.hero {
  background-image: url("../images/68695cb4254e9c834a73bb3e_8042d6209d2d1c63c73d554ef657b53b_Hero%20Bg.webp");
  background-position: 50% 0;
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 168px;
  padding-bottom: 0;
}

.section.about-v1 {
  padding-top: 0;
  padding-bottom: 0;
}

.section.feature-v1 {
  padding-bottom: 0;
}

.section.work-v1 {
  padding-top: 0;
}

.section.advantage {
  padding-top: 0;
  padding-bottom: 0;
}

.section.blog-v1 {
  padding-bottom: 0;
}

.section.feature {
  background-image: url("../images/68662a8d794a916bfa575380_Section%20BG.webp");
  background-position: 50% 0;
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 168px;
  padding-bottom: 0;
}

.section.complex {
  padding-top: 0;
  padding-bottom: 0;
}

.section.code {
  padding-top: 0;
}

.section.benefit {
  padding-top: 0;
  padding-bottom: 0;
}

.section.hero-v2 {
  background-image: url("../images/686b7e5b416ad7fcedb2481f_Hero%20V2%20BG.webp");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  padding-top: 184px;
  padding-bottom: 0;
  overflow: hidden;
}

.section.about-v2, .section.work-v2, .section.cta-v2, .section.testimonial-v2 {
  padding-top: 0;
  padding-bottom: 0;
}

.style-guide-top-wrap {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.style-guide-wrap {
  margin-top: 112px;
}

.section-sub-title {
  color: var(--all-color--primary);
  font-size: var(--_typography---h6--all-paragraph--p-14);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
  text-align: center;
}

.style-title {
  color: var(--all-color--white);
  font-size: var(--_typography---h2--font-size);
  line-height: var(--_typography---all-line-height--line-height-130);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
  text-align: center;
  max-width: 704px;
  margin-top: 12px;
  margin-bottom: 24px;
}

.style-details {
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
  text-align: center;
  max-width: 608px;
}

.style-flex-wrap {
  grid-column-gap: 48px;
  grid-row-gap: 48px;
  flex-flow: column;
  display: flex;
}

.style-single-wrap {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-32);
  background-color: var(--all-color--primary-black);
  padding: 32px 48px 48px;
}

.style-single-title {
  color: var(--all-color--white);
  font-size: var(--_typography---h4--font-size);
  line-height: var(--_typography---all-line-height--line-height-137);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
}

.style-single-line {
  background-color: var(--all-color--sea-green-8);
  width: 100%;
  height: 1px;
  margin-top: 24px;
  margin-bottom: 40px;
}

.color-wrapper {
  grid-column-gap: 48px;
  grid-row-gap: 48px;
  flex-flow: column;
  display: flex;
}

.color-grid-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 40px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.color-title {
  font-size: var(--_typography---h6--font-size);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
}

.color-single-wrap {
  grid-column-gap: 32px;
  grid-row-gap: 32px;
  flex-flow: column;
  display: flex;
}

.color-item-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  flex-flow: column;
  display: flex;
}

.color-wrap {
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
  background-color: var(--all-color--primary);
  height: 200px;
}

.color-wrap._02 {
  border: 1px solid var(--all-color--sea-green-8);
  background-color: var(--all-color--primary-black);
}

.color-wrap._03 {
  border: 1px solid var(--all-color--sea-green-8);
  background-color: var(--all-color--background);
}

.color-wrap._04 {
  background-color: var(--all-color--white);
}

.color-wrap._05 {
  background-color: var(--all-color--gray-light);
}

.color-wrap._06 {
  background-color: var(--all-color--gray);
}

.color-wrap._07 {
  background-color: var(--all-color--sea-green-20);
}

.color-wrap._08 {
  background-color: var(--all-color--sea-green-8);
}

.color-wrap._09 {
  background-color: var(--all-color--yellow);
}

.color-details-wrap {
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.color-name {
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
}

.typography-wrapper {
  grid-column-gap: 32px;
  grid-row-gap: 32px;
  flex-flow: column;
  display: flex;
}

.typography-single-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  flex-flow: column;
  display: flex;
}

.typography-single-title {
  color: var(--all-color--gray);
  font-size: var(--_typography---h5--font-size);
  line-height: var(--_typography---all-line-height--line-height-150);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
}

.typography-flex-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  flex-flow: column;
  display: flex;
}

.typography-item-wrap {
  grid-column-gap: 12px;
  grid-row-gap: 12px;
  flex-flow: column;
  display: flex;
}

.typography-item-details {
  color: var(--all-color--gray-light);
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
}

.paragraph---1 {
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
}

.paragraph---1._02 {
  font-weight: var(--_typography---all-font-weight--font-weight-400);
}

.paragraph---2 {
  font-size: var(--_typography---h6--all-paragraph--p-14);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
}

.paragraph---2._02 {
  font-weight: var(--_typography---all-font-weight--font-weight-400);
}

.paragraph---3 {
  font-size: var(--_typography---h6--all-paragraph--p-12);
  line-height: var(--_typography---all-line-height--line-height-100);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
}

.paragraph---3._02 {
  font-weight: var(--_typography---all-font-weight--font-weight-400);
}

.all-button-wrapper {
  grid-column-gap: 48px;
  grid-row-gap: 48px;
  flex-flow: column;
  display: flex;
}

.button-single-wrap {
  grid-column-gap: 32px;
  grid-row-gap: 32px;
  flex-flow: column;
  display: flex;
}

.button-single-title {
  line-height: var(--_typography---all-line-height--line-height-100);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
}

.button-flex-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.footer-wrapper {
  overflow: hidden;
}

.footer-top-wrap {
  grid-column-gap: 358px;
  grid-row-gap: 358px;
  grid-template-rows: auto;
  grid-template-columns: 1.16fr 1fr;
  grid-auto-columns: 1fr;
  place-items: center stretch;
  display: grid;
}

.footer-wrap {
  margin-top: 80px;
  margin-bottom: 80px;
}

.footer-top-right-wrap {
  grid-column-gap: 12px;
  grid-row-gap: 12px;
  flex-flow: column;
  display: flex;
}

.footer-sub-title-wrap {
  grid-column-gap: 4px;
  grid-row-gap: 4px;
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---h3--font-size);
  justify-content: flex-start;
  align-items: center;
  padding: 7px 11px;
  display: inline-flex;
}

.footer-sub-title {
  font-size: var(--_typography---h6--all-paragraph--p-12);
  line-height: var(--_typography---all-line-height--line-height-100);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
}

.footer-title {
  font-size: var(--_typography---h4--font-size);
  line-height: var(--_typography---all-line-height--line-height-137);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
  margin-top: 12px;
  margin-bottom: 12px;
}

.footer-details {
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
}

.footer-form-block {
  margin-bottom: 0;
}

.footer-form {
  display: flex;
  position: relative;
}

.footer-email-filed {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---h3--font-size);
  background-color: var(--all-color--primary-black);
  color: var(--all-color--white);
  font-size: var(--_typography---h6--all-paragraph--p-14);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
  height: auto;
  margin-bottom: 0;
  padding: 13px 100px 13px 24px;
}

.footer-email-filed:focus {
  border-color: var(--all-color--primary);
}

.footer-email-filed::placeholder {
  color: var(--all-color--gray);
}

.footer-submit-button {
  border-radius: var(--_typography---h3--font-size);
  background-color: var(--all-color--primary);
  color: var(--all-color--primary-black);
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-100);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
  padding: 10px 16px;
  transition: all .3s;
  position: absolute;
  inset: 6px 6px 6px auto;
}

.footer-submit-button:hover {
  background-color: var(--all-color--white);
}

.footer-email-details {
  grid-column-gap: 26px;
  grid-row-gap: 26px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.email-single-details {
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.email-single-title {
  font-size: var(--_typography---h6--all-paragraph--p-14);
  line-height: var(--_typography---all-line-height--line-height-160);
}

.footer-grid-wrap {
  grid-column-gap: 108px;
  grid-row-gap: 108px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1.3fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.footer-left-wrap {
  grid-column-gap: 26px;
  grid-row-gap: 26px;
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
}

.footer-right-wrap {
  justify-content: space-between;
  align-items: flex-start;
  display: flex;
}

.footer-brand-link {
  justify-content: flex-start;
  align-items: flex-start;
  display: inline-flex;
}

.footer-brand-details {
  color: var(--all-color--gray-light);
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
}

.footer-single-menu-wrap {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  flex-flow: column;
  display: flex;
}

.footer-single-tilte {
  font-size: var(--_typography---h6--font-size);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
}

.footer-all-menu-wrap {
  grid-column-gap: 12px;
  grid-row-gap: 12px;
  flex-flow: column;
  display: flex;
}

.footer-single-link {
  color: var(--all-color--gray-light);
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
}

.footer-single-link-wrap {
  height: 26px;
  overflow: hidden;
}

.copyright-wrapper {
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.copyright-text {
  color: var(--all-color--white);
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
}

.all-social-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.license-wrap {
  margin-top: 32px;
}

.license-date {
  color: var(--all-color--gray);
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-100);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
  margin-top: 10px;
  margin-bottom: 24px;
}

.license-details {
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
  max-width: 850px;
}

.license-flex-wrap {
  grid-column-gap: 40px;
  grid-row-gap: 40px;
  flex-flow: column;
  display: flex;
}

.license-single-wrap {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
  background-color: var(--all-color--primary-black);
  padding: 24px 32px 32px;
}

.license-single-tilte {
  font-size: var(--_typography---h4--font-size);
  line-height: var(--_typography---all-line-height--line-height-137);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
}

.license-single-line {
  background-color: var(--all-color--sea-green-8);
  width: 100%;
  height: 1px;
  margin-top: 24px;
  margin-bottom: 24px;
}

.license-image-details {
  color: var(--all-color--gray-light);
}

.license-image-list {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  flex-flow: column;
  margin-top: 16px;
  margin-bottom: 40px;
  padding-left: 20px;
  display: flex;
}

.gray-color {
  color: var(--all-color--gray-light);
}

.license-image-grid {
  grid-column-gap: 12px;
  grid-row-gap: 12px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.license-single-image {
  border-radius: var(--_typography---all-font-weight--all-border--border-20);
}

.license-icon-grid-wrap {
  grid-column-gap: 12px;
  grid-row-gap: 12px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.license-single-icon-wrap {
  grid-column-gap: 10px;
  grid-row-gap: 10px;
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-20);
  background-color: var(--all-color--primary-black);
  flex-flow: column;
  justify-content: center;
  align-items: center;
  height: 266px;
  padding-top: 18px;
  padding-bottom: 18px;
  display: flex;
}

.license-font-wrap {
  grid-column-gap: 12px;
  grid-row-gap: 12px;
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-20);
  background-color: var(--all-color--primary-black);
  flex-flow: column;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 32px 48px;
  display: flex;
}

.license-font {
  font-size: 120px;
  line-height: 80%;
}

.license-font-flex-wrap {
  grid-column-gap: 12px;
  grid-row-gap: 12px;
  flex-flow: wrap;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.license-font-mood {
  border-radius: var(--_typography---all-font-weight--all-border--border-40);
  background-color: var(--all-color--sea-green-8);
  color: var(--all-color--primary);
  padding: 7px 24px;
  display: inline-block;
}

.changelog-top-wrap {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.changelog-wrap {
  grid-column-gap: 48px;
  grid-row-gap: 48px;
  flex-flow: column;
  max-width: 1060px;
  margin-top: 112px;
  display: flex;
}

.changelog-title {
  text-align: center;
  margin-top: 12px;
  margin-bottom: 24px;
}

.changelog-details {
  text-align: center;
  max-width: 570px;
}

.changelog-grid-wrap {
  grid-column-gap: 48px;
  grid-row-gap: 48px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 4.08fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.chagnelog-single-title {
  font-size: var(--_typography---h5--font-size);
  line-height: var(--_typography---all-line-height--line-height-150);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
}

.chagnelog-single-details {
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
  margin-top: 16px;
  margin-bottom: 24px;
}

.changelog-list-title {
  font-size: var(--_typography---h6--font-size);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
  margin-bottom: 20px;
}

.changelog-list {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  color: var(--all-color--gray-light);
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
  flex-flow: column;
  padding-left: 20px;
  display: flex;
}

.chagnelog-image {
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
  margin-top: 32px;
  margin-bottom: 32px;
}

.changelog-date-wrap {
  justify-content: space-between;
  align-items: flex-start;
  height: 100%;
  display: flex;
}

.changelog-date {
  line-height: var(--_typography---all-line-height--line-height-100);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
}

.changelog-line {
  border-radius: var(--_typography---all-font-weight--all-border--border-12);
  background-color: var(--all-color--sea-green-8);
  width: 5px;
  height: 100%;
}

.chagnelog-color {
  border-radius: var(--_typography---all-font-weight--all-border--border-12);
  background-color: var(--all-color--primary);
  width: 5px;
  height: 10%;
  overflow: hidden;
}

.privacy-wrapper {
  max-width: 904px;
  margin-left: auto;
  margin-right: auto;
}

.privacy-wrap {
  margin-top: 112px;
}

.privacy-title {
  text-align: center;
  margin-top: 12px;
  margin-bottom: 24px;
}

.privacy-date {
  color: var(--all-color--gray-light);
  text-align: center;
}

.privacy-flex-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  flex-flow: column;
  margin-top: 48px;
  display: flex;
}

.privacy-details {
  color: var(--all-color--gray-light);
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
}

.privacy-single-wrap {
  grid-column-gap: 20px;
  grid-row-gap: 20px;
  flex-flow: column;
  display: flex;
}

.privacy-single-title {
  font-size: var(--_typography---h5--font-size);
  line-height: var(--_typography---all-line-height--line-height-150);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
}

.privacy-single-list-title {
  font-size: var(--_typography---h6--font-size);
  line-height: var(--_typography---all-line-height--line-height-150);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
}

.privacy-single-list {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  color: var(--all-color--white);
  flex-flow: column;
  padding-left: 20px;
  display: flex;
}

.privacy-single-list-text {
  color: var(--all-color--gray-light);
}

.document-top-wrap {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.document-wrap {
  margin-top: 112px;
}

.document-title {
  text-align: center;
  margin-top: 12px;
  margin-bottom: 24px;
}

.document-details {
  text-align: center;
  max-width: 536px;
}

.document-tabs {
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  max-width: 100%;
  display: flex;
}

.document-tabs-menu {
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-20);
  background-color: var(--all-color--background);
  flex-flow: column;
  width: 100%;
  max-width: 260px;
  padding: 24px;
  display: flex;
  position: sticky;
  top: 140px;
}

.document-tabs-content {
  width: 100%;
  max-width: 828px;
  margin-left: 48px;
}

.document-tab-link {
  background-color: var(--all-color--transparent);
  color: var(--all-color--gray-light);
  font-size: var(--_typography---h6--all-paragraph--p-14);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
  padding: 8px 16px;
}

.document-tab-link.w--current {
  border-radius: var(--_typography---all-font-weight--all-border--border-12);
  background-color: var(--all-color--sea-green-8);
  color: var(--all-color--primary);
}

.document-single-sub-title {
  color: var(--all-color--gray);
}

.document-single-title {
  font-size: var(--_typography---h5--font-size);
  line-height: var(--_typography---all-line-height--line-height-150);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
  margin-top: 12px;
  margin-bottom: 16px;
}

.document-single-details {
  color: var(--all-color--gray);
}

.document-single-image {
  border-radius: var(--_typography---all-font-weight--all-border--border-20);
  margin-top: 32px;
}

.document-list {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  color: var(--all-color--gray);
  line-height: var(--_typography---all-line-height--line-height-160);
  flex-flow: column;
  margin-top: 12px;
  padding-left: 20px;
  display: flex;
}

.contact-top-wrap {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.contact-wrap {
  margin-top: 112px;
}

.contact-title {
  text-align: center;
  margin-top: 12px;
  margin-bottom: 24px;
}

.contact-details {
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-160);
  text-align: center;
  max-width: 570px;
}

.contact-grid-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  grid-template-rows: auto;
  grid-template-columns: 1.78fr 1fr;
  grid-auto-columns: 1fr;
  place-items: start stretch;
  display: grid;
}

.contact-left-wrap {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
  background-color: var(--all-color--primary-black);
  padding: 54px 32px;
  overflow: hidden;
}

.contact-right-wrap {
  border: 1px solid var(--all-color--sea-green-20);
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
  background-color: var(--all-color--sea-green-8);
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 40px;
  display: flex;
}

.contact-text-field {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-10);
  background-color: var(--all-color--primary-black);
  color: var(--all-color--black);
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
  height: auto;
  margin-bottom: 0;
  padding: 12px 16px;
}

.contact-text-field:focus {
  border-color: var(--all-color--primary);
}

.contact-text-field::placeholder {
  color: var(--all-color--gray);
}

.contact-filed-wrap {
  grid-column-gap: 12px;
  grid-row-gap: 12px;
  flex-flow: column;
  width: 100%;
  display: flex;
}

.success-message {
  border-radius: var(--_typography---all-font-weight--all-border--border-10);
  background-color: var(--all-color--primary);
  color: var(--all-color--black);
  width: 100%;
  padding: 20px;
}

.contact-massage-box {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-16);
  background-color: var(--all-color--primary-black);
  color: var(--all-color--white);
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
  width: 100%;
  min-width: 100%;
  max-width: 100%;
  height: 92px;
  min-height: 92px;
  margin-bottom: 0;
  padding: 12px 16px;
}

.contact-massage-box:focus {
  border-color: var(--all-color--primary);
}

.contact-massage-box::placeholder {
  color: var(--all-color--gray);
}

.error-message {
  color: #fff;
  text-align: center;
  background-color: red;
  border-radius: 7px;
  width: 100%;
  margin-top: 15px;
  padding: 15px;
}

.contact-submit-button {
  border-radius: var(--_typography---all-font-weight--all-border--border-80);
  background-color: var(--all-color--primary);
  color: var(--all-color--black);
  line-height: var(--_typography---all-line-height--line-height-100);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
  margin-top: 48px;
  padding: 16px 32px;
  transition: all .3s;
}

.contact-submit-button:hover {
  background-color: var(--all-color--white);
}

.contact-form-block {
  width: 100%;
  margin-bottom: 0;
  overflow: hidden;
}

.contact-form {
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  display: flex;
}

.contact-filed-label {
  color: var(--all-color--white);
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-150);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
  margin-bottom: 0;
}

.contact-filed-wrapper {
  grid-column-gap: 40px;
  grid-row-gap: 40px;
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.contact-all-wrap {
  grid-column-gap: 20px;
  grid-row-gap: 20px;
  flex-flow: column;
  width: 100%;
  display: flex;
}

.contact-single-title {
  font-size: var(--_typography---h5--font-size);
  line-height: var(--_typography---all-line-height--line-height-130);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
}

.contact-single-details {
  color: var(--all-color--gray-light);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
  margin-top: 28px;
}

.contact-all-details-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  flex-flow: column;
  margin-top: 40px;
  margin-bottom: 40px;
  display: flex;
}

.contact-single-wrap {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.contact-single-icon-wrap {
  border-radius: var(--_typography---all-font-weight--all-border--border-60);
  background-color: var(--all-color--sea-green-8);
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  display: flex;
}

.contact-single-link {
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
}

.contact-single-link._01 {
  max-width: 145px;
}

.contact-social-wrapper {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.contact-social-link {
  border-radius: var(--_typography---all-font-weight--all-border--border-60);
  background-color: var(--all-color--primary);
  justify-content: center;
  align-items: center;
  width: 32px;
  height: 32px;
  display: flex;
  position: relative;
  overflow: hidden;
}

.contact-social-image {
  z-index: 1;
  position: relative;
}

.contact-social-bg {
  border-radius: var(--_typography---all-font-weight--all-border--border-60);
  background-color: var(--all-color--white);
  width: 40px;
  height: 40px;
  position: absolute;
}

._404-wrapper {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
  padding-bottom: 80px;
}

._404-wrap {
  flex-flow: column;
  justify-content: center;
  align-items: center;
  margin-top: 80px;
  display: flex;
}

._404-single-title {
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
  text-align: center;
  padding-top: 24px;
  padding-bottom: 24px;
}

._404-line {
  background-color: var(--all-color--sea-green-8);
  width: 100%;
  height: 1px;
}

._404-title {
  font-size: var(--_typography---h3--font-size);
  line-height: var(--_typography---all-line-height--line-height-130);
  margin-top: 32px;
  margin-bottom: 12px;
}

._404-details {
  color: var(--all-color--gray-light);
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
  text-align: center;
  max-width: 400px;
}

._404-button-wrap {
  margin-top: 32px;
}

.blog-top-wrap {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.blog-wrap {
  margin-top: 112px;
}

.blog-title {
  text-align: center;
  max-width: 704px;
  margin-top: 12px;
  margin-bottom: 24px;
}

.blog-details {
  text-align: center;
  max-width: 536px;
}

.blog-grid-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 48px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.blog-card-wrap {
  justify-content: flex-start;
  align-items: flex-start;
}

.blog-image-wrap {
  border-radius: var(--_typography---all-font-weight--all-border--border-16);
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
  overflow: hidden;
}

.blog-author-wrapper {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  justify-content: flex-start;
  align-items: center;
  margin-top: 24px;
  margin-bottom: 20px;
  display: flex;
}

.blog-author-wrapper._01 {
  margin-top: 0;
  margin-bottom: 0;
}

.blog-author-wrapper._02 {
  margin-bottom: 16px;
}

.blog-author-wrap {
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.blog-author-name {
  font-size: var(--_typography---h6--all-paragraph--p-14);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
}

.blog-calender-wrap {
  grid-column-gap: 5px;
  grid-row-gap: 5px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.blog-card-link {
  font-size: var(--_typography---h6--font-size);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
  transition: all .3s;
}

.blog-card-link:hover {
  color: var(--all-color--primary);
}

.cta-wrapper {
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
  background-image: url("../images/686749ac34a811bf70d39c19_CTA.webp");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  border: 1px solid #03f7b51f;
  padding: 65px 56px 56px;
}

.cta-wrap {
  grid-column-gap: 118px;
  grid-row-gap: 118px;
  grid-template-rows: auto;
  grid-template-columns: 1.18fr 1fr;
  grid-auto-columns: 1fr;
  place-items: center stretch;
  display: grid;
}

.cta-title {
  font-size: var(--_typography---h4--font-size);
  line-height: var(--_typography---all-line-height--line-height-137);
}

.cta-details {
  margin-top: 24px;
  margin-bottom: 48px;
}

.cta-button-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.cta-image-top-wrap {
  justify-content: flex-end;
  align-items: center;
  margin-right: 50px;
  display: flex;
}

.cta-image-wrap {
  justify-content: flex-start;
  align-items: flex-start;
  margin-top: 20px;
  display: flex;
}

.cta-image {
  border-radius: var(--_typography---all-font-weight--all-border--border-16);
}

.cta-image._02 {
  margin-top: 50px;
  margin-left: -40px;
}

.cta-image-desktop {
  display: none;
}

.blog-single-page-wrap {
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.blog-page-link {
  color: var(--all-color--gray);
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
}

.blog-page-link.w--current {
  color: var(--all-color--white);
}

.blog-single-title {
  margin-top: 16px;
  margin-bottom: 16px;
}

.blog-thimble-image {
  border-radius: var(--_typography---h4--font-size);
  object-fit: cover;
  width: 100%;
  height: 556px;
  margin-top: 40px;
}

.blog-single-grid-wrap {
  grid-column-gap: 56px;
  grid-row-gap: 56px;
  grid-template-rows: auto;
  grid-template-columns: 2.86fr 1fr;
  grid-auto-columns: 1fr;
  place-items: start stretch;
  display: grid;
}

.blog-single-right-wrap {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  flex-flow: column;
  margin-top: 40px;
  display: flex;
  position: sticky;
  top: 140px;
}

.blog-rich-text h4 {
  font-size: var(--_typography---h4--font-size);
  line-height: var(--_typography---all-line-height--line-height-137);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
  margin-top: 32px;
}

.blog-rich-text p {
  margin-top: 16px;
}

.blog-rich-text ul {
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  flex-flow: column;
  margin-top: 16px;
  padding-left: 20px;
  display: flex;
}

.blog-rich-text figure {
  margin-top: 32px;
}

.content-wrap {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-20);
  background-color: var(--all-color--background);
  flex-flow: column;
  padding: 24px;
  display: flex;
}

.content-title {
  font-size: var(--_typography---h6--font-size);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
}

.content-item-warp {
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  flex-flow: column;
  display: flex;
}

.content-item-link {
  border-radius: var(--_typography---all-font-weight--all-border--border-12);
  background-color: var(--all-color--transparent);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
  padding: 8px 16px;
  transition: all .3s;
}

.content-item-link:hover {
  background-color: var(--all-color--sea-green-8);
  color: var(--all-color--primary);
}

.blog-form-wrapper {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-16);
  background-color: var(--all-color--sea-green-8);
  padding: 24px;
}

.blog-form-title {
  font-size: var(--_typography---h6--font-size);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
}

.blog-form-details {
  color: var(--all-color--gray-light);
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-150);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
  margin-top: 16px;
  margin-bottom: 24px;
}

.blog-form-wrap {
  grid-column-gap: 12px;
  grid-row-gap: 12px;
  flex-flow: column;
  display: flex;
}

.blog-email-field {
  border: 1px solid var(--all-color--sea-green-20);
  border-radius: var(--_typography---all-font-weight--all-border--border-40);
  background-color: var(--all-color--transparent);
  color: var(--all-color--white);
  font-size: var(--_typography---h6--all-paragraph--p-14);
  line-height: var(--_typography---all-line-height--line-height-150);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
  background-image: url("../images/686757b59de39f37509a6518_Email%20Icon.svg");
  background-position: 14px;
  background-repeat: no-repeat;
  background-size: auto;
  height: auto;
  margin-bottom: 0;
  padding: 12px 16px 12px 37px;
}

.blog-email-field:focus {
  border-color: var(--all-color--primary);
}

.blog-email-field::placeholder {
  color: var(--all-color--gray);
}

.blog-submit-btn {
  border-radius: var(--_typography---all-font-weight--all-border--border-60);
  background-color: var(--all-color--primary);
  color: var(--all-color--primary-black);
  line-height: var(--_typography---all-line-height--line-height-100);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
  padding-top: 13px;
  padding-bottom: 13px;
  transition: all .3s;
}

.blog-submit-btn:hover {
  background-color: var(--all-color--white);
}

.more-blog-wrap {
  margin-top: 60px;
}

.more-blog-grid-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 48px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.pricing-top-wrap {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.pricing-wrap {
  margin-top: 112px;
  margin-bottom: 140px;
}

.pricing-title {
  text-align: center;
  max-width: 590px;
  margin-top: 12px;
  margin-bottom: 24px;
}

.pricing-details {
  text-align: center;
  max-width: 542px;
}

.pricing-tabs {
  text-align: center;
}

.pricing-tab-menu {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-40);
  background-color: var(--all-color--primary-black);
  justify-content: flex-start;
  align-items: center;
  padding: 4px;
  display: inline-flex;
}

.pricing-tab-link {
  border-radius: var(--_typography---all-font-weight--all-border--border-40);
  background-color: var(--all-color--transparent);
  color: var(--all-color--white);
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
  padding: 8px 16px;
}

.pricing-tab-link.w--current {
  background-color: var(--all-color--sea-green-20);
  color: var(--all-color--primary);
}

.pricing-tab-content {
  text-align: left;
  margin-top: 56px;
}

.pricing-grid-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.pricing-single-wrap {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
  background-color: var(--all-color--primary-black);
  padding: 32px;
}

.pricing-single-wrap.popular {
  border-color: var(--all-color--sea-green-20);
  background-color: var(--all-color--sea-green-8);
}

.price-title-wrap {
  grid-column-gap: 10px;
  grid-row-gap: 10px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.price-single-title {
  font-size: var(--_typography---h5--font-size);
  line-height: var(--_typography---all-line-height--line-height-150);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
}

.price-single-title._01 {
  font-size: var(--_typography---h6--font-size);
}

.pricing-single-details {
  font-size: var(--_typography---h6--all-paragraph--p-14);
  line-height: var(--_typography---all-line-height--line-height-160);
  margin-top: 16px;
}

.pricing-price-wrap {
  justify-content: flex-start;
  align-items: center;
  margin-top: 47px;
  margin-bottom: 47px;
  display: flex;
}

.pricing-price-wrap._01 {
  margin-top: 12px;
  margin-bottom: 24px;
}

.pricing-price-month {
  color: var(--all-color--gray);
  font-size: var(--_typography---h6--font-size);
  line-height: var(--_typography---all-line-height--line-height-150);
  letter-spacing: -.2px;
}

.pricing-list-wrapper {
  grid-column-gap: 12px;
  grid-row-gap: 12px;
  flex-flow: column;
  margin-bottom: 47px;
  display: flex;
}

.pricing-list-wrap {
  grid-column-gap: 10px;
  grid-row-gap: 10px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.pricing-list-text {
  color: var(--all-color--gray-light);
}

.popular-text {
  border: 1px solid var(--all-color--primary);
  border-radius: var(--_typography---all-font-weight--all-border--border-60);
  background-color: var(--all-color--sea-green-20);
  color: var(--all-color--primary);
  font-size: var(--_typography---h6--all-paragraph--p-12);
  line-height: var(--_typography---all-line-height--line-height-100);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
  padding: 6px 12px;
}

.compare-wrap {
  margin-top: 56px;
}

.compare-title {
  text-align: center;
}

.compar-top-wrap {
  grid-column-gap: 64px;
  grid-row-gap: 64px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.compare-details-wrap {
  border-radius: var(--_typography---all-font-weight--all-border--border-16);
  margin-top: 56px;
  overflow: hidden;
}

.compare-details {
  background-color: var(--all-color--primary-black);
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.compare-details._02 {
  background-color: var(--all-color--background);
}

.compare-single-wrap {
  border: 1px solid var(--all-color--sea-green-8);
  padding: 15px 32px;
}

.compare-single-wrap.right {
  border-left-style: none;
}

.compare-single-wrap.right.top._02 {
  border-bottom-right-radius: 16px;
}

.compare-single-wrap.right._03 {
  border-top-right-radius: 16px;
}

.compare-single-wrap.top {
  border-top-style: none;
}

.compare-single-wrap.top._01 {
  border-bottom-left-radius: 16px;
}

.compare-single-wrap._04 {
  border-top-left-radius: 16px;
}

.compare-single-title {
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-100);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
}

.compare-single-details {
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-160);
}

.faq-grid-wrap {
  grid-column-gap: 48px;
  grid-row-gap: 48px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  place-items: start stretch;
  display: grid;
}

.faq-left-wrap {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
  background-color: var(--all-color--primary-black);
  overflow: hidden;
  box-shadow: 0 2px 4px 2px #0000000f;
}

.faq-right-wrap {
  grid-column-gap: 48px;
  grid-row-gap: 48px;
  flex-flow: column;
  display: flex;
}

.faq-btm-wrap {
  padding: 24px 32px 32px;
}

.faq-title {
  font-size: var(--_typography---h4--font-size);
  line-height: var(--_typography---all-line-height--line-height-137);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
}

.faq-details {
  font-size: var(--_typography---h6--all-paragraph--p-16);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
  margin-top: 20px;
  margin-bottom: 32px;
}

.faq-btn-wrap {
  display: flex;
}

.faq-left-top-wrap {
  grid-column-gap: 12px;
  grid-row-gap: 12px;
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
}

.faq-tabs-menu {
  flex-flow: column;
  display: flex;
}

.display-none {
  display: none;
}

.faq-single-link {
  background-color: var(--all-color--transparent);
  padding: 0;
}

.faq-single-link.w--current {
  background-color: var(--all-color--transparent);
}

.faq-single-wrap {
  border-bottom: 1px solid var(--all-color--sea-green-8);
  padding-top: 20px;
  padding-bottom: 20px;
}

.faq-question-wrap {
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.faq-answer-wrap {
  overflow: hidden;
}

.faq-answer {
  color: var(--all-color--gray-light);
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
  margin-top: 20px;
}

.faq-question {
  color: var(--all-color--white);
  font-size: var(--_typography---h6--font-size);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
}

.faq-arrow-wrap {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-60);
  background-color: var(--all-color--primary-black);
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  display: flex;
}

.about-top-wrap {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.about-wrap {
  margin-top: 140px;
}

.about-title {
  text-align: center;
  max-width: 704px;
  margin-top: 12px;
  margin-bottom: 24px;
}

.about-details {
  text-align: center;
  max-width: 608px;
}

.about-image-grid-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  margin-top: 108px;
  margin-bottom: 48px;
  display: grid;
}

.about-image {
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
}

.story-top-wrap {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
}

.story-wrap {
  margin-top: 48px;
}

.story-details {
  font-size: var(--_typography---h5--font-size);
  line-height: var(--_typography---all-line-height--line-height-150);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
  text-align: left;
  display: none;
}

.dark-gray {
  color: var(--all-color--gray);
}

.story-grid-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.story-single-wrap {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---h5--font-size);
  background-color: var(--all-color--primary-black);
  padding: 32px;
  transition: all .3s;
}

.story-single-wrap:hover {
  border-color: var(--all-color--primary);
}

.story-icon-wrap {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-12);
  background-color: var(--all-color--sea-green-8);
  justify-content: center;
  align-items: center;
  width: 56px;
  height: 56px;
  display: flex;
}

.story-single-title {
  font-size: var(--_typography---h6--font-size);
  line-height: var(--_typography---all-line-height--line-height-130);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
  margin-top: 32px;
  margin-bottom: 16px;
}

.story-single-details {
  color: var(--all-color--gray-light);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
}

.about-content-wrap {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---h5--font-size);
  background-image: url("../images/68691adbb02d79db5ca9c509_About%20BG.webp");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  padding: 80px 90px;
}

.about-content-title {
  font-size: var(--_typography---h5--font-size);
  line-height: var(--_typography---all-line-height--line-height-150);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
  text-align: center;
  display: none;
}

.about-content-btn-wrap {
  justify-content: center;
  align-items: center;
  display: flex;
}

.about-stat-wrap {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  flex-flow: wrap;
  justify-content: center;
  align-items: flex-start;
  max-width: 560px;
  margin-top: 56px;
  margin-left: auto;
  margin-right: auto;
  display: flex;
}

.about-ctg-wrap {
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-80);
  background-color: var(--all-color--primary-black);
  justify-content: flex-start;
  align-items: center;
  padding: 6px 16px 6px 6px;
  display: inline-flex;
}

.star-icon-wrap {
  border-radius: var(--_typography---all-font-weight--all-border--border-60);
  background-color: #0c0e17;
  justify-content: center;
  align-items: center;
  width: 28px;
  height: 28px;
  display: flex;
}

.star-icon-text {
  font-size: var(--_typography---h6--all-paragraph--p-14);
  line-height: var(--_typography---all-line-height--line-height-160);
}

.why-top-wrap {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.why-wrap {
  margin-top: 56px;
}

.why-title {
  font-size: var(--_typography---h4--font-size);
  line-height: var(--_typography---all-line-height--line-height-137);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
  text-align: center;
  margin-top: 12px;
  margin-bottom: 16px;
}

.why-details {
  color: var(--all-color--gray-light);
  text-align: center;
  max-width: 578px;
}

.why-grid-wrap {
  grid-column-gap: 40px;
  grid-row-gap: 40px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.why-single-wrap {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-20);
  background-color: var(--all-color--primary-black);
  padding: 32px;
  transition: all .3s;
}

.why-single-wrap:hover {
  border-color: var(--all-color--primary);
}

.why-icon-wrap {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-12);
  background-color: var(--all-color--sea-green-20);
  justify-content: center;
  align-items: center;
  width: 56px;
  height: 56px;
  display: flex;
}

.why-single-title {
  font-size: var(--_typography---h6--font-size);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
  margin-top: 56px;
  margin-bottom: 16px;
}

.why-btm-wrap {
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  justify-content: flex-start;
  align-items: center;
  margin-top: 56px;
  display: flex;
}

.why-btm-text {
  font-size: var(--_typography---h6--all-paragraph--p-14);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
}

.built-grid-wrap {
  grid-column-gap: 150px;
  grid-row-gap: 150px;
  grid-template-rows: auto;
  grid-template-columns: 1.06fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.built-left-wrap {
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
}

.built-title {
  font-size: var(--_typography---h4--font-size);
  line-height: var(--_typography---all-line-height--line-height-137);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
  margin-top: 16px;
  margin-bottom: 32px;
}

.built-user-wrap {
  grid-column-gap: 12px;
  grid-row-gap: 12px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.built-use-image {
  width: 100%;
  max-width: 130px;
}

.built-right-grid-wrap {
  grid-column-gap: 84px;
  grid-row-gap: 32px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.built-single-wrap {
  grid-column-gap: 4px;
  grid-row-gap: 4px;
  flex-flow: column;
  display: flex;
}

.built-single-item-wrap {
  color: var(--all-color--primary);
}

.built-single-item-wrap.white {
  color: var(--all-color--white);
}

.built-single-overflow-wrap {
  height: 56px;
  overflow: hidden;
}

.built-single-all-wrap {
  display: flex;
}

.number {
  text-transform: uppercase;
}

.team-top-wrap {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.team-wrap {
  grid-column-gap: 32px;
  grid-row-gap: 32px;
  flex-flow: column;
  margin-top: 56px;
  display: flex;
}

.team-title {
  text-align: center;
  margin-top: 12px;
  margin-bottom: 16px;
}

.team-details {
  color: var(--all-color--gray-light);
  text-align: center;
  max-width: 578px;
}

.taem-top-grid-wrap {
  grid-column-gap: 20px;
  grid-row-gap: 20px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.team-top-single-wrap {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-20);
  background-color: var(--all-color--primary-black);
  overflow: hidden;
}

.team-top-single-image {
  transition: all .3s;
}

.team-top-single-image:hover {
  filter: grayscale();
}

.team-top-btm-wrap {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  padding-top: 16px;
  padding-bottom: 24px;
  display: flex;
}

.team-member-name {
  font-size: var(--_typography---h6--font-size);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
  text-align: center;
}

.team-single-details {
  color: var(--all-color--gray-light);
  text-align: center;
  margin-top: 8px;
  margin-bottom: 24px;
}

.team-all-link-wrap {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  justify-content: center;
  align-items: center;
  display: flex;
}

.team-social-link {
  border-radius: var(--_typography---all-font-weight--all-border--border-60);
  background-color: var(--all-color--sea-green-8);
  justify-content: center;
  align-items: center;
  width: 28px;
  height: 28px;
  display: flex;
}

.taem-btm-grid-wrap {
  grid-column-gap: 20px;
  grid-row-gap: 20px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.team-single-details-btm {
  color: var(--all-color--gray-light);
  text-align: center;
  margin-top: 4px;
  margin-bottom: 16px;
}

.team-top-btm-wrap-2 {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  padding-top: 16px;
  padding-bottom: 16px;
  display: flex;
}

.testimonial-top-wrap {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.testimonial-wrap {
  margin-top: 56px;
}

.testimonial-title {
  font-size: var(--_typography---h4--font-size);
  line-height: var(--_typography---all-line-height--line-height-137);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
  text-align: center;
  margin-top: 12px;
  margin-bottom: 16px;
}

.testimonial-details {
  text-align: center;
  max-width: 440px;
}

.testimonial-all-wrap {
  border-radius: var(--_typography---all-font-weight--all-border--border-20);
  background-image: url("../images/686934381111dde8036b46a8_Testimonail%20BG.webp");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  padding: 116px 118px 72px;
}

.testimonial-tabs-menu {
  grid-column-gap: 15px;
  grid-row-gap: 15px;
  flex-flow: wrap;
  justify-content: center;
  align-items: center;
  margin-top: 56px;
  display: flex;
}

.testimonial-single-wrap {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.testimonial-single-title {
  color: #fffc;
  font-size: var(--_typography---h6--font-size);
  line-height: var(--_typography---all-line-height--line-height-130);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
  text-align: center;
}

.testimonial-star {
  margin-top: 32px;
  margin-bottom: 16px;
}

.testimonial-tabs-link {
  grid-column-gap: 10px;
  grid-row-gap: 10px;
  border: 1px solid var(--all-color--sea-green-20);
  border-radius: var(--_typography---all-font-weight--all-border--border-999);
  background-color: var(--all-color--transparent);
  justify-content: flex-start;
  align-items: center;
  padding: 4px 24px 4px 4px;
}

.testimonial-tabs-link.w--current {
  border-color: var(--all-color--primary);
  background-color: var(--all-color--transparent);
}

.testimonial-author-details {
  color: var(--all-color--white);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
}

.testimonial-link-wrap {
  grid-column-gap: 10px;
  grid-row-gap: 10px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.hero-top-wrap {
  z-index: 1;
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
  position: relative;
}

.hero-wrap {
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
  justify-content: center;
  align-items: center;
  margin-top: 72px;
  display: flex;
  position: relative;
}

.hero-sub-title-wrap {
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-40);
  background-color: #ffffff0a;
  justify-content: center;
  align-items: center;
  padding: 2px 16px 2px 2px;
  display: inline-flex;
}

.hero-sub-title {
  font-size: var(--_typography---h6--all-paragraph--p-14);
}

.hero-sub-new {
  border-radius: var(--_typography---all-font-weight--all-border--border-40);
  background-color: var(--all-color--primary);
  color: var(--all-color--black);
  font-size: var(--_typography---h6--all-paragraph--p-12);
  line-height: var(--_typography---all-line-height--line-height-100);
  padding: 6px 10px;
}

.hero-title {
  text-align: center;
  max-width: 704px;
  margin-top: 12px;
  margin-bottom: 24px;
}

.hero-details {
  text-align: center;
  max-width: 440px;
}

.hero-btn-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  justify-content: center;
  align-items: center;
  margin-top: 48px;
  display: flex;
}

.hero-dashboar-image {
  z-index: 1;
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
  width: 100%;
  position: relative;
}

.hero-dashboard-bg {
  width: 100%;
  max-width: 1200px;
  position: absolute;
  inset: -20% 0% auto 50%;
  transform: translate(-50%);
}

.tricker-top-wrap {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.tricker-wrap {
  margin-top: 16px;
  overflow: hidden;
}

.tricker-flex-wrap {
  display: flex;
}

.tricker-single-wrap {
  flex: none;
  display: flex;
}

.tricker-single-image {
  flex: none;
}

.tricker-title {
  text-align: center;
}

.feature-v1-top-wrap {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.feature-v1-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  flex-flow: column;
  margin-top: 56px;
  display: flex;
}

.feature-v1-title {
  font-size: var(--_typography---h4--font-size);
  line-height: var(--_typography---all-line-height--line-height-137);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
  text-align: center;
  letter-spacing: -.32px;
  margin-top: 12px;
  margin-bottom: 16px;
}

.feature-v2-details {
  color: var(--all-color--gray-light);
  text-align: center;
  max-width: 440px;
}

.feature-v2-details._01 {
  color: var(--all-color--white);
  text-align: left;
}

.feature-v1-grid-wrap {
  grid-column-gap: 26px;
  grid-row-gap: 26px;
  grid-template-rows: auto;
  grid-template-columns: 1.71fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.feature-v1-grid-wrap._02 {
  grid-template-columns: 1fr 1.71fr;
}

.feature-v1-left-wrap {
  grid-column-gap: 56px;
  grid-row-gap: 56px;
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
  background-image: url("../images/686a464cb9c3de9845464a3e_Feature%20Bg%2004.webp");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  flex-flow: column;
  padding: 32px;
  display: flex;
}

.feature-v1-left-wrap._02 {
  background-image: url("../images/686a464c76526f0bc83cf237_Feature%20Bg%2003.webp");
}

.feature-v1-left-wrap.v2 {
  background-image: url("../images/686a464cb9c3de9845464a3e_Feature%20Bg%2004.webp");
}

.feature-v1-right-wrap {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
  background-image: url("../images/686a464cb53ea4e1a5f17b83_Feature%20Bg%2002.webp");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  flex-flow: column;
  justify-content: space-between;
  padding: 32px;
  display: flex;
}

.feature-v1-right-wrap._02 {
  background-image: url("../images/686a464cb9c3de9845464a3e_Feature%20Bg%2004.webp");
}

.feature-v1-single-image-wrap {
  justify-content: center;
  align-items: center;
  display: flex;
}

.feature-v1-details-wrap {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  flex-flow: column;
  display: flex;
}

.feature-v1-single-title {
  font-size: var(--_typography---h5--font-size);
  line-height: var(--_typography---all-line-height--line-height-150);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
}

.feature-v1-single-details {
  color: var(--all-color--gray-light);
  font-size: var(--_typography---h6--all-paragraph--p-14);
  line-height: var(--_typography---all-line-height--line-height-160);
}

.work-v1-wrap {
  margin-top: 56px;
}

.work-v1-grid {
  grid-column-gap: 58px;
  grid-row-gap: 58px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 2.44fr;
  grid-auto-columns: 1fr;
  place-items: start stretch;
  display: grid;
}

.work-v1-left-wrap {
  grid-column-gap: 12px;
  grid-row-gap: 12px;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
  position: sticky;
  top: 140px;
}

.work-v1-right-flex-wrap {
  grid-column-gap: 32px;
  grid-row-gap: 32px;
  flex-flow: column;
  display: flex;
}

.work-v1-single-wrap {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
  background-image: url("../images/686a464cb9c3de9845464a3e_Feature%20Bg%2004.webp");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  flex-flow: column;
  justify-content: space-between;
  height: 480px;
  padding: 48px 32px 32px;
  display: flex;
  position: sticky;
  top: 140px;
}

.work-left-line-wrap {
  grid-column-gap: 5px;
  grid-row-gap: 5px;
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  max-width: 12px;
  display: flex;
}

.work-line-dots {
  border-radius: var(--_typography---all-font-weight--all-border--border-80);
  background-color: var(--all-color--primary);
  width: 12px;
  height: 12px;
}

.work-single-line {
  border-radius: var(--_typography---all-font-weight--all-border--border-12);
  background-color: var(--all-color--sea-green-8);
  width: 2px;
  height: 540px;
  overflow: hidden;
}

.work-all-item-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  flex-flow: column;
  display: flex;
}

.work-v1-single-item-title {
  color: var(--all-color--gray);
  line-height: var(--_typography---all-line-height--line-height-100);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
}

.colro-bg {
  background-color: var(--all-color--primary);
  width: 2px;
  height: 10%;
}

.advantage-grid-wrap {
  grid-column-gap: 50px;
  grid-row-gap: 50px;
  grid-template-rows: auto;
  grid-template-columns: 1.5fr 1fr;
  grid-auto-columns: 1fr;
  place-items: center stretch;
  display: grid;
}

.advantage-left-wrap {
  grid-column-gap: 40px;
  grid-row-gap: 40px;
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---h5--font-size);
  background-image: url("../images/686a6b44232dfc421905c521_Advantage%20Bg.webp");
  background-position: 0 0;
  background-repeat: no-repeat;
  background-size: cover;
  flex-flow: column;
  justify-content: space-between;
  align-items: stretch;
  padding-top: 48px;
  padding-left: 48px;
  padding-right: 48px;
  display: flex;
}

.advantage-top-wrap {
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
}

.advantage-list-wrapper {
  grid-column-gap: 42px;
  grid-row-gap: 42px;
  flex-flow: column;
  margin-top: 48px;
  margin-bottom: 56px;
  display: flex;
}

.advantage-list-wrap {
  grid-column-gap: 32px;
  grid-row-gap: 32px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.advantage-list-details-wrap {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
}

.advantage-list-details {
  color: var(--all-color--gray-light);
}

.advantage-list-title {
  font-size: var(--_typography---h6--font-size);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
}

.advantage-btn-wrap {
  display: flex;
}

.advantage-image-wrap {
  justify-content: center;
  align-items: center;
  display: flex;
}

.advantage-left-title {
  font-size: var(--_typography---h5--font-size);
  line-height: var(--_typography---all-line-height--line-height-150);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
}

.advantage-left-details {
  max-width: 476px;
  margin-top: 12px;
  margin-bottom: 24px;
}

.advantage-btm-wrap {
  grid-column-gap: 8px;
  grid-row-gap: 8px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.blog-v1-wrap {
  margin-top: 56px;
}

.blog-v1-grid-wrap {
  grid-column-gap: 32px;
  grid-row-gap: 32px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 2.05fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.blog-v1-cl-list {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  flex-flow: column;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: flex;
}

.blog-v1-author-wrapper {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  justify-content: flex-start;
  align-items: center;
  margin-top: 24px;
  margin-bottom: 16px;
  display: flex;
}

.blog-v1-right-cl-list {
  grid-column-gap: 32px;
  grid-row-gap: 32px;
  flex-flow: column;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  justify-content: flex-start;
  align-items: stretch;
  display: flex;
}

.blog-v1-card-link {
  font-size: var(--_typography---h5--font-size);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
  transition: all .3s;
}

.blog-v1-card-link:hover {
  color: var(--all-color--primary);
}

.feature-top-wrap {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.feature-title {
  text-align: center;
  max-width: 704px;
  margin-top: 12px;
  margin-bottom: 24px;
}

.feature-details {
  text-align: center;
  max-width: 608px;
}

.feature-btn-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  justify-content: flex-start;
  align-items: center;
  margin-top: 48px;
  display: flex;
}

.feature-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  flex-flow: column;
  margin-top: 112px;
  display: flex;
}

.feature-bottom-wrap {
  grid-column-gap: 86px;
  grid-row-gap: 86px;
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
  background-image: url("../images/686a8c83662f7653888a8f75_Feature%20Btm%20Bg.webp");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  place-items: center stretch;
  padding: 58px 54px 98px 32px;
  display: grid;
}

.feature-btm-image {
  width: 100%;
}

.featuer-review-wrap {
  border-left: 1px solid var(--all-color--sea-green-8);
  margin-top: 47px;
  padding-left: 24px;
}

.feature-review {
  color: var(--all-color--gray-light);
  max-width: 377px;
}

.feature-author-wrap {
  grid-column-gap: 12px;
  grid-row-gap: 12px;
  justify-content: flex-start;
  align-items: center;
  margin-top: 24px;
  display: flex;
}

.feature-author-details-wrap {
  grid-column-gap: 12px;
  grid-row-gap: 12px;
  flex-flow: column;
  display: flex;
}

.feature-author-name {
  line-height: var(--_typography---all-line-height--line-height-100);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
}

.feature-author-pst {
  color: var(--all-color--gray-light);
  font-size: var(--_typography---h6--all-paragraph--p-12);
  line-height: var(--_typography---all-line-height--line-height-100);
}

.process-grid-wrap {
  grid-column-gap: 50px;
  grid-row-gap: 50px;
  grid-template-rows: auto;
  grid-template-columns: 1.09fr 1fr;
  grid-auto-columns: 1fr;
  place-items: center stretch;
  display: grid;
}

.process-image {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
}

.process-title {
  font-size: var(--_typography---h4--font-size);
  line-height: var(--_typography---all-line-height--line-height-137);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
  margin-top: 12px;
  margin-bottom: 16px;
}

.process-details {
  color: var(--all-color--gray-light);
  text-align: left;
}

.process-list-wrap {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  flex-flow: column;
  margin-top: 32px;
  margin-bottom: 48px;
  display: flex;
}

.process-list-item {
  grid-column-gap: 12px;
  grid-row-gap: 12px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.process-list-text {
  color: var(--all-color--gray-light);
}

.process-btn-wrap {
  display: flex;
}

.complex-grid-wrap {
  grid-column-gap: 40px;
  grid-row-gap: 40px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1.11fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.complex-image {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
}

.complex-title {
  font-size: var(--_typography---h4--font-size);
  line-height: var(--_typography---all-line-height--line-height-137);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
}

.complex-right-grid-wrap {
  grid-column-gap: 56px;
  grid-row-gap: 32px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  margin-top: 56px;
  display: grid;
}

.complex-single-title {
  line-height: var(--_typography---all-line-height--line-height-100);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
  margin-top: 24px;
  margin-bottom: 16px;
}

.complex-single-details {
  color: var(--all-color--gray-light);
  font-size: var(--_typography---h6--all-paragraph--p-14);
}

.integration-wrap {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
  background-image: url("../images/686a9c2ec2a20931e04fd25d_Intergration%20BG.webp");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  margin-top: 56px;
  padding-top: 70px;
  display: flex;
}

.code-wrap {
  margin-top: 56px;
  margin-bottom: 48px;
}

.code-all-wrap {
  grid-column-gap: 64px;
  grid-row-gap: 64px;
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
  background-image: url("../images/686a9d690228fa3d7d0a2de9_No%20Code%20BG.png");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  flex-flow: column;
  padding-top: 32px;
  padding-bottom: 56px;
  padding-left: 48px;
  display: flex;
}

.need-code-wrap {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  flex-flow: column;
  max-width: 846px;
  display: flex;
}

.nedd-single-details {
  color: var(--all-color--gray-light);
  font-size: var(--_typography---h6--all-paragraph--p-14);
  line-height: var(--_typography---all-line-height--line-height-160);
}

.code-single-image {
  width: 100%;
}

.code-grid-wrap {
  grid-column-gap: 40px;
  grid-row-gap: 40px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.code-item-icon-wrap {
  border: 1px solid var(--all-color--sea-green-20);
  border-radius: var(--_typography---all-font-weight--all-border--border-80);
  background-color: var(--all-color--sea-green-8);
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  display: flex;
}

.code-item-title {
  line-height: var(--_typography---all-line-height--line-height-100);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
  margin-top: 24px;
  margin-bottom: 16px;
}

.code-item-details {
  color: var(--all-color--gray-light);
  font-size: var(--_typography---h6--all-paragraph--p-14);
}

.benefit-wrap {
  margin-top: 56px;
}

.benefit-grid-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.benefit-single-wrap {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
  background-color: var(--all-color--primary-black);
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  padding: 32px;
  display: flex;
  box-shadow: 0 2px 4px 2px #0003;
}

.benefits-icon-wrap {
  border: 1px solid var(--all-color--sea-green-20);
  border-radius: var(--_typography---all-font-weight--all-border--border-80);
  background-color: var(--all-color--sea-green-8);
  justify-content: center;
  align-items: center;
  width: 120px;
  height: 120px;
  display: flex;
}

.benefits-single-title {
  font-size: var(--_typography---h5--font-size);
  line-height: var(--_typography---all-line-height--line-height-150);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
  text-align: center;
  margin-top: 40px;
  margin-bottom: 16px;
}

.benefits-single-details {
  color: var(--all-color--gray-light);
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
  text-align: center;
}

.hero-v2-top-wrap {
  flex-flow: column;
  justify-content: center;
  align-items: flex-start;
  width: 100%;
  max-width: 548px;
  display: flex;
}

.hero-v2-title {
  font-size: var(--_typography---h3--font-size);
  margin-top: 16px;
  margin-bottom: 20px;
}

.hero-v2-details {
  max-width: 440px;
}

.hero-v2-btn-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  justify-content: flex-start;
  align-items: center;
  margin-top: 30px;
  margin-bottom: 40px;
  display: flex;
}

.hero-v2-image {
  border-top: 1px solid #fff3;
  border-left: 1px solid #fff3;
  border-top-left-radius: 24px;
  width: 100%;
  height: 680px;
}

.about-v2-wrapper {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
  background-image: url("../images/686bbab04fc11d7f873457af_About%20V2%20BG.webp");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  padding: 32px;
}

.about-v2-top-wrap {
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
}

.about-v2-wrap {
  margin-top: 64px;
}

.about-v2-details {
  font-size: var(--_typography---h5--font-size);
  line-height: var(--_typography---all-line-height--line-height-150);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
  text-align: left;
  display: none;
}

.about-v2-grid-wrap {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.about-v2-single-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-16);
  background-color: var(--all-color--primary-black);
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 24px;
  display: flex;
}

.about-v2-single-item {
  grid-column-gap: 12px;
  grid-row-gap: 12px;
  flex-flow: column;
  display: flex;
}

.about-v2-counter-details {
  color: var(--all-color--gray-light);
}

.feature-v2-wrap {
  margin-top: 56px;
}

.feature-v2-grid-wrap, .feature-v2-grid-wraps {
  grid-column-gap: 26px;
  grid-row-gap: 26px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.feature-v2-left-wrap {
  grid-column-gap: 36px;
  grid-row-gap: 36px;
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
  background-image: url("../images/686bc155df0fe615b05fae47_Feature%20V2%2004%20Bg.svg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  flex-flow: column;
  padding: 32px;
  display: flex;
}

.feature-v-right-wrap {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
  background-image: url("../images/686a464cb9c3de9845464a3e_Feature%20Bg%2004.webp");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  flex-flow: column;
  justify-content: space-between;
  padding: 32px;
  display: flex;
}

.work-v2-wrap {
  margin-top: 58px;
  position: sticky;
  top: 140px;
}

.work-v2-number-wrap {
  flex-flow: column;
  display: flex;
  position: relative;
}

.work-v2-nb-grid-wrap {
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.work-v2-nb-single-wrap {
  grid-column-gap: 12px;
  grid-row-gap: 12px;
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.work-v2-nb-single-wrap._01.disply, .work-v2-nb-single-wrap._02.display, .work-v2-nb-single-wrap._03.display {
  display: none;
}

.work-v2-nb-icon-wrap {
  border: 1px solid var(--all-color--sea-green-20);
  border-radius: var(--_typography---all-font-weight--all-border--border-60);
  background-color: #042222;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  display: flex;
}

.work-v2-single-number {
  line-height: var(--_typography---all-line-height--line-height-100);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
}

.work-v2-number-title {
  font-size: var(--_typography---h5--font-size);
  line-height: var(--_typography---all-line-height--line-height-150);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
}

.work-numbe-line-wrap {
  z-index: -1;
  border-top: 1px dashed var(--all-color--sea-green-20);
  flex-flow: column;
  justify-content: center;
  align-items: flex-start;
  width: 100%;
  height: 2px;
  position: absolute;
  inset: 20% 0% auto;
}

.work-numbe-color-line {
  background-color: var(--all-color--primary);
  width: 10%;
  height: 1px;
  margin-top: -1px;
}

.work-v2-grid-warp {
  grid-column-gap: 57px;
  grid-row-gap: 57px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  margin-top: 56px;
  display: grid;
  overflow: hidden;
}

.work-v2-single-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  flex-flow: column;
  display: flex;
}

.work-v2-single-image {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-20);
}

.work-v2-single-details {
  font-size: var(--_typography---h6--all-paragraph--p-14);
  line-height: var(--_typography---all-line-height--line-height-160);
  text-align: center;
}

.work-height-wrap {
  height: 200vh;
}

.advantage-v2-wrap {
  margin-top: 56px;
}

.advantage-v2-details {
  color: var(--all-color--gray-light);
  text-align: center;
  max-width: 512px;
}

.advantage-v2-grid-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.advantage-v2-single-wrap {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---h5--font-size);
  background-color: var(--all-color--primary-black);
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  padding: 32px;
  transition: border-color .3s;
}

.advantage-v2-single-wrap:hover {
  border-color: var(--all-color--primary);
}

.advantage-v2-icon-wrap {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-12);
  background-color: var(--all-color--primary-black);
  justify-content: center;
  align-items: center;
  width: 56px;
  height: 56px;
  display: flex;
}

.advantage-v2-single-title {
  font-size: var(--_typography---h6--font-size);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
  margin-top: 32px;
  margin-bottom: 16px;
}

.advantage-v2-single-details {
  color: var(--all-color--gray-light);
}

.blog-v2-wrap {
  margin-top: 56px;
}

.blog-v2-grid-wrap {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.blog-v2-author-wrapper {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  justify-content: flex-start;
  align-items: center;
  margin-top: 24px;
  margin-bottom: 16px;
  display: flex;
}

.blog-v2-ctg {
  border: 1px solid var(--all-color--sea-green-20);
  border-radius: var(--_typography---all-font-weight--all-border--border-60);
  font-size: var(--_typography---h6--all-paragraph--p-12);
  line-height: var(--_typography---all-line-height--line-height-100);
  padding: 8px 12px;
}

.cta-v2-wrapper {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
  background-image: url("../images/686bea1c91dd121f88378a4d_CTA.png");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  padding-top: 54px;
  padding-left: 50px;
  padding-right: 48px;
  overflow: hidden;
}

.cta-v2-grid-wrap {
  grid-column-gap: 132px;
  grid-row-gap: 132px;
  grid-template-rows: auto;
  grid-template-columns: 1.08fr 1fr;
  grid-auto-columns: 1fr;
  place-items: center stretch;
  display: grid;
}

.cta-v2-right-title {
  font-size: var(--_typography---h4--font-size);
  line-height: var(--_typography---all-line-height--line-height-137);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
}

.cta-v2-right-details {
  margin-top: 24px;
  margin-bottom: 48px;
}

.cta-v2-btn-wap {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  grid-template-rows: auto auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: flex;
}

.faq-v2-wrap {
  margin-top: 72px;
}

.faq-v2-details {
  color: var(--all-color--gray-light);
  text-align: center;
  max-width: 480px;
}

.faq-v2-grid-wap {
  grid-column-gap: 32px;
  grid-row-gap: 32px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.faq-v2-single-wrap {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---h6--font-size);
  background-color: var(--all-color--primary-black);
  padding: 20px 24px;
}

.faq-v2-answer {
  color: var(--all-color--gray-light);
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
  margin-top: 12px;
}

.faq-v2-tab-menu {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  flex-flow: column;
  display: flex;
}

.faq-v2-arrow-wrap {
  justify-content: center;
  align-items: center;
  display: flex;
}

.testimonial-v2-wrapper {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-24);
  background-image: url("../images/686befacd6c6423f1087383a_Testimonial%20BG.svg");
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  padding: 70px 100px;
}

.testimonial-v2-wrap {
  margin-top: 80px;
  overflow: hidden;
}

.hero-v2-grid-wrap {
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  place-items: center stretch;
  width: 100%;
  height: 100%;
  display: grid;
}

.home-v2-hero-wrapper {
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.home-hero-image-block {
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.home-hero-details-block {
  position: absolute;
  inset: auto 0% 10%;
}

.testimonial-v2-slider {
  background-color: var(--all-color--transparent);
  height: auto;
  padding-bottom: 50px;
}

.slider-nav {
  height: 18px;
  padding-top: 0;
  padding-bottom: 0;
  font-size: 13px;
}

.testimonial-v2-slider-mask {
  max-width: 362px;
  overflow: visible;
}

.testimonial-v2-slide {
  margin-right: 20px;
}

.testimonial-v2-single-slide {
  border: 1px solid var(--all-color--sea-green-8);
  border-radius: var(--_typography---all-font-weight--all-border--border-20);
  background-color: var(--all-color--primary-black);
  flex-flow: column;
  justify-content: space-between;
  height: 412px;
  padding: 32px 24px;
  display: flex;
}

.testimonial-v2-top {
  grid-column-gap: 24px;
  grid-row-gap: 24px;
  flex-flow: column;
  display: flex;
}

.testimonial-v2-btm {
  grid-column-gap: 12px;
  grid-row-gap: 12px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.testimonial-v2-single-title {
  font-size: var(--_typography---h6--font-size);
  line-height: var(--_typography---all-line-height--line-height-150);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
}

.author-details-name {
  font-weight: var(--_typography---all-font-weight--font-weight-500);
}

.author-details-pst {
  color: var(--all-color--gray);
  font-size: var(--_typography---h6--all-paragraph--p-12);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
}

.dropdown-icon-wrap {
  justify-content: center;
  align-items: center;
  display: flex;
  position: relative;
}

.dropdown-toggle {
  grid-column-gap: 10px;
  grid-row-gap: 10px;
  color: var(--all-color--white);
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-160);
  font-weight: var(--_typography---all-font-weight--font-weight-400);
  justify-content: flex-start;
  align-items: center;
  padding: 0;
  display: flex;
}

.dropdown-list {
  background-color: #0000;
  width: 650px;
  overflow: hidden;
  transform: translate(-80%);
}

.dropdown-singel-link {
  grid-column-gap: 4px;
  grid-row-gap: 4px;
  color: var(--all-color--gray-light);
  font-size: var(--_typography---h6--all-paragraph--p-16);
  line-height: var(--_typography---all-line-height--line-height-150);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
  text-transform: capitalize;
  justify-content: flex-start;
  align-items: center;
  transition: all .4s;
  display: flex;
}

.dropdown-singel-link:hover {
  color: var(--all-color--primary);
  transform: translate(5px);
}

.dropdown-padding-wrap {
  padding-top: 20px;
}

.dropdown-padding {
  border: 1px solid var(--all-color--sea-green-20);
  border-radius: var(--_typography---all-font-weight--all-border--border-12);
  background-color: var(--all-color--primary-black);
  padding: 35px 40px;
}

.dropdown-singel-wrapper {
  grid-column-gap: 20px;
  grid-row-gap: 20px;
  flex-flow: column;
  display: flex;
}

.dropdown-icon._02 {
  position: absolute;
}

.dropdown-title {
  font-family: var(--_typography---body);
  color: var(--all-color--white);
  font-size: var(--_typography---h5--font-size);
  line-height: var(--_typography---all-line-height--line-height-150);
  font-weight: var(--_typography---all-font-weight--font-weight-600);
}

.dropdown-menu-wrapper {
  grid-column-gap: 14px;
  grid-row-gap: 14px;
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
}

.dropdown-flex-wrap {
  justify-content: space-between;
  align-items: stretch;
  display: flex;
}

.about-content-title-wrapper {
  font-size: var(--_typography---h5--font-size);
  line-height: var(--_typography---all-line-height--line-height-150);
  font-weight: var(--_typography---all-font-weight--font-weight-500);
  text-align: center;
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  margin-top: 12px;
  margin-bottom: 32px;
  display: flex;
}

.about-content-title-wrapper._01 {
  justify-content: flex-start;
  align-items: flex-start;
  margin-top: 16px;
  margin-bottom: 40px;
}

.about-content-title-wrapper._02 {
  justify-content: flex-start;
  align-items: flex-start;
  margin-top: 16px;
  margin-bottom: 0;
}

.about-content-title-wrap {
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
  position: relative;
  overflow: hidden;
}

.about-content-text {
  color: var(--all-color--gray);
  line-height: var(--_typography---all-line-height--line-height-150);
}

.about-content-text.gray {
  color: var(--all-color--white);
  white-space: nowrap;
  position: absolute;
  overflow: hidden;
}

@media screen and (min-width: 1440px) {
  .hero-v2-title {
    font-size: var(--_typography---h2--font-size);
    margin-bottom: 24px;
  }

  .hero-v2-details {
    font-size: var(--_typography---h6--all-paragraph--p-16);
  }

  .hero-v2-btn-wrap {
    margin-top: 48px;
    margin-bottom: 96px;
  }

  .hero-v2-image {
    height: auto;
  }

  .advantage-v2-single-wrap:hover {
    border-color: var(--all-color--primary);
  }
}

@media screen and (min-width: 1920px) {
  .hero-wrap {
    position: relative;
  }

  .hero-dashboar-image {
    width: 100%;
  }

  .hero-v2-image {
    box-sizing: border-box;
    object-fit: cover;
    height: auto;
  }
}

@media screen and (max-width: 991px) {
  body {
    --_typography---body: Poppins, sans-serif;
    --_typography---h6--all-paragraph--p-16: 16px;
    --_typography---all-line-height--line-height-160: 160%;
    --_typography---all-font-weight--font-weight-400: 400;
    --_typography---h1--font-size: 48px;
    --_typography---all-line-height--line-height-130: 130%;
    --_typography---all-font-weight--font-weight-600: 600;
    --_typography---h2--font-size: 42px;
    --_typography---h3--font-size: 36px;
    --_typography---all-line-height--line-height-140: 140%;
    --_typography---h4--font-size: 28px;
    --_typography---all-line-height--line-height-137: 137%;
    --_typography---h5--font-size: 22px;
    --_typography---all-line-height--line-height-150: 150%;
    --_typography---h6--font-size: 18px;
    --_typography---all-line-height--line-height-100: 100%;
    --_typography---all-font-weight--font-weight-500: 500;
    --_typography---all-font-weight--all-border--border-32: 20px;
    --_typography---all-font-weight--all-border--border-40: 40px;
    --_typography---all-font-weight--all-border--border-999: 999px;
    --_typography---all-font-weight--all-border--border-24: 20px;
    --_typography---h6--all-paragraph--p-14: 14px;
    --_typography---h6--all-paragraph--p-12: 12px;
    --_typography---all-font-weight--all-border--border-20: 20px;
    --_typography---all-font-weight--all-border--border-12: 12px;
    --_typography---all-font-weight--all-border--border-10: 10px;
    --_typography---all-font-weight--all-border--border-16: 16px;
    --_typography---all-font-weight--all-border--border-80: 80px;
    --_typography---all-font-weight--all-border--border-60: 60px;
  }

  .nav-menu-wrap {
    background-color: var(--all-color--black);
    border-radius: 0;
    flex-flow: column;
    justify-content: flex-start;
    align-items: flex-start;
    height: 100%;
    padding: 40px;
    overflow: auto;
  }

  .nav-menu-wrapper {
    background-color: var(--all-color--transparent);
    height: 100vh;
    overflow: auto;
  }

  .nav-single-menu {
    margin-left: 0;
    margin-right: 0;
  }

  .menu-button {
    background-color: var(--all-color--transparent);
    width: 40px;
    height: 40px;
    padding: 0;
    overflow: hidden;
  }

  .menu-button.w--open {
    background-color: var(--all-color--transparent);
  }

  .section {
    padding-top: 100px;
    padding-bottom: 100px;
  }

  .section.style {
    padding-top: 160px;
  }

  .section.footer {
    background-size: cover;
    padding-bottom: 30px;
  }

  .section.changelog, .section.privacy, .section.document {
    padding-top: 160px;
  }

  .section.contact {
    padding-top: 150px;
  }

  .section._404, .section.blog, .section.pricing, .section.about, .section.hero, .section.feature {
    padding-top: 160px;
  }

  .section.hero-v2 {
    background-size: cover;
    padding-top: 180px;
    padding-bottom: 80px;
  }

  .style-guide-wrap {
    margin-top: 80px;
  }

  .style-title {
    margin-bottom: 20px;
  }

  .style-flex-wrap {
    grid-column-gap: 40px;
    grid-row-gap: 40px;
  }

  .style-single-wrap {
    padding: 30px 25px;
  }

  .color-wrapper {
    grid-column-gap: 40px;
    grid-row-gap: 40px;
  }

  .color-grid-wrap {
    grid-column-gap: 25px;
    grid-row-gap: 30px;
    grid-template-columns: 1fr 1fr;
  }

  .color-title {
    font-size: var(--_typography---h3--font-size);
  }

  .color-single-wrap {
    grid-column-gap: 25px;
    grid-row-gap: 25px;
  }

  .color-item-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .footer-top-wrap {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
  }

  .footer-wrap {
    margin-top: 50px;
    margin-bottom: 50px;
  }

  .footer-grid-wrap {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
  }

  .footer-left-wrap {
    grid-column-gap: 25px;
    grid-row-gap: 25px;
  }

  .footer-brand-image {
    width: 180px;
  }

  .license-flex-wrap {
    grid-column-gap: 35px;
    grid-row-gap: 35px;
  }

  .license-single-wrap {
    padding-left: 25px;
    padding-right: 25px;
  }

  .license-single-line {
    margin-top: 20px;
    margin-bottom: 20px;
  }

  .license-image-list {
    margin-bottom: 30px;
  }

  .license-image-grid {
    grid-template-columns: 1fr 1fr;
  }

  .license-icon-image {
    width: 100px;
  }

  .license-font-wrap {
    padding: 30px 25px;
  }

  .license-font {
    font-size: 100px;
  }

  .license-font-name {
    font-size: var(--_typography---h4--font-size);
  }

  .changelog-wrap {
    grid-column-gap: 40px;
    grid-row-gap: 40px;
    margin-top: 80px;
  }

  .chagnelog-single-details {
    margin-bottom: 20px;
  }

  .chagnelog-image {
    margin-top: 25px;
    margin-bottom: 25px;
  }

  .privacy-wrap {
    margin-top: 80px;
  }

  .privacy-title {
    margin-bottom: 20px;
  }

  .privacy-flex-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    margin-top: 40px;
  }

  .document-wrap {
    margin-top: 80px;
  }

  .document-tabs-menu {
    max-width: 240px;
    padding: 15px;
  }

  .document-tabs-content {
    margin-left: 30px;
  }

  .document-single-image {
    margin-top: 25px;
  }

  .contact-wrap {
    margin-top: 80px;
  }

  .contact-title {
    margin-bottom: 20px;
  }

  .contact-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .contact-left-wrap {
    padding: 30px 20px;
  }

  .contact-right-wrap {
    padding: 30px 25px;
  }

  .contact-submit-button {
    margin-top: 40px;
  }

  .contact-filed-wrapper {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    flex-flow: column;
  }

  .contact-single-details {
    margin-top: 35px;
  }

  .contact-all-details-wrap {
    margin-top: 55px;
    margin-bottom: 55px;
  }

  ._404-wrapper {
    padding-bottom: 60px;
  }

  ._404-wrap {
    margin-top: 60px;
  }

  ._404-single-title {
    padding-top: 20px;
    padding-bottom: 20px;
  }

  ._404-icon {
    width: 160px;
  }

  ._404-title, ._404-button-wrap {
    margin-top: 25px;
  }

  .blog-wrap {
    margin-top: 80px;
  }

  .blog-title {
    margin-bottom: 20px;
  }

  .blog-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 30px;
  }

  .blog-card-image {
    object-fit: cover;
    width: 100%;
    height: 350px;
  }

  .blog-author-wrapper {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    margin-top: 20px;
  }

  .cta-wrapper {
    padding: 40px;
  }

  .cta-wrap {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
    grid-template-columns: 1.18fr;
  }

  .cta-button-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .cta-image-top-wrap {
    margin-right: 0;
  }

  .cta-image-wrap {
    grid-column-gap: 16px;
    grid-row-gap: 16px;
    grid-template-rows: auto;
    grid-template-columns: 1fr 1fr;
    grid-auto-columns: 1fr;
    display: grid;
  }

  .cta-image._01 {
    width: 100%;
  }

  .cta-image._02 {
    width: 100%;
    margin-top: 0;
    margin-left: 0;
  }

  .blog-thimble-image {
    height: 480px;
    margin-top: 30px;
  }

  .blog-single-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    grid-template-columns: 2.86fr 1.75fr;
  }

  .blog-rich-text h4 {
    margin-top: 25px;
  }

  .more-blog-wrap {
    margin-top: 50px;
  }

  .more-blog-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 30px;
    grid-template-columns: 1fr 1fr;
  }

  .pricing-wrap {
    margin-top: 80px;
    margin-bottom: 80px;
  }

  .pricing-title {
    margin-bottom: 20px;
  }

  .pricing-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    grid-template-columns: 1fr 1fr;
  }

  .pricing-single-wrap {
    padding: 25px;
  }

  .pricing-price-wrap {
    margin-top: 30px;
    margin-bottom: 30px;
  }

  .pricing-price-month {
    letter-spacing: -.1px;
  }

  .pricing-list-wrapper {
    margin-bottom: 30px;
  }

  .compare-wrap {
    overflow: auto;
  }

  .compare-all-wrap {
    width: 1000px;
    overflow: auto;
  }

  .faq-grid-wrap, .faq-right-wrap {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
  }

  .faq-btm-wrap {
    padding: 25px;
  }

  .faq-details {
    margin-bottom: 25px;
  }

  .about-wrap {
    margin-top: 80px;
  }

  .about-title {
    margin-bottom: 20px;
  }

  .about-image-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    margin-top: 80px;
    margin-bottom: 40px;
  }

  .story-wrap {
    margin-top: 40px;
  }

  .story-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .story-single-wrap {
    padding: 25px;
  }

  .story-single-title {
    margin-top: 25px;
  }

  .about-content-wrap {
    padding: 40px 25px;
  }

  .about-stat-wrap {
    margin-top: 40px;
  }

  .why-grid-wrap {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
    grid-template-columns: 1fr 1fr;
  }

  .why-single-wrap {
    padding: 30px 25px;
  }

  .why-single-title, .why-btm-wrap {
    margin-top: 30px;
  }

  .built-grid-wrap {
    grid-column-gap: 50px;
    grid-row-gap: 50px;
  }

  .built-right-grid-wrap {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
  }

  .built-single-overflow-wrap {
    height: 48px;
  }

  .team-wrap {
    grid-column-gap: 25px;
    grid-row-gap: 25px;
    margin-top: 50px;
  }

  .team-top-btm-wrap {
    padding-bottom: 20px;
  }

  .team-single-details {
    margin-bottom: 20px;
  }

  .taem-btm-grid-wrap {
    grid-template-columns: 1fr 1fr;
  }

  .testimonial-wrap {
    margin-top: 50px;
  }

  .testimonial-all-wrap {
    padding: 40px 25px;
  }

  .testimonial-tabs-menu {
    margin-top: 40px;
  }

  .testimonial-star {
    margin-top: 25px;
  }

  .hero-wrap {
    margin-top: 60px;
  }

  .hero-title {
    margin-bottom: 20px;
  }

  .hero-btn-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    margin-top: 40px;
  }

  .tricker-single-image {
    width: 160px;
  }

  .feature-v1-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    margin-top: 50px;
  }

  .feature-v1-grid-wrap, .feature-v1-grid-wrap._02 {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .feature-v1-left-wrap {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
    padding: 25px;
  }

  .feature-v1-right-wrap {
    padding: 25px;
  }

  .work-v1-wrap {
    margin-top: 50px;
  }

  .work-v1-grid {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
  }

  .work-v1-right-flex-wrap {
    grid-column-gap: 25px;
    grid-row-gap: 25px;
  }

  .work-v1-single-wrap {
    height: 400px;
    padding: 30px;
  }

  .work-all-item-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .advantage-grid-wrap {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
    grid-template-columns: 1.5fr;
  }

  .advantage-left-wrap {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
    padding: 30px;
  }

  .advantage-list-wrapper {
    grid-column-gap: 25px;
    grid-row-gap: 25px;
    margin-top: 30px;
    margin-bottom: 30px;
  }

  .advantage-list-wrap {
    grid-column-gap: 25px;
    grid-row-gap: 25px;
  }

  .advantage-list-details-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
  }

  .advantage-left-details {
    margin-bottom: 20px;
  }

  .blog-v1-wrap {
    margin-top: 50px;
  }

  .blog-v1-grid-wrap {
    grid-column-gap: 25px;
    grid-row-gap: 25px;
    grid-template-columns: 1fr;
  }

  .blog-v1-cl-list {
    grid-column-gap: 20px;
    grid-row-gap: 30px;
  }

  .blog-v1-author-wrapper {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    margin-top: 20px;
  }

  .blog-v1-right-cl-list {
    grid-column-gap: 20px;
    grid-row-gap: 30px;
  }

  .feature-title {
    margin-bottom: 20px;
  }

  .feature-btn-wrap {
    margin-top: 30px;
  }

  .feature-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    margin-top: 80px;
  }

  .feature-bottom-wrap {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
    padding: 40px 25px;
  }

  .featuer-review-wrap {
    margin-top: 30px;
    padding-left: 20px;
  }

  .process-grid-wrap {
    grid-column-gap: 40px;
    grid-row-gap: 40px;
    grid-template-columns: 1.09fr;
  }

  .process-image {
    width: 100%;
  }

  .process-list-wrap {
    margin-top: 25px;
    margin-bottom: 30px;
  }

  .complex-grid-wrap {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
  }

  .complex-right-grid-wrap {
    margin-top: 50px;
  }

  .complex-single-title {
    margin-top: 20px;
  }

  .integration-wrap {
    padding-top: 50px;
  }

  .code-wrap {
    margin-top: 40px;
    margin-bottom: 40px;
  }

  .code-all-wrap {
    grid-column-gap: 40px;
    grid-row-gap: 40px;
    padding-top: 30px;
    padding-bottom: 30px;
    padding-left: 30px;
  }

  .code-grid-wrap {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
    grid-template-columns: 1fr 1fr;
  }

  .benefit-wrap {
    margin-top: 50px;
  }

  .benefit-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    grid-template-columns: 1fr 1fr;
  }

  .benefits-icon-wrap {
    width: 100px;
    height: 100px;
  }

  .benefit-single-icon {
    width: 40px;
  }

  .benefits-single-title {
    margin-top: 30px;
  }

  .hero-v2-top-wrap, .hero-v2-details {
    max-width: none;
  }

  .hero-v2-btn-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    margin-bottom: 30px;
  }

  .hero-v2-image {
    border-radius: var(--_typography---all-font-weight--all-border--border-20);
    object-fit: cover;
    border: 1px solid #0003;
    max-width: 100%;
    height: 550px;
    position: static;
  }

  .about-v2-wrapper {
    padding: 30px;
  }

  .about-v2-wrap {
    margin-top: 50px;
  }

  .about-v2-grid-wrap {
    grid-template-columns: 1fr 1fr;
  }

  .about-v2-single-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .feature-v2-wrap {
    margin-top: 50px;
  }

  .feature-v2-grid-wrap, .feature-v2-grid-wraps {
    grid-column-gap: 25px;
    grid-row-gap: 25px;
  }

  .work-v2-wrap {
    margin-top: 50px;
  }

  .work-v2-number-title {
    font-size: var(--_typography---h6--font-size);
  }

  .work-v2-grid-warp {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    margin-top: 50px;
  }

  .advantage-v2-wrap {
    margin-top: 50px;
  }

  .advantage-v2-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    grid-template-columns: 1fr 1fr;
  }

  .advantage-v2-single-wrap {
    padding: 30px;
  }

  .advantage-v2-single-title {
    margin-top: 25px;
  }

  .blog-v2-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 30px;
    grid-template-columns: 1fr 1fr;
  }

  .blog-v2-author-wrapper {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    margin-top: 20px;
  }

  .cta-v2-wrapper {
    padding: 40px 20px;
  }

  .cta-v2-grid-wrap {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
  }

  .cta-v2-right-title {
    font-size: var(--_typography---h5--font-size);
  }

  .cta-v2-right-details {
    margin-top: 20px;
    margin-bottom: 30px;
  }

  .faq-v2-wrap {
    margin-top: 50px;
  }

  .faq-v2-grid-wap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    grid-template-columns: 1fr;
  }

  .faq-v2-tab-menu {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .testimonial-v2-wrapper {
    padding: 50px 30px;
  }

  .testimonial-v2-wrap {
    margin-top: 50px;
  }

  .hero-v2-grid-wrap {
    grid-template-columns: 1fr;
  }

  .home-v2-hero-wrapper {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
    flex-flow: column;
    display: flex;
  }

  .home-hero-image-block {
    grid-template-columns: 1fr;
    width: 100%;
    max-width: 100%;
    padding-left: 15px;
    padding-right: 15px;
  }

  .home-hero-blank {
    display: none;
  }

  .home-hero-image {
    width: 100%;
    max-width: 100%;
  }

  .home-hero-details-block {
    order: -1;
    position: static;
  }

  .testimonial-v2-slide {
    margin-right: 15px;
  }

  .testimonial-v2-top {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .dropdown-list {
    width: 100%;
    max-width: 100%;
    transform: none;
  }

  .dropdown-padding-wrap {
    width: 100%;
    max-width: 100%;
  }

  .dropdown-padding {
    width: 100%;
    max-width: 100%;
    padding: 30px;
  }

  .dropdown {
    margin-left: 0;
    margin-right: 0;
  }

  .dropdown-title {
    font-size: 20px;
  }

  .dropdown-flex-wrap {
    flex-flow: wrap;
    width: 100%;
    max-width: 100%;
  }

  .about-content-title-wrapper {
    margin-bottom: 30px;
  }

  .about-content-title-wrapper._01, .about-content-title-wrapper._02 {
    margin-top: 15px;
    margin-bottom: 30px;
  }

  .about-content-text {
    font-size: 15px;
  }
}

@media screen and (max-width: 767px) {
  body {
    --_typography---body: Poppins, sans-serif;
    --_typography---h6--all-paragraph--p-16: 14px;
    --_typography---all-line-height--line-height-160: 160%;
    --_typography---all-font-weight--font-weight-400: 400;
    --_typography---h1--font-size: 42px;
    --_typography---all-line-height--line-height-130: 130%;
    --_typography---all-font-weight--font-weight-600: 600;
    --_typography---h2--font-size: 36px;
    --_typography---h3--font-size: 32px;
    --_typography---all-line-height--line-height-140: 140%;
    --_typography---h4--font-size: 26px;
    --_typography---all-line-height--line-height-137: 137%;
    --_typography---h5--font-size: 20px;
    --_typography---all-line-height--line-height-150: 150%;
    --_typography---h6--font-size: 16px;
    --_typography---all-line-height--line-height-100: 100%;
    --_typography---all-font-weight--font-weight-500: 500;
    --_typography---all-font-weight--all-border--border-32: 15px;
    --_typography---all-font-weight--all-border--border-40: 40px;
    --_typography---all-font-weight--all-border--border-999: 999px;
    --_typography---all-font-weight--all-border--border-24: 15px;
    --_typography---h6--all-paragraph--p-14: 14px;
    --_typography---h6--all-paragraph--p-12: 12px;
    --_typography---all-font-weight--all-border--border-20: 15px;
    --_typography---all-font-weight--all-border--border-12: 12px;
    --_typography---all-font-weight--all-border--border-10: 10px;
    --_typography---all-font-weight--all-border--border-16: 15px;
    --_typography---all-font-weight--all-border--border-80: 80px;
    --_typography---all-font-weight--all-border--border-60: 60px;
  }

  .nav-menu-wrapper {
    height: 90vh;
  }

  .primary-button.desktop-on {
    display: none;
  }

  .primary-btn-wrap {
    height: 15px;
  }

  .mobile-on {
    display: flex;
  }

  .section {
    padding-top: 80px;
    padding-bottom: 80px;
  }

  .section.style {
    padding-top: 140px;
  }

  .section.footer {
    padding-bottom: 25px;
  }

  .section.changelog, .section.privacy, .section.document, .section.contact, .section._404, .section.blog, .section.pricing, .section.about, .section.hero, .section.feature {
    padding-top: 140px;
  }

  .section.hero-v2 {
    padding-top: 140px;
    padding-bottom: 60px;
  }

  .style-guide-wrap {
    margin-top: 60px;
  }

  .style-title {
    margin-bottom: 15px;
  }

  .style-flex-wrap {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
  }

  .style-single-wrap {
    padding: 25px 20px;
  }

  .color-wrapper {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
  }

  .color-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    grid-template-columns: 1fr;
  }

  .color-single-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .color-item-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
  }

  .button-flex-wrap {
    flex-flow: wrap;
  }

  .footer-wrapper {
    grid-column-gap: 40px;
    grid-row-gap: 40px;
    flex-flow: column;
    display: flex;
  }

  .footer-top-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    grid-template-columns: 1.16fr;
  }

  .footer-wrap {
    order: -1;
    margin-top: 0;
    margin-bottom: 0;
  }

  .footer-email-filed {
    padding-left: 20px;
    padding-right: 120px;
  }

  .footer-submit-button {
    padding-left: 30px;
    padding-right: 30px;
  }

  .footer-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    grid-template-columns: 1fr;
  }

  .footer-left-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .footer-right-wrap {
    grid-column-gap: 16px;
    grid-row-gap: 16px;
    grid-template-rows: auto;
    grid-template-columns: 1fr 1fr 1fr;
    grid-auto-columns: 1fr;
    display: grid;
  }

  .footer-brand-image {
    width: 160px;
  }

  .copyright-wrapper {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    flex-flow: column;
  }

  .copyright-text {
    order: 1;
  }

  .license-flex-wrap {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
  }

  .license-single-wrap {
    padding: 25px 20px;
  }

  .license-single-line {
    margin-top: 15px;
    margin-bottom: 15px;
  }

  .license-image-list {
    margin-bottom: 25px;
  }

  .license-icon-grid-wrap {
    grid-template-columns: 1fr;
  }

  .license-single-icon-wrap {
    height: 220px;
  }

  .license-icon-image {
    width: 90px;
  }

  .license-font-wrap {
    padding: 25px 20px;
  }

  .license-font {
    font-size: 80px;
  }

  .changelog-wrap {
    grid-column-gap: 35px;
    grid-row-gap: 35px;
    margin-top: 60px;
  }

  .changelog-grid-wrap {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
    grid-template-columns: 1fr;
  }

  .chagnelog-single-details {
    margin-top: 15px;
    margin-bottom: 15px;
  }

  .changelog-list-title {
    margin-bottom: 15px;
  }

  .changelog-list {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
  }

  .chagnelog-image {
    margin-top: 20px;
    margin-bottom: 20px;
  }

  .changelog-line {
    display: none;
  }

  .privacy-wrap {
    margin-top: 60px;
  }

  .privacy-title {
    margin-bottom: 15px;
  }

  .privacy-flex-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    margin-top: 30px;
  }

  .privacy-single-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
  }

  .document-wrap {
    margin-top: 60px;
  }

  .document-tabs {
    flex-flow: column;
  }

  .document-tabs-menu {
    max-width: none;
    position: static;
  }

  .document-tabs-content {
    margin-top: 30px;
    margin-left: 0;
  }

  .document-single-title {
    margin-bottom: 15px;
  }

  .document-single-details {
    font-size: 15px;
  }

  .document-single-image {
    margin-top: 20px;
  }

  .document-list {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    font-size: 15px;
  }

  .contact-wrap {
    margin-top: 60px;
  }

  .contact-title {
    margin-bottom: 15px;
  }

  .contact-grid-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    grid-template-columns: 1.78fr;
  }

  .contact-left-wrap, .contact-right-wrap {
    padding: 25px 20px;
  }

  .contact-submit-button {
    margin-top: 35px;
  }

  .contact-filed-wrapper {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    flex-flow: column;
  }

  .contact-all-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
  }

  .contact-single-details {
    margin-top: 25px;
  }

  .contact-all-details-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    margin-top: 30px;
    margin-bottom: 30px;
  }

  ._404-wrapper {
    padding-bottom: 50px;
  }

  ._404-wrap {
    margin-top: 50px;
    padding-left: 15px;
    padding-right: 15px;
  }

  ._404-single-title {
    padding-top: 15px;
    padding-bottom: 15px;
  }

  ._404-icon {
    width: 140px;
  }

  ._404-title, ._404-button-wrap {
    margin-top: 20px;
  }

  .blog-wrap {
    margin-top: 60px;
  }

  .blog-title {
    margin-bottom: 15px;
  }

  .blog-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    grid-template-columns: 1fr;
  }

  .blog-card-image {
    object-fit: cover;
    width: 100%;
    height: 400px;
  }

  .blog-author-wrapper {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    margin-top: 15px;
    margin-bottom: 15px;
  }

  .blog-card-link {
    font-size: var(--_typography---h5--font-size);
  }

  .cta-wrapper {
    background-size: cover;
    padding: 30px;
  }

  .cta-details {
    margin-top: 20px;
    margin-bottom: 25px;
  }

  .cta-button-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
  }

  .cta-top-image {
    width: 100%;
  }

  .cta-image-wrap {
    grid-column-gap: 10px;
    grid-row-gap: 10px;
    margin-top: 10px;
  }

  .cta-mobile-image {
    display: none;
  }

  .cta-image-desktop {
    display: block;
  }

  .blog-single-title {
    margin-top: 15px;
    margin-bottom: 15px;
  }

  .blog-thimble-image {
    height: 420px;
    margin-top: 25px;
  }

  .blog-single-grid-wrap {
    grid-template-columns: 1fr;
  }

  .blog-single-right-wrap {
    margin-top: 0;
    position: static;
  }

  .blog-rich-text h4 {
    margin-top: 20px;
  }

  .blog-rich-text p, .blog-rich-text ul {
    margin-top: 15px;
  }

  .content-wrap, .blog-form-wrapper {
    padding: 20px;
  }

  .more-blog-wrap {
    margin-top: 40px;
  }

  .more-blog-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    grid-template-columns: 1fr;
  }

  .pricing-wrap {
    margin-top: 60px;
    margin-bottom: 60px;
  }

  .pricing-title {
    margin-bottom: 15px;
  }

  .pricing-tab-content {
    margin-top: 40px;
  }

  .pricing-grid-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    grid-template-columns: 1fr;
  }

  .pricing-single-details {
    margin-top: 15px;
  }

  .pricing-price-wrap {
    margin-top: 25px;
    margin-bottom: 25px;
  }

  .pricing-price-month {
    letter-spacing: 0;
  }

  .pricing-list-wrapper {
    margin-bottom: 25px;
  }

  .faq-grid-wrap {
    grid-column-gap: 25px;
    grid-row-gap: 25px;
    grid-template-columns: 1fr;
  }

  .faq-right-wrap {
    grid-column-gap: 25px;
    grid-row-gap: 25px;
  }

  .faq-btm-wrap {
    padding-left: 20px;
    padding-right: 20px;
  }

  .faq-details {
    margin-top: 15px;
    margin-bottom: 20px;
  }

  .about-wrap {
    margin-top: 60px;
  }

  .about-title {
    margin-bottom: 15px;
  }

  .about-image-grid-wrap {
    grid-template-columns: 1fr;
    margin-top: 60px;
    margin-bottom: 30px;
  }

  .story-top-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
  }

  .story-wrap {
    margin-top: 30px;
  }

  .story-details {
    display: block;
  }

  .story-grid-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    grid-template-columns: 1fr;
  }

  .story-single-wrap {
    padding: 20px;
  }

  .story-single-title {
    margin-top: 20px;
  }

  .about-content-wrap {
    padding: 30px 20px;
  }

  .about-content-title {
    display: block;
  }

  .about-stat-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    margin-top: 30px;
  }

  .why-title {
    margin-bottom: 15px;
  }

  .why-grid-wrap {
    grid-column-gap: 25px;
    grid-row-gap: 25px;
    grid-template-columns: 1fr;
  }

  .why-single-wrap {
    padding: 25px 20px;
  }

  .why-single-title {
    margin-top: 25px;
    margin-bottom: 15px;
  }

  .why-btm-wrap {
    margin-top: 25px;
  }

  .built-grid-wrap {
    grid-column-gap: 40px;
    grid-row-gap: 40px;
    grid-template-columns: 1.06fr;
  }

  .built-right-grid-wrap {
    grid-column-gap: 25px;
    grid-row-gap: 25px;
  }

  .built-single-overflow-wrap {
    height: 42px;
  }

  .team-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    margin-top: 40px;
  }

  .team-title {
    margin-bottom: 15px;
  }

  .taem-top-grid-wrap {
    grid-template-columns: 1fr;
  }

  .team-top-btm-wrap {
    padding-top: 15px;
    padding-bottom: 15px;
  }

  .team-single-details {
    margin-bottom: 15px;
  }

  .taem-btm-grid-wrap {
    grid-template-columns: 1fr;
  }

  .testimonial-wrap {
    margin-top: 40px;
  }

  .testimonial-title {
    margin-bottom: 15px;
  }

  .testimonial-all-wrap {
    padding: 30px 20px;
  }

  .testimonial-tabs-menu {
    margin-top: 30px;
  }

  .testimonial-star {
    margin-top: 20px;
    margin-bottom: 15px;
  }

  .hero-wrap {
    margin-top: 50px;
  }

  .hero-title {
    font-size: var(--_typography---h2--font-size);
    margin-bottom: 15px;
  }

  .hero-btn-wrap {
    margin-top: 30px;
  }

  .tricker-single-image {
    width: 150px;
  }

  .feature-v1-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    margin-top: 40px;
  }

  .feature-v1-title {
    margin-bottom: 15px;
  }

  .feature-v1-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    grid-template-columns: 1.71fr;
  }

  .feature-v1-grid-wrap._02 {
    grid-template-columns: 1fr;
  }

  .feature-v1-left-wrap {
    grid-column-gap: 25px;
    grid-row-gap: 25px;
    padding: 20px;
  }

  .feature-v1-right-wrap {
    padding: 20px;
  }

  .feature-v1-details-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
  }

  .work-v1-wrap {
    margin-top: 40px;
  }

  .work-v1-grid {
    grid-column-gap: 25px;
    grid-row-gap: 25px;
    grid-template-columns: 1fr;
  }

  .work-v1-left-wrap {
    position: static;
  }

  .work-v1-right-flex-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .work-v1-single-wrap {
    height: 380px;
    padding: 25px;
  }

  .work-left-line-wrap {
    display: none;
  }

  .work-all-item-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
  }

  .advantage-grid-wrap {
    grid-column-gap: 25px;
    grid-row-gap: 25px;
  }

  .advantage-left-wrap {
    grid-column-gap: 25px;
    grid-row-gap: 25px;
    padding: 25px;
  }

  .advantage-list-wrapper {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    margin-top: 25px;
    margin-bottom: 25px;
  }

  .advantage-list-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .advantage-list-details-wrap {
    grid-column-gap: 12px;
    grid-row-gap: 12px;
  }

  .advantage-left-details {
    margin-bottom: 15px;
  }

  .blog-v1-wrap {
    margin-top: 40px;
  }

  .blog-v1-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .blog-v1-cl-list {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    grid-template-columns: 1fr;
  }

  .blog-v1-author-wrapper {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    margin-top: 15px;
    margin-bottom: 15px;
  }

  .blog-v1-right-cl-list {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    grid-template-columns: 1fr;
  }

  .feature-title {
    margin-bottom: 15px;
  }

  .feature-btn-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    margin-top: 25px;
  }

  .feature-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    margin-top: 60px;
  }

  .feature-bottom-wrap {
    grid-template-columns: 1fr;
    padding: 30px 20px;
  }

  .featuer-review-wrap {
    margin-top: 25px;
    padding-left: 15px;
  }

  .process-grid-wrap {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
  }

  .process-title {
    margin-bottom: 15px;
  }

  .process-list-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    margin-top: 20px;
    margin-bottom: 25px;
  }

  .complex-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    grid-template-columns: 1fr;
  }

  .complex-right-grid-wrap {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
    margin-top: 40px;
  }

  .complex-single-title {
    margin-top: 15px;
    margin-bottom: 15px;
  }

  .integration-wrap {
    margin-top: 40px;
    padding-top: 40px;
  }

  .code-wrap {
    margin-top: 30px;
    margin-bottom: 30px;
  }

  .code-all-wrap {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
    padding-top: 25px;
    padding-bottom: 25px;
    padding-left: 25px;
  }

  .code-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .benefit-wrap {
    margin-top: 40px;
  }

  .benefit-grid-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    grid-template-columns: 1fr;
  }

  .benefit-single-wrap {
    padding: 25px 20px;
  }

  .benefits-icon-wrap {
    width: 90px;
    height: 90px;
  }

  .benefit-single-icon {
    width: 36px;
  }

  .benefits-single-title {
    margin-top: 25px;
    margin-bottom: 15px;
  }

  .hero-v2-title {
    margin-top: 15px;
    margin-bottom: 15px;
  }

  .hero-v2-btn-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    margin-top: 25px;
    margin-bottom: 25px;
  }

  .hero-v2-image {
    height: 400px;
  }

  .about-v2-wrapper {
    padding: 25px 20px;
  }

  .about-v2-wrap {
    margin-top: 40px;
  }

  .about-v2-details {
    display: block;
  }

  .about-v2-grid-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    grid-template-columns: 1fr;
  }

  .about-v2-single-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    padding: 20px;
  }

  .about-v2-single-item {
    grid-column-gap: 10px;
    grid-row-gap: 10px;
  }

  .feature-v2-wrap {
    margin-top: 40px;
  }

  .feature-v2-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    grid-template-columns: 1.71fr;
    align-items: stretch;
  }

  .feature-v2-grid-wraps {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    grid-template-columns: 1.71fr;
  }

  .work-v2-wrap {
    margin-top: 40px;
    position: static;
  }

  .work-v2-nb-grid-wrap {
    grid-column-gap: 25px;
    grid-row-gap: 25px;
    grid-template-columns: 1fr;
    display: none;
  }

  .work-v2-nb-single-wrap._01.disply, .work-v2-nb-single-wrap._02.display, .work-v2-nb-single-wrap._03.display {
    display: flex;
  }

  .work-v2-number-title {
    font-size: var(--_typography---h5--font-size);
  }

  .work-numbe-line-wrap {
    display: none;
  }

  .work-v2-grid-warp {
    grid-template-columns: 1fr;
    margin-top: 40px;
  }

  .work-height-wrap {
    height: auto;
  }

  .advantage-v2-wrap {
    margin-top: 40px;
  }

  .advantage-v2-grid-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    grid-template-columns: 1fr;
  }

  .advantage-v2-single-wrap {
    padding: 25px;
  }

  .advantage-v2-single-title {
    margin-top: 20px;
    margin-bottom: 15px;
  }

  .blog-v2-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    grid-template-columns: 1fr;
  }

  .blog-v2-author-wrapper {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    margin-top: 15px;
    margin-bottom: 15px;
  }

  .cta-v2-wrapper {
    padding-bottom: 0;
  }

  .cta-v2-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    grid-template-columns: 1.08fr;
  }

  .cta-v2-right-wrap {
    flex-flow: column;
    justify-content: flex-start;
    align-items: flex-start;
    display: flex;
  }

  .cta-v2-right-details {
    margin-bottom: 20px;
  }

  .faq-v2-wrap {
    margin-top: 40px;
  }

  .faq-v2-grid-wap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
  }

  .faq-v2-single-wrap {
    padding-left: 20px;
    padding-right: 20px;
  }

  .faq-v2-tab-menu {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
  }

  .testimonial-v2-wrapper {
    padding: 40px 20px;
  }

  .testimonial-v2-wrap {
    margin-top: 40px;
  }

  .home-v2-hero-wrapper {
    grid-column-gap: 25px;
    grid-row-gap: 25px;
  }

  .testimonial-v2-single-slide {
    height: 350px;
    padding: 30px 20px;
  }

  .testimonial-v2-top, .dropdown-singel-wrapper {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
  }

  .dropdown-title {
    font-size: 18px;
  }

  .dropdown-flex-wrap {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
  }

  .about-content-title-wrapper, .about-content-title-wrapper._01, .about-content-title-wrapper._02 {
    margin-bottom: 25px;
  }

  .about-content-title-wrap {
    display: none;
  }
}

@media screen and (max-width: 479px) {
  body {
    --_typography---body: Poppins, sans-serif;
    --_typography---h6--all-paragraph--p-16: 14px;
    --_typography---all-line-height--line-height-160: 160%;
    --_typography---all-font-weight--font-weight-400: 400;
    --_typography---h1--font-size: 36px;
    --_typography---all-line-height--line-height-130: 130%;
    --_typography---all-font-weight--font-weight-600: 600;
    --_typography---h2--font-size: 32px;
    --_typography---h3--font-size: 28px;
    --_typography---all-line-height--line-height-140: 140%;
    --_typography---h4--font-size: 24px;
    --_typography---all-line-height--line-height-137: 137%;
    --_typography---h5--font-size: 18px;
    --_typography---all-line-height--line-height-150: 150%;
    --_typography---h6--font-size: 14px;
    --_typography---all-line-height--line-height-100: 100%;
    --_typography---all-font-weight--font-weight-500: 500;
    --_typography---all-font-weight--all-border--border-32: 10px;
    --_typography---all-font-weight--all-border--border-40: 40px;
    --_typography---all-font-weight--all-border--border-999: 999px;
    --_typography---all-font-weight--all-border--border-24: 10px;
    --_typography---h6--all-paragraph--p-14: 14px;
    --_typography---h6--all-paragraph--p-12: 12px;
    --_typography---all-font-weight--all-border--border-20: 10px;
    --_typography---all-font-weight--all-border--border-12: 10px;
    --_typography---all-font-weight--all-border--border-10: 10px;
    --_typography---all-font-weight--all-border--border-16: 10px;
    --_typography---all-font-weight--all-border--border-80: 80px;
    --_typography---all-font-weight--all-border--border-60: 60px;
  }

  .nav-menu-wrapper {
    height: 85vh;
  }

  .primary-btn-wrap {
    height: 14px;
  }

  .menu-button {
    width: 35px;
    height: 35px;
  }

  .section {
    padding-top: 60px;
    padding-bottom: 60px;
  }

  .section.style {
    padding-top: 120px;
  }

  .section.footer {
    padding-top: 60px;
    padding-bottom: 20px;
  }

  .section.changelog, .section.privacy, .section.document, .section.contact, .section._404, .section.blog, .section.blog-single, .section.pricing, .section.about, .section.hero, .section.feature {
    padding-top: 120px;
  }

  .section.hero-v2 {
    padding-top: 120px;
    padding-bottom: 40px;
  }

  .style-guide-wrap {
    margin-top: 40px;
  }

  .style-flex-wrap {
    grid-column-gap: 25px;
    grid-row-gap: 25px;
  }

  .style-single-wrap {
    padding: 20px 15px;
  }

  .color-wrapper {
    grid-column-gap: 25px;
    grid-row-gap: 25px;
  }

  .footer-wrapper {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
  }

  .footer-top-wrap {
    grid-column-gap: 25px;
    grid-row-gap: 25px;
  }

  .footer-email-filed {
    padding-left: 15px;
    padding-right: 110px;
  }

  .footer-submit-button {
    padding-left: 25px;
    padding-right: 25px;
  }

  .footer-email-details {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    flex-flow: wrap;
  }

  .footer-left-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
  }

  .footer-right-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 25px;
    grid-template-columns: 1fr 1fr;
  }

  .footer-brand-image {
    width: 140px;
  }

  .footer-single-menu-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
  }

  .footer-all-menu-wrap {
    grid-column-gap: 10px;
    grid-row-gap: 10px;
  }

  .copyright-wrapper {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
  }

  .all-social-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    flex-flow: wrap;
  }

  .license-flex-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .license-single-wrap {
    padding: 20px 15px;
  }

  .license-image-list {
    margin-top: 15px;
    margin-bottom: 20px;
  }

  .license-image-grid {
    grid-template-columns: 1fr;
  }

  .license-single-icon-wrap {
    height: 240px;
  }

  .license-icon-image {
    width: 80px;
  }

  .license-font-wrap {
    padding: 20px 15px;
  }

  .license-font {
    font-size: 64px;
  }

  .changelog-wrap {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
    margin-top: 40px;
  }

  .changelog-grid-wrap {
    grid-column-gap: 25px;
    grid-row-gap: 25px;
  }

  .changelog-list {
    grid-column-gap: 12px;
    grid-row-gap: 12px;
  }

  .chagnelog-image {
    margin-top: 15px;
    margin-bottom: 15px;
  }

  .privacy-wrap {
    margin-top: 40px;
  }

  .privacy-flex-wrap {
    margin-top: 20px;
  }

  .document-wrap {
    margin-top: 40px;
  }

  .document-tabs-content {
    margin-top: 25px;
  }

  .document-single-title {
    margin-bottom: 12px;
  }

  .document-single-details {
    font-size: 14px;
  }

  .document-single-image {
    margin-top: 15px;
  }

  .document-list {
    grid-column-gap: 10px;
    grid-row-gap: 10px;
    font-size: 14px;
  }

  .contact-wrap {
    margin-top: 40px;
  }

  .contact-left-wrap, .contact-right-wrap {
    padding: 20px 15px;
  }

  .contact-filed-wrap {
    grid-column-gap: 10px;
    grid-row-gap: 10px;
  }

  .contact-submit-button {
    margin-top: 30px;
  }

  .contact-single-details {
    margin-top: 20px;
  }

  .contact-all-details-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    margin-top: 20px;
    margin-bottom: 20px;
  }

  ._404-wrapper {
    padding-bottom: 40px;
  }

  ._404-wrap {
    margin-top: 40px;
  }

  ._404-icon {
    width: 120px;
  }

  ._404-title, ._404-button-wrap {
    margin-top: 15px;
  }

  .blog-wrap {
    margin-top: 40px;
  }

  .blog-card-image {
    height: 300px;
  }

  .blog-author-wrapper {
    grid-column-gap: 12px;
    grid-row-gap: 12px;
    margin-top: 12px;
    margin-bottom: 12px;
  }

  .blog-card-link {
    font-size: var(--_typography---h5--font-size);
  }

  .cta-wrapper {
    padding: 20px;
  }

  .cta-details {
    margin-top: 15px;
    margin-bottom: 20px;
  }

  .cta-button-wrap {
    flex-flow: wrap;
  }

  .cta-image-wrap {
    grid-template-columns: 1fr;
  }

  .blog-single-page-wrap {
    grid-column-gap: 4px;
    grid-row-gap: 4px;
  }

  .blog-single-title {
    font-size: var(--_typography---h4--font-size);
  }

  .blog-thimble-image {
    height: 300px;
    margin-top: 20px;
  }

  .blog-rich-text h4 {
    margin-top: 15px;
  }

  .blog-rich-text p, .blog-rich-text ul {
    margin-top: 12px;
  }

  .content-wrap {
    padding-left: 15px;
    padding-right: 15px;
  }

  .content-item-warp {
    grid-column-gap: 4px;
    grid-row-gap: 4px;
  }

  .content-item-link {
    padding: 6px 12px;
  }

  .blog-form-wrapper {
    padding: 15px;
  }

  .more-blog-wrap {
    margin-top: 30px;
  }

  .pricing-wrap {
    margin-top: 40px;
    margin-bottom: 40px;
  }

  .pricing-tab-content {
    margin-top: 30px;
  }

  .pricing-single-wrap {
    padding: 20px;
  }

  .pricing-price-wrap {
    margin-top: 20px;
    margin-bottom: 20px;
  }

  .pricing-list-wrapper {
    margin-bottom: 20px;
  }

  .pricing-list-wrap {
    grid-column-gap: 8px;
    grid-row-gap: 8px;
  }

  .faq-grid-wrap, .faq-right-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .faq-btm-wrap {
    padding: 20px 15px;
  }

  .faq-details {
    margin-bottom: 15px;
  }

  .faq-single-wrap {
    padding-top: 15px;
    padding-bottom: 15px;
  }

  .about-wrap {
    margin-top: 40px;
  }

  .about-title {
    font-size: var(--_typography---h3--font-size);
  }

  .about-image-grid-wrap {
    margin-top: 40px;
    margin-bottom: 25px;
  }

  .story-wrap {
    margin-top: 25px;
  }

  .story-single-wrap {
    padding-left: 15px;
    padding-right: 15px;
  }

  .story-single-title {
    margin-top: 15px;
    margin-bottom: 15px;
  }

  .about-content-wrap {
    padding: 30px 15px;
  }

  .about-content-title {
    font-size: var(--_typography---h6--all-paragraph--p-16);
  }

  .about-stat-wrap {
    grid-column-gap: 10px;
    grid-row-gap: 10px;
    margin-top: 25px;
  }

  .why-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .why-single-wrap {
    padding: 20px 15px;
  }

  .why-single-title, .why-btm-wrap {
    margin-top: 20px;
  }

  .built-grid-wrap {
    grid-column-gap: 30px;
    grid-row-gap: 30px;
  }

  .built-user-wrap {
    flex-flow: wrap;
  }

  .built-right-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .built-single-overflow-wrap {
    height: 38px;
  }

  .team-wrap {
    margin-top: 30px;
  }

  .team-single-details {
    margin-bottom: 12px;
  }

  .testimonial-wrap {
    margin-top: 30px;
  }

  .testimonial-all-wrap {
    padding: 25px 15px;
  }

  .testimonial-tabs-menu {
    margin-top: 20px;
  }

  .testimonial-star {
    margin-top: 15px;
    margin-bottom: 12px;
  }

  .hero-wrap {
    margin-top: 40px;
  }

  .hero-sub-title {
    font-size: 12px;
  }

  .hero-title {
    font-size: var(--_typography---h2--font-size);
  }

  .hero-btn-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    flex-flow: wrap;
    margin-top: 20px;
  }

  .tricker-single-image {
    width: 140px;
  }

  .feature-v1-wrap {
    margin-top: 30px;
  }

  .feature-v1-left-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    padding: 15px;
  }

  .feature-v1-right-wrap {
    padding: 15px;
  }

  .feature-v1-details-wrap {
    grid-column-gap: 10px;
    grid-row-gap: 10px;
  }

  .work-v1-wrap {
    margin-top: 30px;
  }

  .work-v1-grid {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .work-v1-single-wrap {
    height: 350px;
    padding: 20px;
  }

  .advantage-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .advantage-left-wrap {
    grid-column-gap: 2px;
    grid-row-gap: 2px;
    padding: 20px;
  }

  .advantage-list-wrapper {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    margin-top: 20px;
    margin-bottom: 20px;
  }

  .advantage-list-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    flex-flow: column;
    justify-content: flex-start;
    align-items: flex-start;
  }

  .advantage-list-details-wrap {
    grid-column-gap: 10px;
    grid-row-gap: 10px;
  }

  .advantage-btm-wrap {
    flex-flow: wrap;
  }

  .blog-v1-wrap {
    margin-top: 30px;
  }

  .blog-v1-grid-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
  }

  .blog-v1-author-wrapper {
    grid-column-gap: 12px;
    grid-row-gap: 12px;
    margin-top: 12px;
    margin-bottom: 12px;
  }

  .feature-title {
    font-size: var(--_typography---h3--font-size);
  }

  .feature-btn-wrap {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
    flex-flow: wrap;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
  }

  .feature-wrap {
    margin-top: 40px;
  }

  .feature-bottom-wrap {
    padding: 25px 15px;
  }

  .featuer-review-wrap {
    margin-top: 20px;
  }

  .process-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .process-list-wrap {
    grid-column-gap: 12px;
    grid-row-gap: 12px;
    margin-top: 15px;
    margin-bottom: 20px;
  }

  .complex-right-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    grid-template-columns: 1fr;
    margin-top: 30px;
  }

  .integration-wrap {
    margin-top: 30px;
    padding-top: 30px;
  }

  .code-wrap {
    margin-top: 25px;
    margin-bottom: 25px;
  }

  .code-all-wrap {
    grid-column-gap: 25px;
    grid-row-gap: 25px;
    padding: 20px;
  }

  .code-single-image {
    height: 160px;
  }

  .code-grid-wrap {
    grid-template-columns: 1fr;
  }

  .benefit-wrap {
    margin-top: 30px;
  }

  .benefit-single-wrap {
    padding: 20px 15px;
  }

  .benefits-icon-wrap {
    width: 80px;
    height: 80px;
  }

  .benefit-single-icon {
    width: 32px;
  }

  .benefits-single-title {
    margin-top: 20px;
  }

  .hero-v2-btn-wrap {
    flex-flow: wrap;
    margin-top: 20px;
    margin-bottom: 20px;
  }

  .hero-v2-image {
    height: 300px;
  }

  .about-v2-wrapper {
    padding: 20px 15px;
  }

  .about-v2-wrap {
    margin-top: 30px;
  }

  .about-v2-single-wrap {
    padding: 15px;
  }

  .feature-v2-wrap {
    margin-top: 30px;
  }

  .feature-v2-grid-wrap, .feature-v2-grid-wraps {
    grid-column-gap: 15px;
    grid-row-gap: 15px;
  }

  .work-v2-wrap {
    margin-top: 30px;
  }

  .work-v2-nb-grid-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .work-v2-grid-warp, .advantage-v2-wrap {
    margin-top: 30px;
  }

  .advantage-v2-single-wrap {
    padding: 20px;
  }

  .advantage-v2-single-title {
    margin-top: 15px;
  }

  .blog-v2-author-wrapper {
    grid-column-gap: 12px;
    grid-row-gap: 12px;
    flex-flow: wrap;
    margin-top: 12px;
    margin-bottom: 12px;
  }

  .cta-v2-wrapper {
    padding-top: 30px;
    padding-left: 15px;
    padding-right: 15px;
  }

  .cta-v2-right-details {
    margin-top: 15px;
    margin-bottom: 15px;
  }

  .faq-v2-wrap {
    margin-top: 30px;
  }

  .faq-v2-single-wrap {
    padding: 15px;
  }

  .testimonial-v2-wrapper {
    padding: 30px 15px;
  }

  .testimonial-v2-wrap {
    margin-top: 30px;
  }

  .home-v2-hero-wrapper {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .testimonial-v2-single-slide {
    height: 420px;
    padding: 25px 15px;
  }

  .testimonial-v2-btm {
    flex-flow: column;
    justify-content: flex-start;
    align-items: flex-start;
  }

  .dropdown-singel-text {
    font-size: 15px;
  }

  .dropdown-title {
    font-size: 17px;
  }

  .dropdown-menu-wrapper {
    grid-column-gap: 13px;
    grid-row-gap: 13px;
  }

  .dropdown-flex-wrap {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .about-content-title-wrapper {
    font-size: var(--_typography---h6--all-paragraph--p-16);
    margin-bottom: 20px;
  }

  .about-content-title-wrapper._01, .about-content-title-wrapper._02 {
    margin-bottom: 20px;
  }
}

@media screen and (max-width: 991px) {
  #w-node-acd6ef40-83e5-ef8e-8bc4-a2d3cf8a6f8e-9efc703c {
    order: -9999;
  }
}

@media screen and (max-width: 767px) {
  #w-node-e64162fe-e502-b4e1-2119-249b9e38d75c-a54625a0, #w-node-abaf87c3-01a0-ebd0-58ab-96a51577f4c8-bb3a8cb6 {
    order: -9999;
  }

  #w-node-deca4871-ce32-d41e-a17d-46da0a0c4998-72e857b0 {
    grid-area: span 1 / span 1 / span 1 / span 1;
  }
}
