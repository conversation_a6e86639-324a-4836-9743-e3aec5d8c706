"use strict";(self.webpackChunk=self.webpackChunk||[]).push([["471"],{9078:function(t,e,a){var r=a(3949),n=a(5134);r.define("tabs",t.exports=function(t){var e,a,i={},s=t.tram,o=t(document),l=r.env,d=l.safari,c=l(),u="data-w-tab",f=".w-tabs",h="w--current",b="w--tab-active",p=n.triggers,w=!1;function v(){a=c&&r.env("design"),(e=o.find(f)).length&&(e.each(k),r.env("preview")&&!w&&e.each(g),m(),r.redraw.on(i.redraw))}function m(){r.redraw.off(i.redraw)}function g(e,a){var r=t.data(a,f);r&&(r.links&&r.links.each(p.reset),r.panes&&r.panes.each(p.reset))}function k(e,r){var n=f.substr(1)+"-"+e,i=t(r),s=t.data(r,f);if(s||(s=t.data(r,f,{el:i,config:{}})),s.current=null,s.tabIdentifier=n+"-"+u,s.paneIdentifier=n+"-data-w-pane",s.menu=i.children(".w-tab-menu"),s.links=s.menu.children(".w-tab-link"),s.content=i.children(".w-tab-content"),s.panes=s.content.children(".w-tab-pane"),s.el.off(f),s.links.off(f),s.menu.attr("role","tablist"),s.links.attr("tabindex","-1"),(l={}).easing=(o=s).el.attr("data-easing")||"ease",d=l.intro=(d=parseInt(o.el.attr("data-duration-in"),10))==d?d:0,c=l.outro=(c=parseInt(o.el.attr("data-duration-out"),10))==c?c:0,l.immediate=!d&&!c,o.config=l,!a){s.links.on("click"+f,(b=s,function(t){t.preventDefault();var e=t.currentTarget.getAttribute(u);e&&A(b,{tab:e})})),s.links.on("keydown"+f,(p=s,function(t){var e,a=(e=p.current,Array.prototype.findIndex.call(p.links,t=>t.getAttribute(u)===e,null)),r=t.key,n={ArrowLeft:a-1,ArrowUp:a-1,ArrowRight:a+1,ArrowDown:a+1,End:p.links.length-1,Home:0};if(r in n){t.preventDefault();var i=n[r];-1===i&&(i=p.links.length-1),i===p.links.length&&(i=0);var s=p.links[i].getAttribute(u);s&&A(p,{tab:s})}}));var o,l,d,c,b,p,w=s.links.filter("."+h).attr(u);w&&A(s,{tab:w,immediate:!0})}}function A(e,a){a=a||{};var n,i=e.config,o=i.easing,l=a.tab;if(l!==e.current){e.current=l,e.links.each(function(r,s){var o=t(s);if(a.immediate||i.immediate){var d=e.panes[r];s.id||(s.id=e.tabIdentifier+"-"+r),d.id||(d.id=e.paneIdentifier+"-"+r),s.href="#"+d.id,s.setAttribute("role","tab"),s.setAttribute("aria-controls",d.id),s.setAttribute("aria-selected","false"),d.setAttribute("role","tabpanel"),d.setAttribute("aria-labelledby",s.id)}s.getAttribute(u)===l?(n=s,o.addClass(h).removeAttr("tabindex").attr({"aria-selected":"true"}).each(p.intro)):o.hasClass(h)&&o.removeClass(h).attr({tabindex:"-1","aria-selected":"false"}).each(p.outro)});var c=[],f=[];e.panes.each(function(e,a){var r=t(a);a.getAttribute(u)===l?c.push(a):r.hasClass(b)&&f.push(a)});var v=t(c),m=t(f);if(a.immediate||i.immediate){v.addClass(b).each(p.intro),m.removeClass(b),w||r.redraw.up();return}var g=window.scrollX,k=window.scrollY;n.focus(),window.scrollTo(g,k),m.length&&i.outro?(m.each(p.outro),s(m).add("opacity "+i.outro+"ms "+o,{fallback:d}).start({opacity:0}).then(()=>y(i,m,v))):y(i,m,v)}}function y(t,e,a){if(e.removeClass(b).css({opacity:"",transition:"",transform:"",width:"",height:""}),a.addClass(b).each(p.intro),r.redraw.up(),!t.intro)return s(a).set({opacity:1});s(a).set({opacity:0}).redraw().add("opacity "+t.intro+"ms "+t.easing,{fallback:d}).start({opacity:1})}return i.ready=i.design=i.preview=v,i.redraw=function(){w=!0,v(),w=!1},i.destroy=function(){(e=o.find(f)).length&&(e.each(g),m())},i})}}]);