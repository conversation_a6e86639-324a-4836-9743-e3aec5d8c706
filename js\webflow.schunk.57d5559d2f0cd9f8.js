(self.webpackChunk=self.webpackChunk||[]).push([["910"],{1361:function(t){var e=4,i=.001,r=1e-7,s=10,n=11,a=.1,o="function"==typeof Float32Array;function h(t,e){return 1-3*e+3*t}function l(t,e){return 3*e-6*t}function p(t){return 3*t}function f(t,e,i){return((h(e,i)*t+l(e,i))*t+p(e))*t}function u(t,e,i){return 3*h(e,i)*t*t+2*l(e,i)*t+p(e)}function c(t,e,i,n,a){var o,h,l=0;do(o=f(h=e+(i-e)/2,n,a)-t)>0?i=h:e=h;while(Math.abs(o)>r&&++l<s);return h}function m(t,i,r,s){for(var n=0;n<e;++n){var a=u(i,r,s);if(0===a)break;var o=f(i,r,s)-t;i-=o/a}return i}t.exports=function(t,e,r,s){if(!(0<=t&&t<=1&&0<=r&&r<=1))throw Error("bezier x values must be in [0, 1] range");var h=o?new Float32Array(n):Array(n);if(t!==e||r!==s)for(var l=0;l<n;++l)h[l]=f(l*a,t,r);function p(e){for(var s=0,o=1,l=n-1;o!==l&&h[o]<=e;++o)s+=a;var p=s+(e-h[--o])/(h[o+1]-h[o])*a,f=u(p,t,r);return f>=i?m(e,p,t,r):0===f?p:c(e,s,s+a,t,r)}return function(i){return t===e&&r===s?i:0===i?0:1===i?1:f(p(i),e,s)}}},8439:function(t,e,i){var r=i(3442),s=i(3422),n=i(8295),a=r.TypeError;t.exports=function(t){if(s(t))return t;throw a(n(t)+" is not a function")}},7759:function(t,e,i){var r=i(3608),s=i(9638),n=i(5827),a=r("unscopables"),o=Array.prototype;void 0==o[a]&&n.f(o,a,{configurable:!0,value:s(null)}),t.exports=function(t){o[a][t]=!0}},287:function(t,e,i){var r=i(3442),s=i(3292),n=r.String,a=r.TypeError;t.exports=function(t){if(s(t))return t;throw a(n(t)+" is not an object")}},9373:function(t,e,i){"use strict";var r=i(4026),s=i(2631),n=i(8281),a=i(6990),o=i(958),h=i(306),l=i(5963),p=i(7755),f=i(9642),u=i(3608),c=i(7769),m=i(3488).toArray,d=u("asyncIterator"),g=p("Array").values;t.exports=function(t){var e=this,i=arguments.length,p=i>1?arguments[1]:void 0,u=i>2?arguments[2]:void 0;return new(f("Promise"))(function(i){var f=s(t);void 0!==p&&(p=r(p,u));var v=l(f,d),y=v?void 0:h(f)||g,b=n(e)?new e:[];i(m(v?a(f,v):new c(o(f,y)),p,b))})}},4885:function(t){t.exports=function(t,e){for(var i=0,r=e.length,s=new t(r);r>i;)s[i]=e[i++];return s}},4655:function(t,e,i){var r=i(3442),s=i(4026),n=i(6201),a=i(5249),o=i(2631),h=i(5024),l=i(7218),p=i(9638),f=i(4885),u=r.Array,c=n([].push);t.exports=function(t,e,i,r){for(var n,m,d,g=o(t),v=a(g),y=s(e,i),b=p(null),x=l(v),_=0;x>_;_++)(m=h(y(d=v[_],_,g)))in b?c(b[m],d):b[m]=[d];if(r&&(n=r(g))!==u)for(m in b)b[m]=f(n,b[m]);return b}},6030:function(t,e,i){var r=i(4920),s=i(9892),n=i(7218),a=function(t){return function(e,i,a){var o,h=r(e),l=n(h),p=s(a,l);if(t&&i!=i){for(;l>p;)if((o=h[p++])!=o)return!0}else for(;l>p;p++)if((t||p in h)&&h[p]===i)return t||p||0;return!t&&-1}};t.exports={includes:a(!0),indexOf:a(!1)}},3539:function(t,e,i){var r=i(4026),s=i(6201),n=i(5249),a=i(2631),o=i(7218),h=i(8004),l=s([].push),p=function(t){var e=1==t,i=2==t,s=3==t,p=4==t,f=6==t,u=7==t,c=5==t||f;return function(m,d,g,v){for(var y,b,x=a(m),_=n(x),A=r(d,g),k=o(_),w=0,P=v||h,C=e?P(m,k):i||u?P(m,0):void 0;k>w;w++)if((c||w in _)&&(b=A(y=_[w],w,x),t))if(e)C[w]=b;else if(b)switch(t){case 3:return!0;case 5:return y;case 6:return w;case 2:l(C,y)}else switch(t){case 4:return!1;case 7:l(C,y)}return f?-1:s||p?p:C}};t.exports={forEach:p(0),map:p(1),filter:p(2),some:p(3),every:p(4),find:p(5),findIndex:p(6),filterReject:p(7)}},1924:function(t,e,i){var r=i(3442),s=i(6937),n=i(8281),a=i(3292),o=i(3608)("species"),h=r.Array;t.exports=function(t){var e;return s(t)&&(n(e=t.constructor)&&(e===h||s(e.prototype))?e=void 0:a(e)&&null===(e=e[o])&&(e=void 0)),void 0===e?h:e}},8004:function(t,e,i){var r=i(1924);t.exports=function(t,e){return new(r(t))(0===e?0:e)}},9022:function(t,e,i){"use strict";var r=i(9642),s=i(6201),n=i(8439),a=i(7218),o=i(2631),h=i(8004),l=r("Map"),p=l.prototype,f=s(p.forEach),u=s(p.has),c=s(p.set),m=s([].push);t.exports=function(t){var e,i,r,s=o(this),p=a(s),d=h(s,0),g=new l,v=null!=t?n(t):function(t){return t};for(e=0;e<p;e++)u(g,r=v(i=s[e]))||c(g,r,i);return f(g,function(t){m(d,t)}),d}},7769:function(t,e,i){"use strict";var r=i(1168),s=i(287),n=i(9638),a=i(5963),o=i(2275),h=i(5723),l=i(9642),p=i(8419),f=l("Promise"),u=h.set,c=h.get,m=function(t,e,i){var r=t.done;f.resolve(t.value).then(function(t){e({done:r,value:t})},i)},d=function(t){u(this,{iterator:s(t),next:t.next})};d.prototype=o(n(p),{next:function(t){var e=c(this),i=!!arguments.length;return new f(function(n,a){m(s(r(e.next,e.iterator,i?[t]:[])),n,a)})},return:function(t){var e=c(this).iterator,i=!!arguments.length;return new f(function(n,o){var h=a(e,"return");if(void 0===h)return n({done:!0,value:t});m(s(r(h,e,i?[t]:[])),n,o)})},throw:function(t){var e=c(this).iterator,i=!!arguments.length;return new f(function(n,o){var h=a(e,"throw");if(void 0===h)return o(t);m(s(r(h,e,i?[t]:[])),n,o)})}}),t.exports=d},3488:function(t,e,i){"use strict";var r=i(3442),s=i(3702),n=i(8439),a=i(287),o=i(9642),h=i(5963),l=0x1fffffffffffff,p=r.TypeError,f=function(t){var e=0==t,i=1==t,r=2==t,f=3==t;return function(t,u,c){a(t);var m=o("Promise"),d=n(t.next),g=0,v=void 0!==u;return(v||!e)&&n(u),new m(function(n,o){var y=function(e,i){try{var r=h(t,"return");if(r)return m.resolve(s(r,t)).then(function(){e(i)},function(t){o(t)})}catch(t){return o(t)}e(i)},b=function(t){y(o,t)},x=function(){try{if(e&&g>l&&v)throw p("The allowed number of iterations has been exceeded");m.resolve(a(s(d,t))).then(function(t){try{if(a(t).done)e?(c.length=g,n(c)):n(!f&&(r||void 0));else{var s=t.value;v?m.resolve(e?u(s,g):u(s)).then(function(t){i?x():r?t?x():y(n,!1):e?(c[g++]=t,x()):t?y(n,f||s):x()},b):(c[g++]=s,x())}}catch(t){b(t)}},b)}catch(t){b(t)}};x()})}};t.exports={toArray:f(0),forEach:f(1),every:f(2),some:f(3),find:f(4)}},8419:function(t,e,i){var r,s,n=i(3442),a=i(1219),o=i(3422),h=i(9638),l=i(327),p=i(8238),f=i(3608),u=i(3581),c="USE_FUNCTION_CONSTRUCTOR",m=f("asyncIterator"),d=n.AsyncIterator,g=a.AsyncIteratorPrototype;if(g)r=g;else if(o(d))r=d.prototype;else if(a[c]||n[c])try{s=l(l(l(Function("return async function*(){}()")()))),l(s)===Object.prototype&&(r=s)}catch(t){}r?u&&(r=h(r)):r={},o(r[m])||p(r,m,function(){return this}),t.exports=r},9674:function(t,e,i){var r=i(6201),s=r({}.toString),n=r("".slice);t.exports=function(t){return n(s(t),8,-1)}},229:function(t,e,i){var r=i(3442),s=i(4756),n=i(3422),a=i(9674),o=i(3608)("toStringTag"),h=r.Object,l="Arguments"==a(function(){return arguments}()),p=function(t,e){try{return t[e]}catch(t){}};t.exports=s?a:function(t){var e,i,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(i=p(e=h(t),o))?i:l?a(e):"Object"==(r=a(e))&&n(e.callee)?"Arguments":r}},5111:function(t,e,i){var r=i(4831),s=i(8620),n=i(5722),a=i(5827);t.exports=function(t,e){for(var i=s(e),o=a.f,h=n.f,l=0;l<i.length;l++){var p=i[l];r(t,p)||o(t,p,h(e,p))}}},6738:function(t,e,i){t.exports=!i(4198)(function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})},9756:function(t,e,i){var r=i(4562),s=i(5827),n=i(8821);t.exports=r?function(t,e,i){return s.f(t,e,n(1,i))}:function(t,e,i){return t[e]=i,t}},8821:function(t){t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},4562:function(t,e,i){t.exports=!i(4198)(function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})},4459:function(t,e,i){var r=i(3442),s=i(3292),n=r.document,a=s(n)&&s(n.createElement);t.exports=function(t){return a?n.createElement(t):{}}},9841:function(t,e,i){t.exports=i(9642)("navigator","userAgent")||""},4733:function(t,e,i){var r,s,n=i(3442),a=i(9841),o=n.process,h=n.Deno,l=o&&o.versions||h&&h.version,p=l&&l.v8;p&&(s=(r=p.split("."))[0]>0&&r[0]<4?1:+(r[0]+r[1])),!s&&a&&(!(r=a.match(/Edge\/(\d+)/))||r[1]>=74)&&(r=a.match(/Chrome\/(\d+)/))&&(s=+r[1]),t.exports=s},7755:function(t,e,i){var r=i(3442);t.exports=function(t){return r[t].prototype}},5906:function(t){t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},5545:function(t,e,i){var r=i(3442),s=i(5722).f,n=i(9756),a=i(8238),o=i(4669),h=i(5111),l=i(8387);t.exports=function(t,e){var i,p,f,u,c,m=t.target,d=t.global,g=t.stat;if(i=d?r:g?r[m]||o(m,{}):(r[m]||{}).prototype)for(p in e){if(u=e[p],f=t.noTargetGet?(c=s(i,p))&&c.value:i[p],!l(d?p:m+(g?".":"#")+p,t.forced)&&void 0!==f){if(typeof u==typeof f)continue;h(u,f)}(t.sham||f&&f.sham)&&n(u,"sham",!0),a(i,p,u,t)}}},4198:function(t){t.exports=function(t){try{return!!t()}catch(t){return!0}}},1168:function(t){var e=Function.prototype,i=e.apply,r=e.bind,s=e.call;t.exports="object"==typeof Reflect&&Reflect.apply||(r?s.bind(i):function(){return s.apply(i,arguments)})},4026:function(t,e,i){var r=i(6201),s=i(8439),n=r(r.bind);t.exports=function(t,e){return s(t),void 0===e?t:n?n(t,e):function(){return t.apply(e,arguments)}}},3702:function(t){var e=Function.prototype.call;t.exports=e.bind?e.bind(e):function(){return e.apply(e,arguments)}},7101:function(t,e,i){var r=i(4562),s=i(4831),n=Function.prototype,a=r&&Object.getOwnPropertyDescriptor,o=s(n,"name"),h=o&&"something"===(function(){}).name,l=o&&(!r||r&&a(n,"name").configurable);t.exports={EXISTS:o,PROPER:h,CONFIGURABLE:l}},6201:function(t){var e=Function.prototype,i=e.bind,r=e.call,s=i&&i.bind(r);t.exports=i?function(t){return t&&s(r,t)}:function(t){return t&&function(){return r.apply(t,arguments)}}},6990:function(t,e,i){var r=i(3702),s=i(7769),n=i(287),a=i(958),o=i(5963),h=i(3608)("asyncIterator");t.exports=function(t,e){var i=arguments.length<2?o(t,h):e;return i?n(r(i,t)):new s(a(t))}},9642:function(t,e,i){var r=i(3442),s=i(3422),n=function(t){return s(t)?t:void 0};t.exports=function(t,e){return arguments.length<2?n(r[t]):r[t]&&r[t][e]}},306:function(t,e,i){var r=i(229),s=i(5963),n=i(3759),a=i(3608)("iterator");t.exports=function(t){if(void 0!=t)return s(t,a)||s(t,"@@iterator")||n[r(t)]}},958:function(t,e,i){var r=i(3442),s=i(3702),n=i(8439),a=i(287),o=i(8295),h=i(306),l=r.TypeError;t.exports=function(t,e){var i=arguments.length<2?h(t):e;if(n(i))return a(s(i,t));throw l(o(t)+" is not iterable")}},5963:function(t,e,i){var r=i(8439);t.exports=function(t,e){var i=t[e];return null==i?void 0:r(i)}},3442:function(t,e,i){var r=function(t){return t&&t.Math==Math&&t};t.exports=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof i.g&&i.g)||function(){return this}()||Function("return this")()},4831:function(t,e,i){var r=i(6201),s=i(2631),n=r({}.hasOwnProperty);t.exports=Object.hasOwn||function(t,e){return n(s(t),e)}},2511:function(t){t.exports={}},5231:function(t,e,i){t.exports=i(9642)("document","documentElement")},8907:function(t,e,i){var r=i(4562),s=i(4198),n=i(4459);t.exports=!r&&!s(function(){return 7!=Object.defineProperty(n("div"),"a",{get:function(){return 7}}).a})},5249:function(t,e,i){var r=i(3442),s=i(6201),n=i(4198),a=i(9674),o=r.Object,h=s("".split);t.exports=n(function(){return!o("z").propertyIsEnumerable(0)})?function(t){return"String"==a(t)?h(t,""):o(t)}:o},705:function(t,e,i){var r=i(6201),s=i(3422),n=i(1219),a=r(Function.toString);s(n.inspectSource)||(n.inspectSource=function(t){return a(t)}),t.exports=n.inspectSource},5723:function(t,e,i){var r,s,n,a=i(1585),o=i(3442),h=i(6201),l=i(3292),p=i(9756),f=i(4831),u=i(1219),c=i(5511),m=i(2511),d="Object already initialized",g=o.TypeError,v=o.WeakMap,y=function(t){return n(t)?s(t):r(t,{})},b=function(t){return function(e){var i;if(!l(e)||(i=s(e)).type!==t)throw g("Incompatible receiver, "+t+" required");return i}};if(a||u.state){var x=u.state||(u.state=new v),_=h(x.get),A=h(x.has),k=h(x.set);r=function(t,e){if(A(x,t))throw new g(d);return e.facade=t,k(x,t,e),e},s=function(t){return _(x,t)||{}},n=function(t){return A(x,t)}}else{var w=c("state");m[w]=!0,r=function(t,e){if(f(t,w))throw new g(d);return e.facade=t,p(t,w,e),e},s=function(t){return f(t,w)?t[w]:{}},n=function(t){return f(t,w)}}t.exports={set:r,get:s,has:n,enforce:y,getterFor:b}},6937:function(t,e,i){var r=i(9674);t.exports=Array.isArray||function(t){return"Array"==r(t)}},3422:function(t){t.exports=function(t){return"function"==typeof t}},8281:function(t,e,i){var r=i(6201),s=i(4198),n=i(3422),a=i(229),o=i(9642),h=i(705),l=function(){},p=[],f=o("Reflect","construct"),u=/^\s*(?:class|function)\b/,c=r(u.exec),m=!u.exec(l),d=function(t){if(!n(t))return!1;try{return f(l,p,t),!0}catch(t){return!1}},g=function(t){if(!n(t))return!1;switch(a(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}return m||!!c(u,h(t))};t.exports=!f||s(function(){var t;return d(d.call)||!d(Object)||!d(function(){t=!0})||t})?g:d},8387:function(t,e,i){var r=i(4198),s=i(3422),n=/#|\.prototype\./,a=function(t,e){var i=h[o(t)];return i==p||i!=l&&(s(e)?r(e):!!e)},o=a.normalize=function(t){return String(t).replace(n,".").toLowerCase()},h=a.data={},l=a.NATIVE="N",p=a.POLYFILL="P";t.exports=a},3292:function(t,e,i){var r=i(3422);t.exports=function(t){return"object"==typeof t?null!==t:r(t)}},3581:function(t){t.exports=!1},8924:function(t,e,i){var r=i(3442),s=i(9642),n=i(3422),a=i(9914),o=i(2233),h=r.Object;t.exports=o?function(t){return"symbol"==typeof t}:function(t){var e=s("Symbol");return n(e)&&a(e.prototype,h(t))}},3759:function(t){t.exports={}},7218:function(t,e,i){var r=i(5248);t.exports=function(t){return r(t.length)}},5555:function(t,e,i){var r=i(4733),s=i(4198);t.exports=!!Object.getOwnPropertySymbols&&!s(function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&r&&r<41})},1585:function(t,e,i){var r=i(3442),s=i(3422),n=i(705),a=r.WeakMap;t.exports=s(a)&&/native code/.test(n(a))},9638:function(t,e,i){var r,s=i(287),n=i(3561),a=i(5906),o=i(2511),h=i(5231),l=i(4459),p=i(5511),f=">",u="<",c="prototype",m="script",d=p("IE_PROTO"),g=function(){},v=function(t){return u+m+f+t+u+"/"+m+f},y=function(t){t.write(v("")),t.close();var e=t.parentWindow.Object;return t=null,e},b=function(){var t,e=l("iframe"),i="java"+m+":";return e.style.display="none",h.appendChild(e),e.src=String(i),(t=e.contentWindow.document).open(),t.write(v("document.F=Object")),t.close(),t.F},x=function(){try{r=new ActiveXObject("htmlfile")}catch(t){}x="undefined"!=typeof document?document.domain&&r?y(r):b():y(r);for(var t=a.length;t--;)delete x[c][a[t]];return x()};o[d]=!0,t.exports=Object.create||function(t,e){var i;return null!==t?(g[c]=s(t),i=new g,g[c]=null,i[d]=t):i=x(),void 0===e?i:n(i,e)}},3561:function(t,e,i){var r=i(4562),s=i(5827),n=i(287),a=i(4920),o=i(2475);t.exports=r?Object.defineProperties:function(t,e){n(t);for(var i,r=a(e),h=o(e),l=h.length,p=0;l>p;)s.f(t,i=h[p++],r[i]);return t}},5827:function(t,e,i){var r=i(3442),s=i(4562),n=i(8907),a=i(287),o=i(5024),h=r.TypeError,l=Object.defineProperty;e.f=s?l:function(t,e,i){if(a(t),e=o(e),a(i),n)try{return l(t,e,i)}catch(t){}if("get"in i||"set"in i)throw h("Accessors not supported");return"value"in i&&(t[e]=i.value),t}},5722:function(t,e,i){var r=i(4562),s=i(3702),n=i(9626),a=i(8821),o=i(4920),h=i(5024),l=i(4831),p=i(8907),f=Object.getOwnPropertyDescriptor;e.f=r?f:function(t,e){if(t=o(t),e=h(e),p)try{return f(t,e)}catch(t){}if(l(t,e))return a(!s(n.f,t,e),t[e])}},4671:function(t,e,i){var r=i(8834),s=i(5906).concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return r(t,s)}},1089:function(t,e){e.f=Object.getOwnPropertySymbols},327:function(t,e,i){var r=i(3442),s=i(4831),n=i(3422),a=i(2631),o=i(5511),h=i(6738),l=o("IE_PROTO"),p=r.Object,f=p.prototype;t.exports=h?p.getPrototypeOf:function(t){var e=a(t);if(s(e,l))return e[l];var i=e.constructor;return n(i)&&e instanceof i?i.prototype:e instanceof p?f:null}},9914:function(t,e,i){t.exports=i(6201)({}.isPrototypeOf)},8834:function(t,e,i){var r=i(6201),s=i(4831),n=i(4920),a=i(6030).indexOf,o=i(2511),h=r([].push);t.exports=function(t,e){var i,r=n(t),l=0,p=[];for(i in r)!s(o,i)&&s(r,i)&&h(p,i);for(;e.length>l;)s(r,i=e[l++])&&(~a(p,i)||h(p,i));return p}},2475:function(t,e,i){var r=i(8834),s=i(5906);t.exports=Object.keys||function(t){return r(t,s)}},9626:function(t,e){"use strict";var i={}.propertyIsEnumerable,r=Object.getOwnPropertyDescriptor;e.f=r&&!i.call({1:2},1)?function(t){var e=r(this,t);return!!e&&e.enumerable}:i},9295:function(t,e,i){var r=i(3442),s=i(3702),n=i(3422),a=i(3292),o=r.TypeError;t.exports=function(t,e){var i,r;if("string"===e&&n(i=t.toString)&&!a(r=s(i,t))||n(i=t.valueOf)&&!a(r=s(i,t))||"string"!==e&&n(i=t.toString)&&!a(r=s(i,t)))return r;throw o("Can't convert object to primitive value")}},8620:function(t,e,i){var r=i(9642),s=i(6201),n=i(4671),a=i(1089),o=i(287),h=s([].concat);t.exports=r("Reflect","ownKeys")||function(t){var e=n.f(o(t)),i=a.f;return i?h(e,i(t)):e}},2275:function(t,e,i){var r=i(8238);t.exports=function(t,e,i){for(var s in e)r(t,s,e[s],i);return t}},8238:function(t,e,i){var r=i(3442),s=i(3422),n=i(4831),a=i(9756),o=i(4669),h=i(705),l=i(5723),p=i(7101).CONFIGURABLE,f=l.get,u=l.enforce,c=String(String).split("String");(t.exports=function(t,e,i,h){var l,f=!!h&&!!h.unsafe,m=!!h&&!!h.enumerable,d=!!h&&!!h.noTargetGet,g=h&&void 0!==h.name?h.name:e;if(s(i)&&("Symbol("===String(g).slice(0,7)&&(g="["+String(g).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!n(i,"name")||p&&i.name!==g)&&a(i,"name",g),(l=u(i)).source||(l.source=c.join("string"==typeof g?g:""))),t===r)return void(m?t[e]=i:o(e,i));f?!d&&t[e]&&(m=!0):delete t[e],m?t[e]=i:a(t,e,i)})(Function.prototype,"toString",function(){return s(this)&&f(this).source||h(this)})},8369:function(t,e,i){var r=i(3442).TypeError;t.exports=function(t){if(void 0==t)throw r("Can't call method on "+t);return t}},4669:function(t,e,i){var r=i(3442),s=Object.defineProperty;t.exports=function(t,e){try{s(r,t,{value:e,configurable:!0,writable:!0})}catch(i){r[t]=e}return e}},5511:function(t,e,i){var r=i(4147),s=i(6760),n=r("keys");t.exports=function(t){return n[t]||(n[t]=s(t))}},1219:function(t,e,i){var r=i(3442),s=i(4669),n="__core-js_shared__";t.exports=r[n]||s(n,{})},4147:function(t,e,i){var r=i(3581),s=i(1219);(t.exports=function(t,e){return s[t]||(s[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.19.0",mode:r?"pure":"global",copyright:"\xa9 2021 Denis Pushkarev (zloirock.ru)"})},9892:function(t,e,i){var r=i(8063),s=Math.max,n=Math.min;t.exports=function(t,e){var i=r(t);return i<0?s(i+e,0):n(i,e)}},4920:function(t,e,i){var r=i(5249),s=i(8369);t.exports=function(t){return r(s(t))}},8063:function(t){var e=Math.ceil,i=Math.floor;t.exports=function(t){var r=+t;return r!=r||0===r?0:(r>0?i:e)(r)}},5248:function(t,e,i){var r=i(8063),s=Math.min;t.exports=function(t){return t>0?s(r(t),0x1fffffffffffff):0}},2631:function(t,e,i){var r=i(3442),s=i(8369),n=r.Object;t.exports=function(t){return n(s(t))}},194:function(t,e,i){var r=i(3442),s=i(3702),n=i(3292),a=i(8924),o=i(5963),h=i(9295),l=i(3608),p=r.TypeError,f=l("toPrimitive");t.exports=function(t,e){if(!n(t)||a(t))return t;var i,r=o(t,f);if(r){if(void 0===e&&(e="default"),!n(i=s(r,t,e))||a(i))return i;throw p("Can't convert object to primitive value")}return void 0===e&&(e="number"),h(t,e)}},5024:function(t,e,i){var r=i(194),s=i(8924);t.exports=function(t){var e=r(t,"string");return s(e)?e:e+""}},4756:function(t,e,i){var r=i(3608)("toStringTag"),s={};s[r]="z",t.exports="[object z]"===String(s)},8295:function(t,e,i){var r=i(3442).String;t.exports=function(t){try{return r(t)}catch(t){return"Object"}}},6760:function(t,e,i){var r=i(6201),s=0,n=Math.random(),a=r(1..toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+a(++s+n,36)}},2233:function(t,e,i){t.exports=i(5555)&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},3608:function(t,e,i){var r=i(3442),s=i(4147),n=i(4831),a=i(6760),o=i(5555),h=i(2233),l=s("wks"),p=r.Symbol,f=p&&p.for,u=h?p:p&&p.withoutSetter||a;t.exports=function(t){if(!n(l,t)||!(o||"string"==typeof l[t])){var e="Symbol."+t;o&&n(p,t)?l[t]=p[t]:h&&f?l[t]=f(e):l[t]=u(e)}return l[t]}},233:function(t,e,i){"use strict";var r=i(5545),s=i(3539).filterReject,n=i(7759);r({target:"Array",proto:!0},{filterOut:function(t){return s(this,t,arguments.length>1?arguments[1]:void 0)}}),n("filterOut")},9754:function(t,e,i){"use strict";var r=i(5545),s=i(3539).filterReject,n=i(7759);r({target:"Array",proto:!0},{filterReject:function(t){return s(this,t,arguments.length>1?arguments[1]:void 0)}}),n("filterReject")},2897:function(t,e,i){i(5545)({target:"Array",stat:!0},{fromAsync:i(9373)})},971:function(t,e,i){"use strict";var r=i(5545),s=i(4655),n=i(1924),a=i(7759);r({target:"Array",proto:!0},{groupBy:function(t){var e=arguments.length>1?arguments[1]:void 0;return s(this,t,e,n)}}),a("groupBy")},2374:function(t,e,i){var r=i(5545),s=i(6937),n=Object.isFrozen,a=function(t,e){if(!n||!s(t)||!n(t))return!1;for(var i,r=0,a=t.length;r<a;)if(!("string"==typeof(i=t[r++])||e&&void 0===i))return!1;return 0!==a};r({target:"Array",stat:!0},{isTemplateObject:function(t){if(!a(t,!0))return!1;var e=t.raw;return e.length===t.length&&!!a(e,!1)}})},5152:function(t,e,i){"use strict";var r=i(4562),s=i(7759),n=i(2631),a=i(7218),o=i(5827).f;!r||"lastIndex"in[]||(o(Array.prototype,"lastIndex",{configurable:!0,get:function(){var t=a(n(this));return 0==t?0:t-1}}),s("lastIndex"))},5273:function(t,e,i){"use strict";var r=i(4562),s=i(7759),n=i(2631),a=i(7218),o=i(5827).f;!r||"lastItem"in[]||(o(Array.prototype,"lastItem",{configurable:!0,get:function(){var t=n(this),e=a(t);return 0==e?void 0:t[e-1]},set:function(t){var e=n(this),i=a(e);return e[0==i?0:i-1]=t}}),s("lastItem"))},172:function(t,e,i){"use strict";var r=i(5545),s=i(7759);r({target:"Array",proto:!0},{uniqueBy:i(9022)}),s("uniqueBy")},8172:function(t,e,i){t.exports=i(440)(i(5238),"DataView")},1796:function(t,e,i){var r=i(7322),s=i(2937),n=i(207),a=i(2165),o=i(7523);function h(t){var e=-1,i=null==t?0:t.length;for(this.clear();++e<i;){var r=t[e];this.set(r[0],r[1])}}h.prototype.clear=r,h.prototype.delete=s,h.prototype.get=n,h.prototype.has=a,h.prototype.set=o,t.exports=h},4281:function(t,e,i){var r=i(5940),s=i(4382),n=0xffffffff;function a(t){this.__wrapped__=t,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=n,this.__views__=[]}a.prototype=r(s.prototype),a.prototype.constructor=a,t.exports=a},283:function(t,e,i){var r=i(7435),s=i(8438),n=i(3067),a=i(9679),o=i(2426);function h(t){var e=-1,i=null==t?0:t.length;for(this.clear();++e<i;){var r=t[e];this.set(r[0],r[1])}}h.prototype.clear=r,h.prototype.delete=s,h.prototype.get=n,h.prototype.has=a,h.prototype.set=o,t.exports=h},9675:function(t,e,i){function r(t,e){this.__wrapped__=t,this.__actions__=[],this.__chain__=!!e,this.__index__=0,this.__values__=void 0}r.prototype=i(5940)(i(4382).prototype),r.prototype.constructor=r,t.exports=r},9036:function(t,e,i){t.exports=i(440)(i(5238),"Map")},4544:function(t,e,i){var r=i(6409),s=i(5335),n=i(5601),a=i(1533),o=i(151);function h(t){var e=-1,i=null==t?0:t.length;for(this.clear();++e<i;){var r=t[e];this.set(r[0],r[1])}}h.prototype.clear=r,h.prototype.delete=s,h.prototype.get=n,h.prototype.has=a,h.prototype.set=o,t.exports=h},44:function(t,e,i){t.exports=i(440)(i(5238),"Promise")},6656:function(t,e,i){t.exports=i(440)(i(5238),"Set")},3290:function(t,e,i){var r=i(4544),s=i(1760),n=i(5484);function a(t){var e=-1,i=null==t?0:t.length;for(this.__data__=new r;++e<i;)this.add(t[e])}a.prototype.add=a.prototype.push=s,a.prototype.has=n,t.exports=a},1902:function(t,e,i){var r=i(283),s=i(6063),n=i(7727),a=i(3281),o=i(6667),h=i(1270);function l(t){var e=this.__data__=new r(t);this.size=e.size}l.prototype.clear=s,l.prototype.delete=n,l.prototype.get=a,l.prototype.has=o,l.prototype.set=h,t.exports=l},4886:function(t,e,i){t.exports=i(5238).Symbol},8965:function(t,e,i){t.exports=i(5238).Uint8Array},3283:function(t,e,i){t.exports=i(440)(i(5238),"WeakMap")},9198:function(t){t.exports=function(t,e,i){switch(i.length){case 0:return t.call(e);case 1:return t.call(e,i[0]);case 2:return t.call(e,i[0],i[1]);case 3:return t.call(e,i[0],i[1],i[2])}return t.apply(e,i)}},4970:function(t){t.exports=function(t,e){for(var i=-1,r=null==t?0:t.length;++i<r&&!1!==e(t[i],i,t););return t}},2654:function(t){t.exports=function(t,e){for(var i=-1,r=null==t?0:t.length,s=0,n=[];++i<r;){var a=t[i];e(a,i,t)&&(n[s++]=a)}return n}},4979:function(t,e,i){var r=i(1682),s=i(9732),n=i(6377),a=i(6018),o=i(9251),h=i(8586),l=Object.prototype.hasOwnProperty;t.exports=function(t,e){var i=n(t),p=!i&&s(t),f=!i&&!p&&a(t),u=!i&&!p&&!f&&h(t),c=i||p||f||u,m=c?r(t.length,String):[],d=m.length;for(var g in t)(e||l.call(t,g))&&!(c&&("length"==g||f&&("offset"==g||"parent"==g)||u&&("buffer"==g||"byteLength"==g||"byteOffset"==g)||o(g,d)))&&m.push(g);return m}},1098:function(t){t.exports=function(t,e){for(var i=-1,r=null==t?0:t.length,s=Array(r);++i<r;)s[i]=e(t[i],i,t);return s}},5741:function(t){t.exports=function(t,e){for(var i=-1,r=e.length,s=t.length;++i<r;)t[s+i]=e[i];return t}},2607:function(t){t.exports=function(t,e,i,r){var s=-1,n=null==t?0:t.length;for(r&&n&&(i=t[++s]);++s<n;)i=e(i,t[s],s,t);return i}},3955:function(t){t.exports=function(t,e){for(var i=-1,r=null==t?0:t.length;++i<r;)if(e(t[i],i,t))return!0;return!1}},609:function(t,e,i){t.exports=i(2726)("length")},3615:function(t,e,i){var r=i(2676),s=i(4071),n=Object.prototype.hasOwnProperty;t.exports=function(t,e,i){var a=t[e];n.call(t,e)&&s(a,i)&&(void 0!==i||e in t)||r(t,e,i)}},8357:function(t,e,i){var r=i(4071);t.exports=function(t,e){for(var i=t.length;i--;)if(r(t[i][0],e))return i;return -1}},2676:function(t,e,i){var r=i(9833);t.exports=function(t,e,i){"__proto__"==e&&r?r(t,e,{configurable:!0,enumerable:!0,value:i,writable:!0}):t[e]=i}},2009:function(t){t.exports=function(t,e,i){return t==t&&(void 0!==i&&(t=t<=i?t:i),void 0!==e&&(t=t>=e?t:e)),t}},5940:function(t,e,i){var r=i(8532),s=Object.create;t.exports=function(){function t(){}return function(e){if(!r(e))return{};if(s)return s(e);t.prototype=e;var i=new t;return t.prototype=void 0,i}}()},8264:function(t,e,i){var r=i(3406);t.exports=i(2679)(r)},2056:function(t){t.exports=function(t,e,i,r){for(var s=t.length,n=i+(r?1:-1);r?n--:++n<s;)if(e(t[n],n,t))return n;return -1}},5265:function(t,e,i){var r=i(5741),s=i(1668);function n(t,e,i,a,o){var h=-1,l=t.length;for(i||(i=s),o||(o=[]);++h<l;){var p=t[h];e>0&&i(p)?e>1?n(p,e-1,i,a,o):r(o,p):a||(o[o.length]=p)}return o}t.exports=n},1:function(t,e,i){t.exports=i(132)()},3406:function(t,e,i){var r=i(1),s=i(7361);t.exports=function(t,e){return t&&r(t,e,s)}},1957:function(t,e,i){var r=i(3835),s=i(8481);t.exports=function(t,e){e=r(e,t);for(var i=0,n=e.length;null!=t&&i<n;)t=t[s(e[i++])];return i&&i==n?t:void 0}},7743:function(t,e,i){var r=i(5741),s=i(6377);t.exports=function(t,e,i){var n=e(t);return s(t)?n:r(n,i(t))}},3757:function(t,e,i){var r=i(4886),s=i(5118),n=i(7070),a="[object Null]",o="[object Undefined]",h=r?r.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?o:a:h&&h in Object(t)?s(t):n(t)}},6993:function(t){t.exports=function(t,e){return null!=t&&e in Object(t)}},841:function(t,e,i){var r=i(3757),s=i(7013),n="[object Arguments]";t.exports=function(t){return s(t)&&r(t)==n}},5447:function(t,e,i){var r=i(906),s=i(7013);function n(t,e,i,a,o){return t===e||(null!=t&&null!=e&&(s(t)||s(e))?r(t,e,i,a,n,o):t!=t&&e!=e)}t.exports=n},906:function(t,e,i){var r=i(1902),s=i(4476),n=i(9027),a=i(8714),o=i(9937),h=i(6377),l=i(6018),p=i(8586),f=1,u="[object Arguments]",c="[object Array]",m="[object Object]",d=Object.prototype.hasOwnProperty;t.exports=function(t,e,i,g,v,y){var b=h(t),x=h(e),_=b?c:o(t),A=x?c:o(e);_=_==u?m:_,A=A==u?m:A;var k=_==m,w=A==m,P=_==A;if(P&&l(t)){if(!l(e))return!1;b=!0,k=!1}if(P&&!k)return y||(y=new r),b||p(t)?s(t,e,i,g,v,y):n(t,e,_,i,g,v,y);if(!(i&f)){var C=k&&d.call(t,"__wrapped__"),S=w&&d.call(e,"__wrapped__");if(C||S){var D=C?t.value():t,T=S?e.value():e;return y||(y=new r),v(D,T,i,g,y)}}return!!P&&(y||(y=new r),a(t,e,i,g,v,y))}},7293:function(t,e,i){var r=i(1902),s=i(5447),n=1,a=2;t.exports=function(t,e,i,o){var h=i.length,l=h,p=!o;if(null==t)return!l;for(t=Object(t);h--;){var f=i[h];if(p&&f[2]?f[1]!==t[f[0]]:!(f[0]in t))return!1}for(;++h<l;){var u=(f=i[h])[0],c=t[u],m=f[1];if(p&&f[2]){if(void 0===c&&!(u in t))return!1}else{var d=new r;if(o)var g=o(c,m,u,t,e,d);if(!(void 0===g?s(m,c,n|a,o,d):g))return!1}}return!0}},692:function(t,e,i){var r=i(6644),s=i(3417),n=i(8532),a=i(1473),o=/[\\^$.*+?()[\]{}|]/g,h=/^\[object .+?Constructor\]$/,l=Object.prototype,p=Function.prototype.toString,f=l.hasOwnProperty,u=RegExp("^"+p.call(f).replace(o,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!n(t)||s(t))&&(r(t)?u:h).test(a(t))}},2195:function(t,e,i){var r=i(3757),s=i(7924),n=i(7013),a="[object Arguments]",o="[object Array]",h="[object Boolean]",l="[object Date]",p="[object Error]",f="[object Function]",u="[object Map]",c="[object Number]",m="[object Object]",d="[object RegExp]",g="[object Set]",v="[object String]",y="[object WeakMap]",b="[object ArrayBuffer]",x="[object DataView]",_="[object Float64Array]",A="[object Int8Array]",k="[object Int16Array]",w="[object Int32Array]",P="[object Uint8Array]",C="[object Uint8ClampedArray]",S="[object Uint16Array]",D="[object Uint32Array]",T={};T["[object Float32Array]"]=T[_]=T[A]=T[k]=T[w]=T[P]=T[C]=T[S]=T[D]=!0,T[a]=T[o]=T[b]=T[h]=T[x]=T[l]=T[p]=T[f]=T[u]=T[c]=T[m]=T[d]=T[g]=T[v]=T[y]=!1,t.exports=function(t){return n(t)&&s(t.length)&&!!T[r(t)]}},5462:function(t,e,i){var r=i(6358),s=i(4503),n=i(1622),a=i(6377),o=i(8303);t.exports=function(t){return"function"==typeof t?t:null==t?n:"object"==typeof t?a(t)?s(t[0],t[1]):r(t):o(t)}},7407:function(t,e,i){var r=i(8857),s=i(2440),n=Object.prototype.hasOwnProperty;t.exports=function(t){if(!r(t))return s(t);var e=[];for(var i in Object(t))n.call(t,i)&&"constructor"!=i&&e.push(i);return e}},9237:function(t,e,i){var r=i(8532),s=i(8857),n=i(1308),a=Object.prototype.hasOwnProperty;t.exports=function(t){if(!r(t))return n(t);var e=s(t),i=[];for(var o in t)"constructor"==o&&(e||!a.call(t,o))||i.push(o);return i}},4382:function(t){t.exports=function(){}},6358:function(t,e,i){var r=i(7293),s=i(7145),n=i(4167);t.exports=function(t){var e=s(t);return 1==e.length&&e[0][2]?n(e[0][0],e[0][1]):function(i){return i===t||r(i,t,e)}}},4503:function(t,e,i){var r=i(5447),s=i(4738),n=i(9290),a=i(7074),o=i(1542),h=i(4167),l=i(8481),p=1,f=2;t.exports=function(t,e){return a(t)&&o(e)?h(l(t),e):function(i){var a=s(i,t);return void 0===a&&a===e?n(i,t):r(e,a,p|f)}}},7100:function(t,e,i){var r=i(1957),s=i(5495),n=i(3835);t.exports=function(t,e,i){for(var a=-1,o=e.length,h={};++a<o;){var l=e[a],p=r(t,l);i(p,l)&&s(h,n(l,t),p)}return h}},2726:function(t){t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},1374:function(t,e,i){var r=i(1957);t.exports=function(t){return function(e){return r(e,t)}}},9864:function(t){t.exports=function(t,e,i,r,s){return s(t,function(t,s,n){i=r?(r=!1,t):e(i,t,s,n)}),i}},5495:function(t,e,i){var r=i(3615),s=i(3835),n=i(9251),a=i(8532),o=i(8481);t.exports=function(t,e,i,h){if(!a(t))return t;e=s(e,t);for(var l=-1,p=e.length,f=p-1,u=t;null!=u&&++l<p;){var c=o(e[l]),m=i;if("__proto__"===c||"constructor"===c||"prototype"===c)break;if(l!=f){var d=u[c];void 0===(m=h?h(d,c,u):void 0)&&(m=a(d)?d:n(e[l+1])?[]:{})}r(u,c,m),u=u[c]}return t}},2422:function(t,e,i){var r=i(5055),s=i(9833),n=i(1622);t.exports=s?function(t,e){return s(t,"toString",{configurable:!0,enumerable:!1,value:r(e),writable:!0})}:n},1682:function(t){t.exports=function(t,e){for(var i=-1,r=Array(t);++i<t;)r[i]=e(i);return r}},9653:function(t,e,i){var r=i(4886),s=i(1098),n=i(6377),a=i(1359),o=1/0,h=r?r.prototype:void 0,l=h?h.toString:void 0;function p(t){if("string"==typeof t)return t;if(n(t))return s(t,p)+"";if(a(t))return l?l.call(t):"";var e=t+"";return"0"==e&&1/t==-o?"-0":e}t.exports=p},1072:function(t,e,i){var r=i(3230),s=/^\s+/;t.exports=function(t){return t?t.slice(0,r(t)+1).replace(s,""):t}},7509:function(t){t.exports=function(t){return function(e){return t(e)}}},2471:function(t){t.exports=function(t,e){return t.has(e)}},8269:function(t,e,i){var r=i(1622);t.exports=function(t){return"function"==typeof t?t:r}},3835:function(t,e,i){var r=i(6377),s=i(7074),n=i(8997),a=i(6214);t.exports=function(t,e){return r(t)?t:s(t,e)?[t]:n(a(t))}},8606:function(t){t.exports=function(t,e){var i=-1,r=t.length;for(e||(e=Array(r));++i<r;)e[i]=t[i];return e}},5772:function(t,e,i){t.exports=i(5238)["__core-js_shared__"]},2679:function(t,e,i){var r=i(508);t.exports=function(t,e){return function(i,s){if(null==i)return i;if(!r(i))return t(i,s);for(var n=i.length,a=e?n:-1,o=Object(i);(e?a--:++a<n)&&!1!==s(o[a],a,o););return i}}},132:function(t){t.exports=function(t){return function(e,i,r){for(var s=-1,n=Object(e),a=r(e),o=a.length;o--;){var h=a[t?o:++s];if(!1===i(n[h],h,n))break}return e}}},727:function(t,e,i){var r=i(5462),s=i(508),n=i(7361);t.exports=function(t){return function(e,i,a){var o=Object(e);if(!s(e)){var h=r(i,3);e=n(e),i=function(t){return h(o[t],t,o)}}var l=t(e,i,a);return l>-1?o[h?e[l]:l]:void 0}}},914:function(t,e,i){var r=i(9675),s=i(4502),n=i(6007),a=i(195),o=i(6377),h=i(6252),l="Expected a function",p=8,f=32,u=128,c=256;t.exports=function(t){return s(function(e){var i=e.length,s=i,m=r.prototype.thru;for(t&&e.reverse();s--;){var d=e[s];if("function"!=typeof d)throw TypeError(l);if(m&&!g&&"wrapper"==a(d))var g=new r([],!0)}for(s=g?s:i;++s<i;){var v=a(d=e[s]),y="wrapper"==v?n(d):void 0;g=y&&h(y[0])&&y[1]==(u|p|f|c)&&!y[4].length&&1==y[9]?g[a(y[0])].apply(g,y[3]):1==d.length&&h(d)?g[v]():g.thru(d)}return function(){var t=arguments,r=t[0];if(g&&1==t.length&&o(r))return g.plant(r).value();for(var s=0,n=i?e[s].apply(this,t):r;++s<i;)n=e[s].call(this,n);return n}})}},9833:function(t,e,i){var r=i(440);t.exports=function(){try{var t=r(Object,"defineProperty");return t({},"",{}),t}catch(t){}}()},4476:function(t,e,i){var r=i(3290),s=i(3955),n=i(2471),a=1,o=2;t.exports=function(t,e,i,h,l,p){var f=i&a,u=t.length,c=e.length;if(u!=c&&!(f&&c>u))return!1;var m=p.get(t),d=p.get(e);if(m&&d)return m==e&&d==t;var g=-1,v=!0,y=i&o?new r:void 0;for(p.set(t,e),p.set(e,t);++g<u;){var b=t[g],x=e[g];if(h)var _=f?h(x,b,g,e,t,p):h(b,x,g,t,e,p);if(void 0!==_){if(_)continue;v=!1;break}if(y){if(!s(e,function(t,e){if(!n(y,e)&&(b===t||l(b,t,i,h,p)))return y.push(e)})){v=!1;break}}else if(!(b===x||l(b,x,i,h,p))){v=!1;break}}return p.delete(t),p.delete(e),v}},9027:function(t,e,i){var r=i(4886),s=i(8965),n=i(4071),a=i(4476),o=i(7170),h=i(2779),l=1,p=2,f="[object Boolean]",u="[object Date]",c="[object Error]",m="[object Map]",d="[object Number]",g="[object RegExp]",v="[object Set]",y="[object String]",b="[object Symbol]",x="[object ArrayBuffer]",_="[object DataView]",A=r?r.prototype:void 0,k=A?A.valueOf:void 0;t.exports=function(t,e,i,r,A,w,P){switch(i){case _:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)break;t=t.buffer,e=e.buffer;case x:if(t.byteLength!=e.byteLength||!w(new s(t),new s(e)))break;return!0;case f:case u:case d:return n(+t,+e);case c:return t.name==e.name&&t.message==e.message;case g:case y:return t==e+"";case m:var C=o;case v:var S=r&l;if(C||(C=h),t.size!=e.size&&!S)break;var D=P.get(t);if(D)return D==e;r|=p,P.set(t,e);var T=a(C(t),C(e),r,A,w,P);return P.delete(t),T;case b:if(k)return k.call(t)==k.call(e)}return!1}},8714:function(t,e,i){var r=i(3948),s=1,n=Object.prototype.hasOwnProperty;t.exports=function(t,e,i,a,o,h){var l=i&s,p=r(t),f=p.length;if(f!=r(e).length&&!l)return!1;for(var u=f;u--;){var c=p[u];if(!(l?c in e:n.call(e,c)))return!1}var m=h.get(t),d=h.get(e);if(m&&d)return m==e&&d==t;var g=!0;h.set(t,e),h.set(e,t);for(var v=l;++u<f;){var y=t[c=p[u]],b=e[c];if(a)var x=l?a(b,y,c,e,t,h):a(y,b,c,t,e,h);if(!(void 0===x?y===b||o(y,b,i,a,h):x)){g=!1;break}v||(v="constructor"==c)}if(g&&!v){var _=t.constructor,A=e.constructor;_!=A&&"constructor"in t&&"constructor"in e&&!("function"==typeof _&&_ instanceof _&&"function"==typeof A&&A instanceof A)&&(g=!1)}return h.delete(t),h.delete(e),g}},4502:function(t,e,i){var r=i(6380),s=i(6813),n=i(2413);t.exports=function(t){return n(s(t,void 0,r),t+"")}},2593:function(t,e,i){t.exports="object"==typeof i.g&&i.g&&i.g.Object===Object&&i.g},3948:function(t,e,i){var r=i(7743),s=i(6230),n=i(7361);t.exports=function(t){return r(t,n,s)}},9254:function(t,e,i){var r=i(7743),s=i(2992),n=i(3747);t.exports=function(t){return r(t,n,s)}},6007:function(t,e,i){var r=i(900),s=i(6032);t.exports=r?function(t){return r.get(t)}:s},195:function(t,e,i){var r=i(8564),s=Object.prototype.hasOwnProperty;t.exports=function(t){for(var e=t.name+"",i=r[e],n=s.call(r,e)?i.length:0;n--;){var a=i[n],o=a.func;if(null==o||o==t)return a.name}return e}},1143:function(t,e,i){var r=i(6669);t.exports=function(t,e){var i=t.__data__;return r(e)?i["string"==typeof e?"string":"hash"]:i.map}},7145:function(t,e,i){var r=i(1542),s=i(7361);t.exports=function(t){for(var e=s(t),i=e.length;i--;){var n=e[i],a=t[n];e[i]=[n,a,r(a)]}return e}},440:function(t,e,i){var r=i(692),s=i(8974);t.exports=function(t,e){var i=s(t,e);return r(i)?i:void 0}},6095:function(t,e,i){t.exports=i(6512)(Object.getPrototypeOf,Object)},5118:function(t,e,i){var r=i(4886),s=Object.prototype,n=s.hasOwnProperty,a=s.toString,o=r?r.toStringTag:void 0;t.exports=function(t){var e=n.call(t,o),i=t[o];try{t[o]=void 0;var r=!0}catch(t){}var s=a.call(t);return r&&(e?t[o]=i:delete t[o]),s}},6230:function(t,e,i){var r=i(2654),s=i(1036),n=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols;t.exports=a?function(t){return null==t?[]:r(a(t=Object(t)),function(e){return n.call(t,e)})}:s},2992:function(t,e,i){var r=i(5741),s=i(6095),n=i(6230),a=i(1036);t.exports=Object.getOwnPropertySymbols?function(t){for(var e=[];t;)r(e,n(t)),t=s(t);return e}:a},9937:function(t,e,i){var r=i(8172),s=i(9036),n=i(44),a=i(6656),o=i(3283),h=i(3757),l=i(1473),p="[object Map]",f="[object Object]",u="[object Promise]",c="[object Set]",m="[object WeakMap]",d="[object DataView]",g=l(r),v=l(s),y=l(n),b=l(a),x=l(o),_=h;(r&&_(new r(new ArrayBuffer(1)))!=d||s&&_(new s)!=p||n&&_(n.resolve())!=u||a&&_(new a)!=c||o&&_(new o)!=m)&&(_=function(t){var e=h(t),i=e==f?t.constructor:void 0,r=i?l(i):"";if(r)switch(r){case g:return d;case v:return p;case y:return u;case b:return c;case x:return m}return e}),t.exports=_},8974:function(t){t.exports=function(t,e){return null==t?void 0:t[e]}},7635:function(t,e,i){var r=i(3835),s=i(9732),n=i(6377),a=i(9251),o=i(7924),h=i(8481);t.exports=function(t,e,i){e=r(e,t);for(var l=-1,p=e.length,f=!1;++l<p;){var u=h(e[l]);if(!(f=null!=t&&i(t,u)))break;t=t[u]}return f||++l!=p?f:!!(p=null==t?0:t.length)&&o(p)&&a(u,p)&&(n(t)||s(t))}},9520:function(t){var e=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},7322:function(t,e,i){var r=i(7305);t.exports=function(){this.__data__=r?r(null):{},this.size=0}},2937:function(t){t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=!!e,e}},207:function(t,e,i){var r=i(7305),s="__lodash_hash_undefined__",n=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(r){var i=e[t];return i===s?void 0:i}return n.call(e,t)?e[t]:void 0}},2165:function(t,e,i){var r=i(7305),s=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return r?void 0!==e[t]:s.call(e,t)}},7523:function(t,e,i){var r=i(7305),s="__lodash_hash_undefined__";t.exports=function(t,e){var i=this.__data__;return this.size+=+!this.has(t),i[t]=r&&void 0===e?s:e,this}},1668:function(t,e,i){var r=i(4886),s=i(9732),n=i(6377),a=r?r.isConcatSpreadable:void 0;t.exports=function(t){return n(t)||s(t)||!!(a&&t&&t[a])}},9251:function(t){var e=0x1fffffffffffff,i=/^(?:0|[1-9]\d*)$/;t.exports=function(t,r){var s=typeof t;return!!(r=null==r?e:r)&&("number"==s||"symbol"!=s&&i.test(t))&&t>-1&&t%1==0&&t<r}},7074:function(t,e,i){var r=i(6377),s=i(1359),n=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(r(t))return!1;var i=typeof t;return!!("number"==i||"symbol"==i||"boolean"==i||null==t||s(t))||a.test(t)||!n.test(t)||null!=e&&t in Object(e)}},6669:function(t){t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},6252:function(t,e,i){var r=i(4281),s=i(6007),n=i(195),a=i(6985);t.exports=function(t){var e=n(t),i=a[e];if("function"!=typeof i||!(e in r.prototype))return!1;if(t===i)return!0;var o=s(i);return!!o&&t===o[0]}},3417:function(t,e,i){var r=i(5772),s=function(){var t=/[^.]+$/.exec(r&&r.keys&&r.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();t.exports=function(t){return!!s&&s in t}},8857:function(t){var e=Object.prototype;t.exports=function(t){var i=t&&t.constructor;return t===("function"==typeof i&&i.prototype||e)}},1542:function(t,e,i){var r=i(8532);t.exports=function(t){return t==t&&!r(t)}},7435:function(t){t.exports=function(){this.__data__=[],this.size=0}},8438:function(t,e,i){var r=i(8357),s=Array.prototype.splice;t.exports=function(t){var e=this.__data__,i=r(e,t);return!(i<0)&&(i==e.length-1?e.pop():s.call(e,i,1),--this.size,!0)}},3067:function(t,e,i){var r=i(8357);t.exports=function(t){var e=this.__data__,i=r(e,t);return i<0?void 0:e[i][1]}},9679:function(t,e,i){var r=i(8357);t.exports=function(t){return r(this.__data__,t)>-1}},2426:function(t,e,i){var r=i(8357);t.exports=function(t,e){var i=this.__data__,s=r(i,t);return s<0?(++this.size,i.push([t,e])):i[s][1]=e,this}},6409:function(t,e,i){var r=i(1796),s=i(283),n=i(9036);t.exports=function(){this.size=0,this.__data__={hash:new r,map:new(n||s),string:new r}}},5335:function(t,e,i){var r=i(1143);t.exports=function(t){var e=r(this,t).delete(t);return this.size-=!!e,e}},5601:function(t,e,i){var r=i(1143);t.exports=function(t){return r(this,t).get(t)}},1533:function(t,e,i){var r=i(1143);t.exports=function(t){return r(this,t).has(t)}},151:function(t,e,i){var r=i(1143);t.exports=function(t,e){var i=r(this,t),s=i.size;return i.set(t,e),this.size+=+(i.size!=s),this}},7170:function(t){t.exports=function(t){var e=-1,i=Array(t.size);return t.forEach(function(t,r){i[++e]=[r,t]}),i}},4167:function(t){t.exports=function(t,e){return function(i){return null!=i&&i[t]===e&&(void 0!==e||t in Object(i))}}},6141:function(t,e,i){var r=i(4984),s=500;t.exports=function(t){var e=r(t,function(t){return i.size===s&&i.clear(),t}),i=e.cache;return e}},900:function(t,e,i){var r=i(3283);t.exports=r&&new r},7305:function(t,e,i){t.exports=i(440)(Object,"create")},2440:function(t,e,i){t.exports=i(6512)(Object.keys,Object)},1308:function(t){t.exports=function(t){var e=[];if(null!=t)for(var i in Object(t))e.push(i);return e}},895:function(t,e,i){t=i.nmd(t);var r=i(2593),s=e&&!e.nodeType&&e,n=s&&t&&!t.nodeType&&t,a=n&&n.exports===s&&r.process,o=function(){try{var t=n&&n.require&&n.require("util").types;if(t)return t;return a&&a.binding&&a.binding("util")}catch(t){}}();t.exports=o},7070:function(t){var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},6512:function(t){t.exports=function(t,e){return function(i){return t(e(i))}}},6813:function(t,e,i){var r=i(9198),s=Math.max;t.exports=function(t,e,i){return e=s(void 0===e?t.length-1:e,0),function(){for(var n=arguments,a=-1,o=s(n.length-e,0),h=Array(o);++a<o;)h[a]=n[e+a];a=-1;for(var l=Array(e+1);++a<e;)l[a]=n[a];return l[e]=i(h),r(t,this,l)}}},8564:function(t){t.exports={}},5238:function(t,e,i){var r=i(2593),s="object"==typeof self&&self&&self.Object===Object&&self;t.exports=r||s||Function("return this")()},1760:function(t){var e="__lodash_hash_undefined__";t.exports=function(t){return this.__data__.set(t,e),this}},5484:function(t){t.exports=function(t){return this.__data__.has(t)}},2779:function(t){t.exports=function(t){var e=-1,i=Array(t.size);return t.forEach(function(t){i[++e]=t}),i}},2413:function(t,e,i){var r=i(2422);t.exports=i(7890)(r)},7890:function(t){var e=800,i=16,r=Date.now;t.exports=function(t){var s=0,n=0;return function(){var a=r(),o=i-(a-n);if(n=a,o>0){if(++s>=e)return arguments[0]}else s=0;return t.apply(void 0,arguments)}}},6063:function(t,e,i){var r=i(283);t.exports=function(){this.__data__=new r,this.size=0}},7727:function(t){t.exports=function(t){var e=this.__data__,i=e.delete(t);return this.size=e.size,i}},3281:function(t){t.exports=function(t){return this.__data__.get(t)}},6667:function(t){t.exports=function(t){return this.__data__.has(t)}},1270:function(t,e,i){var r=i(283),s=i(9036),n=i(4544),a=200;t.exports=function(t,e){var i=this.__data__;if(i instanceof r){var o=i.__data__;if(!s||o.length<a-1)return o.push([t,e]),this.size=++i.size,this;i=this.__data__=new n(o)}return i.set(t,e),this.size=i.size,this}},6749:function(t,e,i){var r=i(609),s=i(9520),n=i(9668);t.exports=function(t){return s(t)?n(t):r(t)}},8997:function(t,e,i){var r=i(6141),s=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,n=/\\(\\)?/g;t.exports=r(function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(s,function(t,i,r,s){e.push(r?s.replace(n,"$1"):i||t)}),e})},8481:function(t,e,i){var r=i(1359),s=1/0;t.exports=function(t){if("string"==typeof t||r(t))return t;var e=t+"";return"0"==e&&1/t==-s?"-0":e}},1473:function(t){var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},3230:function(t){var e=/\s/;t.exports=function(t){for(var i=t.length;i--&&e.test(t.charAt(i)););return i}},9668:function(t){var e="\ud800-\udfff",i="["+e+"]",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",s="\ud83c[\udffb-\udfff]",n="[^"+e+"]",a="(?:\ud83c[\udde6-\uddff]){2}",o="[\ud800-\udbff][\udc00-\udfff]",h="(?:"+r+"|"+s+")?",l="[\\ufe0e\\ufe0f]?",p="(?:\\u200d(?:"+[n,a,o].join("|")+")"+l+h+")*",f=l+h+p,u=RegExp(s+"(?="+s+")|"+("(?:"+[n+r+"?",r,a,o,i].join("|"))+")"+f,"g");t.exports=function(t){for(var e=u.lastIndex=0;u.test(t);)++e;return e}},219:function(t,e,i){var r=i(4281),s=i(9675),n=i(8606);t.exports=function(t){if(t instanceof r)return t.clone();var e=new s(t.__wrapped__,t.__chain__);return e.__actions__=n(t.__actions__),e.__index__=t.__index__,e.__values__=t.__values__,e}},3789:function(t,e,i){var r=i(2009),s=i(6127);t.exports=function(t,e,i){return void 0===i&&(i=e,e=void 0),void 0!==i&&(i=(i=s(i))==i?i:0),void 0!==e&&(e=(e=s(e))==e?e:0),r(s(t),e,i)}},5055:function(t){t.exports=function(t){return function(){return t}}},8305:function(t,e,i){var r=i(8532),s=i(806),n=i(6127),a="Expected a function",o=Math.max,h=Math.min;t.exports=function(t,e,i){var l,p,f,u,c,m,d=0,g=!1,v=!1,y=!0;if("function"!=typeof t)throw TypeError(a);function b(e){var i=l,r=p;return l=p=void 0,d=e,u=t.apply(r,i)}function x(t){return d=t,c=setTimeout(k,e),g?b(t):u}function _(t){var i=t-m,r=t-d,s=e-i;return v?h(s,f-r):s}function A(t){var i=t-m,r=t-d;return void 0===m||i>=e||i<0||v&&r>=f}function k(){var t=s();if(A(t))return w(t);c=setTimeout(k,_(t))}function w(t){return(c=void 0,y&&l)?b(t):(l=p=void 0,u)}function P(){return void 0===c?u:w(s())}function C(){var t=s(),i=A(t);if(l=arguments,p=this,m=t,i){if(void 0===c)return x(m);if(v)return clearTimeout(c),c=setTimeout(k,e),b(m)}return void 0===c&&(c=setTimeout(k,e)),u}return e=n(e)||0,r(i)&&(g=!!i.leading,f=(v="maxWait"in i)?o(n(i.maxWait)||0,e):f,y="trailing"in i?!!i.trailing:y),C.cancel=function(){void 0!==c&&clearTimeout(c),d=0,l=m=p=c=void 0},C.flush=P,C}},4075:function(t){t.exports=function(t,e){return null==t||t!=t?e:t}},4071:function(t){t.exports=function(t,e){return t===e||t!=t&&e!=e}},9777:function(t,e,i){t.exports=i(727)(i(3142))},3142:function(t,e,i){var r=i(2056),s=i(5462),n=i(8536),a=Math.max;t.exports=function(t,e,i){var o=null==t?0:t.length;if(!o)return -1;var h=null==i?0:n(i);return h<0&&(h=a(o+h,0)),r(t,s(e,3),h)}},5720:function(t,e,i){t.exports=i(727)(i(3758))},3758:function(t,e,i){var r=i(2056),s=i(5462),n=i(8536),a=Math.max,o=Math.min;t.exports=function(t,e,i){var h=null==t?0:t.length;if(!h)return -1;var l=h-1;return void 0!==i&&(l=n(i),l=i<0?a(h+l,0):o(l,h-1)),r(t,s(e,3),l,!0)}},6380:function(t,e,i){var r=i(5265);t.exports=function(t){return(null==t?0:t.length)?r(t,1):[]}},5801:function(t,e,i){t.exports=i(914)()},2397:function(t,e,i){var r=i(4970),s=i(8264),n=i(8269),a=i(6377);t.exports=function(t,e){return(a(t)?r:s)(t,n(e))}},4738:function(t,e,i){var r=i(1957);t.exports=function(t,e,i){var s=null==t?void 0:r(t,e);return void 0===s?i:s}},9290:function(t,e,i){var r=i(6993),s=i(7635);t.exports=function(t,e){return null!=t&&s(t,e,r)}},1622:function(t){t.exports=function(t){return t}},9732:function(t,e,i){var r=i(841),s=i(7013),n=Object.prototype,a=n.hasOwnProperty,o=n.propertyIsEnumerable;t.exports=r(function(){return arguments}())?r:function(t){return s(t)&&a.call(t,"callee")&&!o.call(t,"callee")}},6377:function(t){t.exports=Array.isArray},508:function(t,e,i){var r=i(6644),s=i(7924);t.exports=function(t){return null!=t&&s(t.length)&&!r(t)}},6018:function(t,e,i){t=i.nmd(t);var r=i(5238),s=i(5786),n=e&&!e.nodeType&&e,a=n&&t&&!t.nodeType&&t,o=a&&a.exports===n?r.Buffer:void 0,h=(o?o.isBuffer:void 0)||s;t.exports=h},6633:function(t,e,i){var r=i(7407),s=i(9937),n=i(9732),a=i(6377),o=i(508),h=i(6018),l=i(8857),p=i(8586),f="[object Map]",u="[object Set]",c=Object.prototype.hasOwnProperty;t.exports=function(t){if(null==t)return!0;if(o(t)&&(a(t)||"string"==typeof t||"function"==typeof t.splice||h(t)||p(t)||n(t)))return!t.length;var e=s(t);if(e==f||e==u)return!t.size;if(l(t))return!r(t).length;for(var i in t)if(c.call(t,i))return!1;return!0}},6644:function(t,e,i){var r=i(3757),s=i(8532),n="[object AsyncFunction]",a="[object Function]",o="[object GeneratorFunction]",h="[object Proxy]";t.exports=function(t){if(!s(t))return!1;var e=r(t);return e==a||e==o||e==n||e==h}},7924:function(t){var e=0x1fffffffffffff;t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=e}},8532:function(t){t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},7013:function(t){t.exports=function(t){return null!=t&&"object"==typeof t}},1085:function(t,e,i){var r=i(3757),s=i(6377),n=i(7013),a="[object String]";t.exports=function(t){return"string"==typeof t||!s(t)&&n(t)&&r(t)==a}},1359:function(t,e,i){var r=i(3757),s=i(7013),n="[object Symbol]";t.exports=function(t){return"symbol"==typeof t||s(t)&&r(t)==n}},8586:function(t,e,i){var r=i(2195),s=i(7509),n=i(895),a=n&&n.isTypedArray;t.exports=a?s(a):r},7361:function(t,e,i){var r=i(4979),s=i(7407),n=i(508);t.exports=function(t){return n(t)?r(t):s(t)}},3747:function(t,e,i){var r=i(4979),s=i(9237),n=i(508);t.exports=function(t){return n(t)?r(t,!0):s(t)}},3729:function(t,e,i){var r=i(2676),s=i(3406),n=i(5462);t.exports=function(t,e){var i={};return e=n(e,3),s(t,function(t,s,n){r(i,s,e(t,s,n))}),i}},4984:function(t,e,i){var r=i(4544),s="Expected a function";function n(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw TypeError(s);var i=function(){var r=arguments,s=e?e.apply(this,r):r[0],n=i.cache;if(n.has(s))return n.get(s);var a=t.apply(this,r);return i.cache=n.set(s,a)||n,a};return i.cache=new(n.Cache||r),i}n.Cache=r,t.exports=n},3103:function(t){var e="Expected a function";t.exports=function(t){if("function"!=typeof t)throw TypeError(e);return function(){var e=arguments;switch(e.length){case 0:return!t.call(this);case 1:return!t.call(this,e[0]);case 2:return!t.call(this,e[0],e[1]);case 3:return!t.call(this,e[0],e[1],e[2])}return!t.apply(this,e)}}},6032:function(t){t.exports=function(){}},806:function(t,e,i){var r=i(5238);t.exports=function(){return r.Date.now()}},3452:function(t,e,i){var r=i(5462),s=i(3103),n=i(4103);t.exports=function(t,e){return n(t,s(r(e)))}},4103:function(t,e,i){var r=i(1098),s=i(5462),n=i(7100),a=i(9254);t.exports=function(t,e){if(null==t)return{};var i=r(a(t),function(t){return[t]});return e=s(e),n(t,i,function(t,i){return e(t,i[0])})}},8303:function(t,e,i){var r=i(2726),s=i(1374),n=i(7074),a=i(8481);t.exports=function(t){return n(t)?r(a(t)):s(t)}},1455:function(t,e,i){var r=i(2607),s=i(8264),n=i(5462),a=i(9864),o=i(6377);t.exports=function(t,e,i){var h=o(t)?r:a,l=arguments.length<3;return h(t,n(e,4),i,l,s)}},4659:function(t,e,i){var r=i(7407),s=i(9937),n=i(508),a=i(1085),o=i(6749),h="[object Map]",l="[object Set]";t.exports=function(t){if(null==t)return 0;if(n(t))return a(t)?o(t):t.length;var e=s(t);return e==h||e==l?t.size:r(t).length}},1036:function(t){t.exports=function(){return[]}},5786:function(t){t.exports=function(){return!1}},5082:function(t,e,i){var r=i(8305),s=i(8532),n="Expected a function";t.exports=function(t,e,i){var a=!0,o=!0;if("function"!=typeof t)throw TypeError(n);return s(i)&&(a="leading"in i?!!i.leading:a,o="trailing"in i?!!i.trailing:o),r(t,e,{leading:a,maxWait:e,trailing:o})}},5597:function(t,e,i){var r=i(6127),s=1/0,n=17976931348623157e292;t.exports=function(t){return t?(t=r(t))===s||t===-s?(t<0?-1:1)*n:t==t?t:0:0===t?t:0}},8536:function(t,e,i){var r=i(5597);t.exports=function(t){var e=r(t),i=e%1;return e==e?i?e-i:e:0}},6127:function(t,e,i){var r=i(1072),s=i(8532),n=i(1359),a=0/0,o=/^[-+]0x[0-9a-f]+$/i,h=/^0b[01]+$/i,l=/^0o[0-7]+$/i,p=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(n(t))return a;if(s(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=s(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=r(t);var i=h.test(t);return i||l.test(t)?p(t.slice(2),i?2:8):o.test(t)?a:+t}},6214:function(t,e,i){var r=i(9653);t.exports=function(t){return null==t?"":r(t)}},6985:function(t,e,i){var r=i(4281),s=i(9675),n=i(4382),a=i(6377),o=i(7013),h=i(219),l=Object.prototype.hasOwnProperty;function p(t){if(o(t)&&!a(t)&&!(t instanceof r)){if(t instanceof s)return t;if(l.call(t,"__wrapped__"))return h(t)}return new s(t)}p.prototype=n.prototype,p.prototype.constructor=p,t.exports=p},8724:function(module,exports){"undefined"!=typeof navigator&&function(t,e){module.exports=e()}(0,function(){"use strict";var svgNS="http://www.w3.org/2000/svg",locationHref="",_useWebWorker=!1,initialDefaultFrame=-999999,setWebWorker=function(t){_useWebWorker=!!t},getWebWorker=function(){return _useWebWorker},setLocationHref=function(t){locationHref=t},getLocationHref=function(){return locationHref};function createTag(t){return document.createElement(t)}function extendPrototype(t,e){var i,r,s=t.length;for(i=0;i<s;i+=1)for(var n in r=t[i].prototype)Object.prototype.hasOwnProperty.call(r,n)&&(e.prototype[n]=r[n])}function getDescriptor(t,e){return Object.getOwnPropertyDescriptor(t,e)}function createProxyFunction(t){function e(){}return e.prototype=t,e}var audioControllerFactory=function(){function t(t){this.audios=[],this.audioFactory=t,this._volume=1,this._isMuted=!1}return t.prototype={addAudio:function(t){this.audios.push(t)},pause:function(){var t,e=this.audios.length;for(t=0;t<e;t+=1)this.audios[t].pause()},resume:function(){var t,e=this.audios.length;for(t=0;t<e;t+=1)this.audios[t].resume()},setRate:function(t){var e,i=this.audios.length;for(e=0;e<i;e+=1)this.audios[e].setRate(t)},createAudio:function(t){return this.audioFactory?this.audioFactory(t):window.Howl?new window.Howl({src:[t]}):{isPlaying:!1,play:function(){this.isPlaying=!0},seek:function(){this.isPlaying=!1},playing:function(){},rate:function(){},setVolume:function(){}}},setAudioFactory:function(t){this.audioFactory=t},setVolume:function(t){this._volume=t,this._updateVolume()},mute:function(){this._isMuted=!0,this._updateVolume()},unmute:function(){this._isMuted=!1,this._updateVolume()},getVolume:function(){return this._volume},_updateVolume:function(){var t,e=this.audios.length;for(t=0;t<e;t+=1)this.audios[t].volume(!this._isMuted*this._volume)}},function(){return new t}}(),createTypedArray=function(){function t(t,e){var i,r=0,s=[];switch(t){case"int16":case"uint8c":i=1;break;default:i=1.1}for(r=0;r<e;r+=1)s.push(i);return s}return"function"==typeof Uint8ClampedArray&&"function"==typeof Float32Array?function(e,i){return"float32"===e?new Float32Array(i):"int16"===e?new Int16Array(i):"uint8c"===e?new Uint8ClampedArray(i):t(e,i)}:t}();function createSizedArray(t){return Array.apply(null,{length:t})}function _typeof$6(t){return(_typeof$6="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var subframeEnabled=!0,expressionsPlugin=null,expressionsInterfaces=null,idPrefix$1="",isSafari=/^((?!chrome|android).)*safari/i.test(navigator.userAgent),_shouldRoundValues=!1,bmPow=Math.pow,bmSqrt=Math.sqrt,bmFloor=Math.floor,bmMax=Math.max,bmMin=Math.min,BMMath={};function ProjectInterface$1(){return{}}!function(){var t,e=["abs","acos","acosh","asin","asinh","atan","atanh","atan2","ceil","cbrt","expm1","clz32","cos","cosh","exp","floor","fround","hypot","imul","log","log1p","log2","log10","max","min","pow","random","round","sign","sin","sinh","sqrt","tan","tanh","trunc","E","LN10","LN2","LOG10E","LOG2E","PI","SQRT1_2","SQRT2"],i=e.length;for(t=0;t<i;t+=1)BMMath[e[t]]=Math[e[t]]}(),BMMath.random=Math.random,BMMath.abs=function(t){if("object"===_typeof$6(t)&&t.length){var e,i=createSizedArray(t.length),r=t.length;for(e=0;e<r;e+=1)i[e]=Math.abs(t[e]);return i}return Math.abs(t)};var defaultCurveSegments=150,degToRads=Math.PI/180,roundCorner=.5519;function roundValues(t){_shouldRoundValues=!!t}function bmRnd(t){return _shouldRoundValues?Math.round(t):t}function styleDiv(t){t.style.position="absolute",t.style.top=0,t.style.left=0,t.style.display="block",t.style.transformOrigin="0 0",t.style.webkitTransformOrigin="0 0",t.style.backfaceVisibility="visible",t.style.webkitBackfaceVisibility="visible",t.style.transformStyle="preserve-3d",t.style.webkitTransformStyle="preserve-3d",t.style.mozTransformStyle="preserve-3d"}function BMEnterFrameEvent(t,e,i,r){this.type=t,this.currentTime=e,this.totalTime=i,this.direction=r<0?-1:1}function BMCompleteEvent(t,e){this.type=t,this.direction=e<0?-1:1}function BMCompleteLoopEvent(t,e,i,r){this.type=t,this.currentLoop=i,this.totalLoops=e,this.direction=r<0?-1:1}function BMSegmentStartEvent(t,e,i){this.type=t,this.firstFrame=e,this.totalFrames=i}function BMDestroyEvent(t,e){this.type=t,this.target=e}function BMRenderFrameErrorEvent(t,e){this.type="renderFrameError",this.nativeError=t,this.currentTime=e}function BMConfigErrorEvent(t){this.type="configError",this.nativeError=t}function BMAnimationConfigErrorEvent(t,e){this.type=t,this.nativeError=e}var _count,createElementID=(_count=0,function(){return idPrefix$1+"__lottie_element_"+(_count+=1)});function HSVtoRGB(t,e,i){var r,s,n,a,o,h,l,p;switch(h=i*(1-e),l=i*(1-(o=6*t-(a=Math.floor(6*t)))*e),p=i*(1-(1-o)*e),a%6){case 0:r=i,s=p,n=h;break;case 1:r=l,s=i,n=h;break;case 2:r=h,s=i,n=p;break;case 3:r=h,s=l,n=i;break;case 4:r=p,s=h,n=i;break;case 5:r=i,s=h,n=l}return[r,s,n]}function RGBtoHSV(t,e,i){var r,s=Math.max(t,e,i),n=Math.min(t,e,i),a=s-n,o=0===s?0:a/s,h=s/255;switch(s){case n:r=0;break;case t:r=(e-i+6*(e<i)*a)/(6*a);break;case e:r=(i-t+2*a)/(6*a);break;case i:r=(t-e+4*a)/(6*a)}return[r,o,h]}function addSaturationToRGB(t,e){var i=RGBtoHSV(255*t[0],255*t[1],255*t[2]);return i[1]+=e,i[1]>1?i[1]=1:i[1]<=0&&(i[1]=0),HSVtoRGB(i[0],i[1],i[2])}function addBrightnessToRGB(t,e){var i=RGBtoHSV(255*t[0],255*t[1],255*t[2]);return i[2]+=e,i[2]>1?i[2]=1:i[2]<0&&(i[2]=0),HSVtoRGB(i[0],i[1],i[2])}function addHueToRGB(t,e){var i=RGBtoHSV(255*t[0],255*t[1],255*t[2]);return i[0]+=e/360,i[0]>1?i[0]-=1:i[0]<0&&(i[0]+=1),HSVtoRGB(i[0],i[1],i[2])}var rgbToHex=function(){var t,e,i=[];for(t=0;t<256;t+=1)e=t.toString(16),i[t]=1===e.length?"0"+e:e;return function(t,e,r){return t<0&&(t=0),e<0&&(e=0),r<0&&(r=0),"#"+i[t]+i[e]+i[r]}}(),setSubframeEnabled=function(t){subframeEnabled=!!t},getSubframeEnabled=function(){return subframeEnabled},setExpressionsPlugin=function(t){expressionsPlugin=t},getExpressionsPlugin=function(){return expressionsPlugin},setExpressionInterfaces=function(t){expressionsInterfaces=t},getExpressionInterfaces=function(){return expressionsInterfaces},setDefaultCurveSegments=function(t){defaultCurveSegments=t},getDefaultCurveSegments=function(){return defaultCurveSegments},setIdPrefix=function(t){idPrefix$1=t},getIdPrefix=function(){return idPrefix$1};function createNS(t){return document.createElementNS(svgNS,t)}function _typeof$5(t){return(_typeof$5="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var dataManager=function(){var t,e,i=1,r=[],s={onmessage:function(){},postMessage:function(e){t({data:e})}},n={postMessage:function(t){s.onmessage({data:t})}};function a(){e||((e=function(e){if(window.Worker&&window.Blob&&getWebWorker()){var i=new Blob(["var _workerSelf = self; self.onmessage = ",e.toString()],{type:"text/javascript"});return new Worker(URL.createObjectURL(i))}return t=e,s}(function(t){if(n.dataManager||(n.dataManager=function(){function t(s,n){var a,o,h,l,p,u,c=s.length;for(o=0;o<c;o+=1)if("ks"in(a=s[o])&&!a.completed){if(a.completed=!0,a.hasMask){var m=a.masksProperties;for(l=m.length,h=0;h<l;h+=1)if(m[h].pt.k.i)r(m[h].pt.k);else for(u=m[h].pt.k.length,p=0;p<u;p+=1)m[h].pt.k[p].s&&r(m[h].pt.k[p].s[0]),m[h].pt.k[p].e&&r(m[h].pt.k[p].e[0])}0===a.ty?(a.layers=e(a.refId,n),t(a.layers,n)):4===a.ty?i(a.shapes):5===a.ty&&f(a)}}function e(t,e){var i=function(t,e){for(var i=0,r=e.length;i<r;){if(e[i].id===t)return e[i];i+=1}return null}(t,e);return i?i.layers.__used?JSON.parse(JSON.stringify(i.layers)):(i.layers.__used=!0,i.layers):null}function i(t){var e,s,n;for(e=t.length-1;e>=0;e-=1)if("sh"===t[e].ty)if(t[e].ks.k.i)r(t[e].ks.k);else for(n=t[e].ks.k.length,s=0;s<n;s+=1)t[e].ks.k[s].s&&r(t[e].ks.k[s].s[0]),t[e].ks.k[s].e&&r(t[e].ks.k[s].e[0]);else"gr"===t[e].ty&&i(t[e].it)}function r(t){var e,i=t.i.length;for(e=0;e<i;e+=1)t.i[e][0]+=t.v[e][0],t.i[e][1]+=t.v[e][1],t.o[e][0]+=t.v[e][0],t.o[e][1]+=t.v[e][1]}function s(t,e){var i=e?e.split("."):[100,100,100];return t[0]>i[0]||!(i[0]>t[0])&&(t[1]>i[1]||!(i[1]>t[1])&&(t[2]>i[2]||!(i[2]>t[2])&&null))}var n,a=function(){var t=[4,4,14];function e(t){var e,i,r,s=t.length;for(e=0;e<s;e+=1)5===t[e].ty&&(r=void 0,r=(i=t[e]).t.d,i.t.d={k:[{s:r,t:0}]})}return function(i){if(s(t,i.v)&&(e(i.layers),i.assets)){var r,n=i.assets.length;for(r=0;r<n;r+=1)i.assets[r].layers&&e(i.assets[r].layers)}}}(),o=(n=[4,7,99],function(t){if(t.chars&&!s(n,t.v)){var e,r=t.chars.length;for(e=0;e<r;e+=1){var a=t.chars[e];a.data&&a.data.shapes&&(i(a.data.shapes),a.data.ip=0,a.data.op=99999,a.data.st=0,a.data.sr=1,a.data.ks={p:{k:[0,0],a:0},s:{k:[100,100],a:0},a:{k:[0,0],a:0},r:{k:0,a:0},o:{k:100,a:0}},t.chars[e].t||(a.data.shapes.push({ty:"no"}),a.data.shapes[0].it.push({p:{k:[0,0],a:0},s:{k:[100,100],a:0},a:{k:[0,0],a:0},r:{k:0,a:0},o:{k:100,a:0},sk:{k:0,a:0},sa:{k:0,a:0},ty:"tr"})))}}}),h=function(){var t=[5,7,15];function e(t){var e,i,r=t.length;for(e=0;e<r;e+=1)5===t[e].ty&&(i=void 0,"number"==typeof(i=t[e].t.p).a&&(i.a={a:0,k:i.a}),"number"==typeof i.p&&(i.p={a:0,k:i.p}),"number"==typeof i.r&&(i.r={a:0,k:i.r}))}return function(i){if(s(t,i.v)&&(e(i.layers),i.assets)){var r,n=i.assets.length;for(r=0;r<n;r+=1)i.assets[r].layers&&e(i.assets[r].layers)}}}(),l=function(){var t=[4,1,9];function e(t){var i,r,s,n=t.length;for(i=0;i<n;i+=1)if("gr"===t[i].ty)e(t[i].it);else if("fl"===t[i].ty||"st"===t[i].ty)if(t[i].c.k&&t[i].c.k[0].i)for(s=t[i].c.k.length,r=0;r<s;r+=1)t[i].c.k[r].s&&(t[i].c.k[r].s[0]/=255,t[i].c.k[r].s[1]/=255,t[i].c.k[r].s[2]/=255,t[i].c.k[r].s[3]/=255),t[i].c.k[r].e&&(t[i].c.k[r].e[0]/=255,t[i].c.k[r].e[1]/=255,t[i].c.k[r].e[2]/=255,t[i].c.k[r].e[3]/=255);else t[i].c.k[0]/=255,t[i].c.k[1]/=255,t[i].c.k[2]/=255,t[i].c.k[3]/=255}function i(t){var i,r=t.length;for(i=0;i<r;i+=1)4===t[i].ty&&e(t[i].shapes)}return function(e){if(s(t,e.v)&&(i(e.layers),e.assets)){var r,n=e.assets.length;for(r=0;r<n;r+=1)e.assets[r].layers&&i(e.assets[r].layers)}}}(),p=function(){var t=[4,4,18];function e(t){var i,r,s;for(i=t.length-1;i>=0;i-=1)if("sh"===t[i].ty)if(t[i].ks.k.i)t[i].ks.k.c=t[i].closed;else for(s=t[i].ks.k.length,r=0;r<s;r+=1)t[i].ks.k[r].s&&(t[i].ks.k[r].s[0].c=t[i].closed),t[i].ks.k[r].e&&(t[i].ks.k[r].e[0].c=t[i].closed);else"gr"===t[i].ty&&e(t[i].it)}function i(t){var i,r,s,n,a,o,h=t.length;for(r=0;r<h;r+=1){if((i=t[r]).hasMask){var l=i.masksProperties;for(n=l.length,s=0;s<n;s+=1)if(l[s].pt.k.i)l[s].pt.k.c=l[s].cl;else for(o=l[s].pt.k.length,a=0;a<o;a+=1)l[s].pt.k[a].s&&(l[s].pt.k[a].s[0].c=l[s].cl),l[s].pt.k[a].e&&(l[s].pt.k[a].e[0].c=l[s].cl)}4===i.ty&&e(i.shapes)}}return function(e){if(s(t,e.v)&&(i(e.layers),e.assets)){var r,n=e.assets.length;for(r=0;r<n;r+=1)e.assets[r].layers&&i(e.assets[r].layers)}}}();function f(t){0===t.t.a.length&&t.t.p}var u={completeData:function(i){i.__complete||(l(i),a(i),o(i),h(i),p(i),t(i.layers,i.assets),function(i,r){if(i){var s=0,n=i.length;for(s=0;s<n;s+=1)1===i[s].t&&(i[s].data.layers=e(i[s].data.refId,r),t(i[s].data.layers,r))}}(i.chars,i.assets),i.__complete=!0)}};return u.checkColors=l,u.checkChars=o,u.checkPathProperties=h,u.checkShapes=p,u.completeLayers=t,u}()),n.assetLoader||(n.assetLoader=function(){function t(t){var e=t.getResponseHeader("content-type");return e&&"json"===t.responseType&&-1!==e.indexOf("json")||t.response&&"object"===_typeof$5(t.response)?t.response:t.response&&"string"==typeof t.response?JSON.parse(t.response):t.responseText?JSON.parse(t.responseText):null}return{load:function(e,i,r,s){var n,a=new XMLHttpRequest;try{a.responseType="json"}catch(t){}a.onreadystatechange=function(){if(4===a.readyState)if(200===a.status)r(n=t(a));else try{n=t(a),r(n)}catch(t){s&&s(t)}};try{a.open("GET",e,!0)}catch(t){a.open("GET",i+"/"+e,!0)}a.send()}}}()),"loadAnimation"===t.data.type)n.assetLoader.load(t.data.path,t.data.fullPath,function(e){n.dataManager.completeData(e),n.postMessage({id:t.data.id,payload:e,status:"success"})},function(){n.postMessage({id:t.data.id,status:"error"})});else if("complete"===t.data.type){var e=t.data.animation;n.dataManager.completeData(e),n.postMessage({id:t.data.id,payload:e,status:"success"})}else"loadData"===t.data.type&&n.assetLoader.load(t.data.path,t.data.fullPath,function(e){n.postMessage({id:t.data.id,payload:e,status:"success"})},function(){n.postMessage({id:t.data.id,status:"error"})})})).onmessage=function(t){var e=t.data,i=e.id,s=r[i];r[i]=null,"success"===e.status?s.onComplete(e.payload):s.onError&&s.onError()})}function o(t,e){var s="processId_"+(i+=1);return r[s]={onComplete:t,onError:e},s}return{loadAnimation:function(t,i,r){a();var s=o(i,r);e.postMessage({type:"loadAnimation",path:t,fullPath:window.location.origin+window.location.pathname,id:s})},loadData:function(t,i,r){a();var s=o(i,r);e.postMessage({type:"loadData",path:t,fullPath:window.location.origin+window.location.pathname,id:s})},completeAnimation:function(t,i,r){a();var s=o(i,r);e.postMessage({type:"complete",animation:t,id:s})}}}(),ImagePreloader=function(){var t=function(){var t=createTag("canvas");t.width=1,t.height=1;var e=t.getContext("2d");return e.fillStyle="rgba(0,0,0,0)",e.fillRect(0,0,1,1),t}();function e(){this.loadedAssets+=1,this.loadedAssets===this.totalImages&&this.loadedFootagesCount===this.totalFootages&&this.imagesLoadedCb&&this.imagesLoadedCb(null)}function i(){this.loadedFootagesCount+=1,this.loadedAssets===this.totalImages&&this.loadedFootagesCount===this.totalFootages&&this.imagesLoadedCb&&this.imagesLoadedCb(null)}function r(t,e,i){var r="";if(t.e)r=t.p;else if(e){var s=t.p;-1!==s.indexOf("images/")&&(s=s.split("/")[1]),r=e+s}else r=i+(t.u?t.u:"")+t.p;return r}function s(t){var e=0,i=setInterval((function(){(t.getBBox().width||e>500)&&(this._imageLoaded(),clearInterval(i)),e+=1}).bind(this),50)}function n(t){var e={assetData:t},i=r(t,this.assetsPath,this.path);return dataManager.loadData(i,(function(t){e.img=t,this._footageLoaded()}).bind(this),(function(){e.img={},this._footageLoaded()}).bind(this)),e}function a(){this._imageLoaded=e.bind(this),this._footageLoaded=i.bind(this),this.testImageLoaded=s.bind(this),this.createFootageData=n.bind(this),this.assetsPath="",this.path="",this.totalImages=0,this.totalFootages=0,this.loadedAssets=0,this.loadedFootagesCount=0,this.imagesLoadedCb=null,this.images=[]}return a.prototype={loadAssets:function(t,e){this.imagesLoadedCb=e;var i,r=t.length;for(i=0;i<r;i+=1)t[i].layers||(t[i].t&&"seq"!==t[i].t?3===t[i].t&&(this.totalFootages+=1,this.images.push(this.createFootageData(t[i]))):(this.totalImages+=1,this.images.push(this._createImageData(t[i]))))},setAssetsPath:function(t){this.assetsPath=t||""},setPath:function(t){this.path=t||""},loadedImages:function(){return this.totalImages===this.loadedAssets},loadedFootages:function(){return this.totalFootages===this.loadedFootagesCount},destroy:function(){this.imagesLoadedCb=null,this.images.length=0},getAsset:function(t){for(var e=0,i=this.images.length;e<i;){if(this.images[e].assetData===t)return this.images[e].img;e+=1}return null},createImgData:function(e){var i=r(e,this.assetsPath,this.path),s=createTag("img");s.crossOrigin="anonymous",s.addEventListener("load",this._imageLoaded,!1),s.addEventListener("error",(function(){n.img=t,this._imageLoaded()}).bind(this),!1),s.src=i;var n={img:s,assetData:e};return n},createImageData:function(e){var i=r(e,this.assetsPath,this.path),s=createNS("image");isSafari?this.testImageLoaded(s):s.addEventListener("load",this._imageLoaded,!1),s.addEventListener("error",(function(){n.img=t,this._imageLoaded()}).bind(this),!1),s.setAttributeNS("http://www.w3.org/1999/xlink","href",i),this._elementHelper.append?this._elementHelper.append(s):this._elementHelper.appendChild(s);var n={img:s,assetData:e};return n},imageLoaded:e,footageLoaded:i,setCacheType:function(t,e){"svg"===t?(this._elementHelper=e,this._createImageData=this.createImageData.bind(this)):this._createImageData=this.createImgData.bind(this)}},a}();function BaseEvent(){}BaseEvent.prototype={triggerEvent:function(t,e){if(this._cbs[t])for(var i=this._cbs[t],r=0;r<i.length;r+=1)i[r](e)},addEventListener:function(t,e){return this._cbs[t]||(this._cbs[t]=[]),this._cbs[t].push(e),(function(){this.removeEventListener(t,e)}).bind(this)},removeEventListener:function(t,e){if(e){if(this._cbs[t]){for(var i=0,r=this._cbs[t].length;i<r;)this._cbs[t][i]===e&&(this._cbs[t].splice(i,1),i-=1,r-=1),i+=1;this._cbs[t].length||(this._cbs[t]=null)}}else this._cbs[t]=null}};var markerParser=function(){function t(t){for(var e,i=t.split("\r\n"),r={},s=0,n=0;n<i.length;n+=1)2===(e=i[n].split(":")).length&&(r[e[0]]=e[1].trim(),s+=1);if(0===s)throw Error();return r}return function(e){for(var i=[],r=0;r<e.length;r+=1){var s=e[r],n={time:s.tm,duration:s.dr};try{n.payload=JSON.parse(e[r].cm)}catch(i){try{n.payload=t(e[r].cm)}catch(t){n.payload={name:e[r].cm}}}i.push(n)}return i}}(),ProjectInterface=function(){function t(t){this.compositions.push(t)}return function(){function e(t){for(var e=0,i=this.compositions.length;e<i;){if(this.compositions[e].data&&this.compositions[e].data.nm===t)return this.compositions[e].prepareFrame&&this.compositions[e].data.xt&&this.compositions[e].prepareFrame(this.currentFrame),this.compositions[e].compInterface;e+=1}return null}return e.compositions=[],e.currentFrame=0,e.registerComposition=t,e}}(),renderers={},registerRenderer=function(t,e){renderers[t]=e};function getRenderer(t){return renderers[t]}function getRegisteredRenderer(){if(renderers.canvas)return"canvas";for(var t in renderers)if(renderers[t])return t;return""}function _typeof$4(t){return(_typeof$4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var AnimationItem=function(){this._cbs=[],this.name="",this.path="",this.isLoaded=!1,this.currentFrame=0,this.currentRawFrame=0,this.firstFrame=0,this.totalFrames=0,this.frameRate=0,this.frameMult=0,this.playSpeed=1,this.playDirection=1,this.playCount=0,this.animationData={},this.assets=[],this.isPaused=!0,this.autoplay=!1,this.loop=!0,this.renderer=null,this.animationID=createElementID(),this.assetsPath="",this.timeCompleted=0,this.segmentPos=0,this.isSubframeEnabled=getSubframeEnabled(),this.segments=[],this._idle=!0,this._completedLoop=!1,this.projectInterface=ProjectInterface(),this.imagePreloader=new ImagePreloader,this.audioController=audioControllerFactory(),this.markers=[],this.configAnimation=this.configAnimation.bind(this),this.onSetupError=this.onSetupError.bind(this),this.onSegmentComplete=this.onSegmentComplete.bind(this),this.drawnFrameEvent=new BMEnterFrameEvent("drawnFrame",0,0,0),this.expressionsPlugin=getExpressionsPlugin()};extendPrototype([BaseEvent],AnimationItem),AnimationItem.prototype.setParams=function(t){(t.wrapper||t.container)&&(this.wrapper=t.wrapper||t.container);var e="svg";t.animType?e=t.animType:t.renderer&&(e=t.renderer);var i=getRenderer(e);this.renderer=new i(this,t.rendererSettings),this.imagePreloader.setCacheType(e,this.renderer.globalData.defs),this.renderer.setProjectInterface(this.projectInterface),this.animType=e,""===t.loop||null===t.loop||void 0===t.loop||!0===t.loop?this.loop=!0:!1===t.loop?this.loop=!1:this.loop=parseInt(t.loop,10),this.autoplay=!("autoplay"in t)||t.autoplay,this.name=t.name?t.name:"",this.autoloadSegments=!Object.prototype.hasOwnProperty.call(t,"autoloadSegments")||t.autoloadSegments,this.assetsPath=t.assetsPath,this.initialSegment=t.initialSegment,t.audioFactory&&this.audioController.setAudioFactory(t.audioFactory),t.animationData?this.setupAnimation(t.animationData):t.path&&(-1!==t.path.lastIndexOf("\\")?this.path=t.path.substr(0,t.path.lastIndexOf("\\")+1):this.path=t.path.substr(0,t.path.lastIndexOf("/")+1),this.fileName=t.path.substr(t.path.lastIndexOf("/")+1),this.fileName=this.fileName.substr(0,this.fileName.lastIndexOf(".json")),dataManager.loadAnimation(t.path,this.configAnimation,this.onSetupError))},AnimationItem.prototype.onSetupError=function(){this.trigger("data_failed")},AnimationItem.prototype.setupAnimation=function(t){dataManager.completeAnimation(t,this.configAnimation)},AnimationItem.prototype.setData=function(t,e){e&&"object"!==_typeof$4(e)&&(e=JSON.parse(e));var i={wrapper:t,animationData:e},r=t.attributes;i.path=r.getNamedItem("data-animation-path")?r.getNamedItem("data-animation-path").value:r.getNamedItem("data-bm-path")?r.getNamedItem("data-bm-path").value:r.getNamedItem("bm-path")?r.getNamedItem("bm-path").value:"",i.animType=r.getNamedItem("data-anim-type")?r.getNamedItem("data-anim-type").value:r.getNamedItem("data-bm-type")?r.getNamedItem("data-bm-type").value:r.getNamedItem("bm-type")?r.getNamedItem("bm-type").value:r.getNamedItem("data-bm-renderer")?r.getNamedItem("data-bm-renderer").value:r.getNamedItem("bm-renderer")?r.getNamedItem("bm-renderer").value:getRegisteredRenderer()||"canvas";var s=r.getNamedItem("data-anim-loop")?r.getNamedItem("data-anim-loop").value:r.getNamedItem("data-bm-loop")?r.getNamedItem("data-bm-loop").value:r.getNamedItem("bm-loop")?r.getNamedItem("bm-loop").value:"";"false"===s?i.loop=!1:"true"===s?i.loop=!0:""!==s&&(i.loop=parseInt(s,10)),i.autoplay="false"!==(r.getNamedItem("data-anim-autoplay")?r.getNamedItem("data-anim-autoplay").value:r.getNamedItem("data-bm-autoplay")?r.getNamedItem("data-bm-autoplay").value:!r.getNamedItem("bm-autoplay")||r.getNamedItem("bm-autoplay").value),i.name=r.getNamedItem("data-name")?r.getNamedItem("data-name").value:r.getNamedItem("data-bm-name")?r.getNamedItem("data-bm-name").value:r.getNamedItem("bm-name")?r.getNamedItem("bm-name").value:"","false"===(r.getNamedItem("data-anim-prerender")?r.getNamedItem("data-anim-prerender").value:r.getNamedItem("data-bm-prerender")?r.getNamedItem("data-bm-prerender").value:r.getNamedItem("bm-prerender")?r.getNamedItem("bm-prerender").value:"")&&(i.prerender=!1),i.path?this.setParams(i):this.trigger("destroy")},AnimationItem.prototype.includeLayers=function(t){t.op>this.animationData.op&&(this.animationData.op=t.op,this.totalFrames=Math.floor(t.op-this.animationData.ip));var e,i,r=this.animationData.layers,s=r.length,n=t.layers,a=n.length;for(i=0;i<a;i+=1)for(e=0;e<s;){if(r[e].id===n[i].id){r[e]=n[i];break}e+=1}if((t.chars||t.fonts)&&(this.renderer.globalData.fontManager.addChars(t.chars),this.renderer.globalData.fontManager.addFonts(t.fonts,this.renderer.globalData.defs)),t.assets)for(s=t.assets.length,e=0;e<s;e+=1)this.animationData.assets.push(t.assets[e]);this.animationData.__complete=!1,dataManager.completeAnimation(this.animationData,this.onSegmentComplete)},AnimationItem.prototype.onSegmentComplete=function(t){this.animationData=t;var e=getExpressionsPlugin();e&&e.initExpressions(this),this.loadNextSegment()},AnimationItem.prototype.loadNextSegment=function(){var t=this.animationData.segments;if(!t||0===t.length||!this.autoloadSegments)return this.trigger("data_ready"),void(this.timeCompleted=this.totalFrames);var e=t.shift();this.timeCompleted=e.time*this.frameRate;var i=this.path+this.fileName+"_"+this.segmentPos+".json";this.segmentPos+=1,dataManager.loadData(i,this.includeLayers.bind(this),(function(){this.trigger("data_failed")}).bind(this))},AnimationItem.prototype.loadSegments=function(){this.animationData.segments||(this.timeCompleted=this.totalFrames),this.loadNextSegment()},AnimationItem.prototype.imagesLoaded=function(){this.trigger("loaded_images"),this.checkLoaded()},AnimationItem.prototype.preloadImages=function(){this.imagePreloader.setAssetsPath(this.assetsPath),this.imagePreloader.setPath(this.path),this.imagePreloader.loadAssets(this.animationData.assets,this.imagesLoaded.bind(this))},AnimationItem.prototype.configAnimation=function(t){if(this.renderer)try{this.animationData=t,this.initialSegment?(this.totalFrames=Math.floor(this.initialSegment[1]-this.initialSegment[0]),this.firstFrame=Math.round(this.initialSegment[0])):(this.totalFrames=Math.floor(this.animationData.op-this.animationData.ip),this.firstFrame=Math.round(this.animationData.ip)),this.renderer.configAnimation(t),t.assets||(t.assets=[]),this.assets=this.animationData.assets,this.frameRate=this.animationData.fr,this.frameMult=this.animationData.fr/1e3,this.renderer.searchExtraCompositions(t.assets),this.markers=markerParser(t.markers||[]),this.trigger("config_ready"),this.preloadImages(),this.loadSegments(),this.updaFrameModifier(),this.waitForFontsLoaded(),this.isPaused&&this.audioController.pause()}catch(t){this.triggerConfigError(t)}},AnimationItem.prototype.waitForFontsLoaded=function(){this.renderer&&(this.renderer.globalData.fontManager.isLoaded?this.checkLoaded():setTimeout(this.waitForFontsLoaded.bind(this),20))},AnimationItem.prototype.checkLoaded=function(){if(!this.isLoaded&&this.renderer.globalData.fontManager.isLoaded&&(this.imagePreloader.loadedImages()||"canvas"!==this.renderer.rendererType)&&this.imagePreloader.loadedFootages()){this.isLoaded=!0;var t=getExpressionsPlugin();t&&t.initExpressions(this),this.renderer.initItems(),setTimeout((function(){this.trigger("DOMLoaded")}).bind(this),0),this.gotoFrame(),this.autoplay&&this.play()}},AnimationItem.prototype.resize=function(t,e){var i="number"==typeof t?t:void 0,r="number"==typeof e?e:void 0;this.renderer.updateContainerSize(i,r)},AnimationItem.prototype.setSubframe=function(t){this.isSubframeEnabled=!!t},AnimationItem.prototype.gotoFrame=function(){this.currentFrame=this.isSubframeEnabled?this.currentRawFrame:~~this.currentRawFrame,this.timeCompleted!==this.totalFrames&&this.currentFrame>this.timeCompleted&&(this.currentFrame=this.timeCompleted),this.trigger("enterFrame"),this.renderFrame(),this.trigger("drawnFrame")},AnimationItem.prototype.renderFrame=function(){if(!1!==this.isLoaded&&this.renderer)try{this.expressionsPlugin&&this.expressionsPlugin.resetFrame(),this.renderer.renderFrame(this.currentFrame+this.firstFrame)}catch(t){this.triggerRenderFrameError(t)}},AnimationItem.prototype.play=function(t){t&&this.name!==t||!0===this.isPaused&&(this.isPaused=!1,this.trigger("_play"),this.audioController.resume(),this._idle&&(this._idle=!1,this.trigger("_active")))},AnimationItem.prototype.pause=function(t){t&&this.name!==t||!1===this.isPaused&&(this.isPaused=!0,this.trigger("_pause"),this._idle=!0,this.trigger("_idle"),this.audioController.pause())},AnimationItem.prototype.togglePause=function(t){t&&this.name!==t||(!0===this.isPaused?this.play():this.pause())},AnimationItem.prototype.stop=function(t){t&&this.name!==t||(this.pause(),this.playCount=0,this._completedLoop=!1,this.setCurrentRawFrameValue(0))},AnimationItem.prototype.getMarkerData=function(t){for(var e,i=0;i<this.markers.length;i+=1)if((e=this.markers[i]).payload&&e.payload.name===t)return e;return null},AnimationItem.prototype.goToAndStop=function(t,e,i){if(!i||this.name===i){if(isNaN(Number(t))){var r=this.getMarkerData(t);r&&this.goToAndStop(r.time,!0)}else e?this.setCurrentRawFrameValue(t):this.setCurrentRawFrameValue(t*this.frameModifier);this.pause()}},AnimationItem.prototype.goToAndPlay=function(t,e,i){if(!i||this.name===i){var r=Number(t);if(isNaN(r)){var s=this.getMarkerData(t);s&&(s.duration?this.playSegments([s.time,s.time+s.duration],!0):this.goToAndStop(s.time,!0))}else this.goToAndStop(r,e,i);this.play()}},AnimationItem.prototype.advanceTime=function(t){if(!0!==this.isPaused&&!1!==this.isLoaded){var e=this.currentRawFrame+t*this.frameModifier,i=!1;e>=this.totalFrames-1&&this.frameModifier>0?this.loop&&this.playCount!==this.loop?e>=this.totalFrames?(this.playCount+=1,this.checkSegments(e%this.totalFrames)||(this.setCurrentRawFrameValue(e%this.totalFrames),this._completedLoop=!0,this.trigger("loopComplete"))):this.setCurrentRawFrameValue(e):this.checkSegments(e>this.totalFrames?e%this.totalFrames:0)||(i=!0,e=this.totalFrames-1):e<0?this.checkSegments(e%this.totalFrames)||(!this.loop||this.playCount--<=0&&!0!==this.loop?(i=!0,e=0):(this.setCurrentRawFrameValue(this.totalFrames+e%this.totalFrames),this._completedLoop?this.trigger("loopComplete"):this._completedLoop=!0)):this.setCurrentRawFrameValue(e),i&&(this.setCurrentRawFrameValue(e),this.pause(),this.trigger("complete"))}},AnimationItem.prototype.adjustSegment=function(t,e){this.playCount=0,t[1]<t[0]?(this.frameModifier>0&&(this.playSpeed<0?this.setSpeed(-this.playSpeed):this.setDirection(-1)),this.totalFrames=t[0]-t[1],this.timeCompleted=this.totalFrames,this.firstFrame=t[1],this.setCurrentRawFrameValue(this.totalFrames-.001-e)):t[1]>t[0]&&(this.frameModifier<0&&(this.playSpeed<0?this.setSpeed(-this.playSpeed):this.setDirection(1)),this.totalFrames=t[1]-t[0],this.timeCompleted=this.totalFrames,this.firstFrame=t[0],this.setCurrentRawFrameValue(.001+e)),this.trigger("segmentStart")},AnimationItem.prototype.setSegment=function(t,e){var i=-1;this.isPaused&&(this.currentRawFrame+this.firstFrame<t?i=t:this.currentRawFrame+this.firstFrame>e&&(i=e-t)),this.firstFrame=t,this.totalFrames=e-t,this.timeCompleted=this.totalFrames,-1!==i&&this.goToAndStop(i,!0)},AnimationItem.prototype.playSegments=function(t,e){if(e&&(this.segments.length=0),"object"===_typeof$4(t[0])){var i,r=t.length;for(i=0;i<r;i+=1)this.segments.push(t[i])}else this.segments.push(t);this.segments.length&&e&&this.adjustSegment(this.segments.shift(),0),this.isPaused&&this.play()},AnimationItem.prototype.resetSegments=function(t){this.segments.length=0,this.segments.push([this.animationData.ip,this.animationData.op]),t&&this.checkSegments(0)},AnimationItem.prototype.checkSegments=function(t){return!!this.segments.length&&(this.adjustSegment(this.segments.shift(),t),!0)},AnimationItem.prototype.destroy=function(t){t&&this.name!==t||!this.renderer||(this.renderer.destroy(),this.imagePreloader.destroy(),this.trigger("destroy"),this._cbs=null,this.onEnterFrame=null,this.onLoopComplete=null,this.onComplete=null,this.onSegmentStart=null,this.onDestroy=null,this.renderer=null,this.expressionsPlugin=null,this.imagePreloader=null,this.projectInterface=null)},AnimationItem.prototype.setCurrentRawFrameValue=function(t){this.currentRawFrame=t,this.gotoFrame()},AnimationItem.prototype.setSpeed=function(t){this.playSpeed=t,this.updaFrameModifier()},AnimationItem.prototype.setDirection=function(t){this.playDirection=t<0?-1:1,this.updaFrameModifier()},AnimationItem.prototype.setLoop=function(t){this.loop=t},AnimationItem.prototype.setVolume=function(t,e){e&&this.name!==e||this.audioController.setVolume(t)},AnimationItem.prototype.getVolume=function(){return this.audioController.getVolume()},AnimationItem.prototype.mute=function(t){t&&this.name!==t||this.audioController.mute()},AnimationItem.prototype.unmute=function(t){t&&this.name!==t||this.audioController.unmute()},AnimationItem.prototype.updaFrameModifier=function(){this.frameModifier=this.frameMult*this.playSpeed*this.playDirection,this.audioController.setRate(this.playSpeed*this.playDirection)},AnimationItem.prototype.getPath=function(){return this.path},AnimationItem.prototype.getAssetsPath=function(t){var e="";if(t.e)e=t.p;else if(this.assetsPath){var i=t.p;-1!==i.indexOf("images/")&&(i=i.split("/")[1]),e=this.assetsPath+i}else e=this.path+(t.u?t.u:"")+t.p;return e},AnimationItem.prototype.getAssetData=function(t){for(var e=0,i=this.assets.length;e<i;){if(t===this.assets[e].id)return this.assets[e];e+=1}return null},AnimationItem.prototype.hide=function(){this.renderer.hide()},AnimationItem.prototype.show=function(){this.renderer.show()},AnimationItem.prototype.getDuration=function(t){return t?this.totalFrames:this.totalFrames/this.frameRate},AnimationItem.prototype.updateDocumentData=function(t,e,i){try{this.renderer.getElementByPath(t).updateDocumentData(e,i)}catch(t){}},AnimationItem.prototype.trigger=function(t){if(this._cbs&&this._cbs[t])switch(t){case"enterFrame":this.triggerEvent(t,new BMEnterFrameEvent(t,this.currentFrame,this.totalFrames,this.frameModifier));break;case"drawnFrame":this.drawnFrameEvent.currentTime=this.currentFrame,this.drawnFrameEvent.totalTime=this.totalFrames,this.drawnFrameEvent.direction=this.frameModifier,this.triggerEvent(t,this.drawnFrameEvent);break;case"loopComplete":this.triggerEvent(t,new BMCompleteLoopEvent(t,this.loop,this.playCount,this.frameMult));break;case"complete":this.triggerEvent(t,new BMCompleteEvent(t,this.frameMult));break;case"segmentStart":this.triggerEvent(t,new BMSegmentStartEvent(t,this.firstFrame,this.totalFrames));break;case"destroy":this.triggerEvent(t,new BMDestroyEvent(t,this));break;default:this.triggerEvent(t)}"enterFrame"===t&&this.onEnterFrame&&this.onEnterFrame.call(this,new BMEnterFrameEvent(t,this.currentFrame,this.totalFrames,this.frameMult)),"loopComplete"===t&&this.onLoopComplete&&this.onLoopComplete.call(this,new BMCompleteLoopEvent(t,this.loop,this.playCount,this.frameMult)),"complete"===t&&this.onComplete&&this.onComplete.call(this,new BMCompleteEvent(t,this.frameMult)),"segmentStart"===t&&this.onSegmentStart&&this.onSegmentStart.call(this,new BMSegmentStartEvent(t,this.firstFrame,this.totalFrames)),"destroy"===t&&this.onDestroy&&this.onDestroy.call(this,new BMDestroyEvent(t,this))},AnimationItem.prototype.triggerRenderFrameError=function(t){var e=new BMRenderFrameErrorEvent(t,this.currentFrame);this.triggerEvent("error",e),this.onError&&this.onError.call(this,e)},AnimationItem.prototype.triggerConfigError=function(t){var e=new BMConfigErrorEvent(t,this.currentFrame);this.triggerEvent("error",e),this.onError&&this.onError.call(this,e)};var animationManager=function(){var t={},e=[],i=0,r=0,s=0,n=!0,a=!1;function o(t){for(var i=0,s=t.target;i<r;)e[i].animation===s&&(e.splice(i,1),i-=1,r-=1,s.isPaused||p()),i+=1}function h(t,i){if(!t)return null;for(var s=0;s<r;){if(e[s].elem===t&&null!==e[s].elem)return e[s].animation;s+=1}var n=new AnimationItem;return f(n,t),n.setData(t,i),n}function l(){s+=1,m()}function p(){s-=1}function f(t,i){t.addEventListener("destroy",o),t.addEventListener("_active",l),t.addEventListener("_idle",p),e.push({elem:i,animation:t}),r+=1}function u(t){var o,h=t-i;for(o=0;o<r;o+=1)e[o].animation.advanceTime(h);i=t,s&&!a?window.requestAnimationFrame(u):n=!0}function c(t){i=t,window.requestAnimationFrame(u)}function m(){!a&&s&&n&&(window.requestAnimationFrame(c),n=!1)}return t.registerAnimation=h,t.loadAnimation=function(t){var e=new AnimationItem;return f(e,null),e.setParams(t),e},t.setSpeed=function(t,i){var s;for(s=0;s<r;s+=1)e[s].animation.setSpeed(t,i)},t.setDirection=function(t,i){var s;for(s=0;s<r;s+=1)e[s].animation.setDirection(t,i)},t.play=function(t){var i;for(i=0;i<r;i+=1)e[i].animation.play(t)},t.pause=function(t){var i;for(i=0;i<r;i+=1)e[i].animation.pause(t)},t.stop=function(t){var i;for(i=0;i<r;i+=1)e[i].animation.stop(t)},t.togglePause=function(t){var i;for(i=0;i<r;i+=1)e[i].animation.togglePause(t)},t.searchAnimations=function(t,e,i){var r,s=[].concat([].slice.call(document.getElementsByClassName("lottie")),[].slice.call(document.getElementsByClassName("bodymovin"))),n=s.length;for(r=0;r<n;r+=1)i&&s[r].setAttribute("data-bm-type",i),h(s[r],t);if(e&&0===n){i||(i="svg");var a=document.getElementsByTagName("body")[0];a.innerText="";var o=createTag("div");o.style.width="100%",o.style.height="100%",o.setAttribute("data-bm-type",i),a.appendChild(o),h(o,t)}},t.resize=function(){var t;for(t=0;t<r;t+=1)e[t].animation.resize()},t.goToAndStop=function(t,i,s){var n;for(n=0;n<r;n+=1)e[n].animation.goToAndStop(t,i,s)},t.destroy=function(t){var i;for(i=r-1;i>=0;i-=1)e[i].animation.destroy(t)},t.freeze=function(){a=!0},t.unfreeze=function(){a=!1,m()},t.setVolume=function(t,i){var s;for(s=0;s<r;s+=1)e[s].animation.setVolume(t,i)},t.mute=function(t){var i;for(i=0;i<r;i+=1)e[i].animation.mute(t)},t.unmute=function(t){var i;for(i=0;i<r;i+=1)e[i].animation.unmute(t)},t.getRegisteredAnimations=function(){var t,i=e.length,r=[];for(t=0;t<i;t+=1)r.push(e[t].animation);return r},t}(),BezierFactory=function(){var t={getBezierEasing:function(t,i,r,s,n){var a=n||("bez_"+t+"_"+i+"_"+r+"_"+s).replace(/\./g,"p");if(e[a])return e[a];var o=new l([t,i,r,s]);return e[a]=o,o}},e={},i=.1,r="function"==typeof Float32Array;function s(t,e){return 1-3*e+3*t}function n(t,e){return 3*e-6*t}function a(t){return 3*t}function o(t,e,i){return((s(e,i)*t+n(e,i))*t+a(e))*t}function h(t,e,i){return 3*s(e,i)*t*t+2*n(e,i)*t+a(e)}function l(t){this._p=t,this._mSampleValues=r?new Float32Array(11):Array(11),this._precomputed=!1,this.get=this.get.bind(this)}return l.prototype={get:function(t){var e=this._p[0],i=this._p[1],r=this._p[2],s=this._p[3];return this._precomputed||this._precompute(),e===i&&r===s?t:0===t?0:1===t?1:o(this._getTForX(t),i,s)},_precompute:function(){var t=this._p[0],e=this._p[1],i=this._p[2],r=this._p[3];this._precomputed=!0,t===e&&i===r||this._calcSampleValues()},_calcSampleValues:function(){for(var t=this._p[0],e=this._p[2],r=0;r<11;++r)this._mSampleValues[r]=o(r*i,t,e)},_getTForX:function(t){for(var e=this._p[0],r=this._p[2],s=this._mSampleValues,n=0,a=1;10!==a&&s[a]<=t;++a)n+=i;var l=n+(t-s[--a])/(s[a+1]-s[a])*i,p=h(l,e,r);return p>=.001?function(t,e,i,r){for(var s=0;s<4;++s){var n=h(e,i,r);if(0===n)break;e-=(o(e,i,r)-t)/n}return e}(t,l,e,r):0===p?l:function(t,e,i,r,s){var n,a,h=0;do(n=o(a=e+(i-e)/2,r,s)-t)>0?i=a:e=a;while(Math.abs(n)>1e-7&&++h<10);return a}(t,n,n+i,e,r)}},t}(),pooling={double:function(t){return t.concat(createSizedArray(t.length))}},poolFactory=function(t,e,i){var r=0,s=t,n=createSizedArray(s);return{newElement:function(){return r?n[r-=1]:e()},release:function(t){r===s&&(n=pooling.double(n),s*=2),i&&i(t),n[r]=t,r+=1}}},bezierLengthPool=poolFactory(8,function(){return{addedLength:0,percents:createTypedArray("float32",getDefaultCurveSegments()),lengths:createTypedArray("float32",getDefaultCurveSegments())}}),segmentsLengthPool=poolFactory(8,function(){return{lengths:[],totalLength:0}},function(t){var e,i=t.lengths.length;for(e=0;e<i;e+=1)bezierLengthPool.release(t.lengths[e]);t.lengths.length=0});function bezFunction(){var t=Math;function e(t,e,i,r,s,n){var a=t*r+e*s+i*n-s*r-n*t-i*e;return a>-.001&&a<.001}var i=function(t,e,i,r){var s,n,a,o,h,l,p=getDefaultCurveSegments(),f=0,u=[],c=[],m=bezierLengthPool.newElement();for(a=i.length,s=0;s<p;s+=1){for(h=s/(p-1),l=0,n=0;n<a;n+=1)o=bmPow(1-h,3)*t[n]+3*bmPow(1-h,2)*h*i[n]+3*(1-h)*bmPow(h,2)*r[n]+bmPow(h,3)*e[n],u[n]=o,null!==c[n]&&(l+=bmPow(u[n]-c[n],2)),c[n]=u[n];l&&(f+=l=bmSqrt(l)),m.percents[s]=h,m.lengths[s]=f}return m.addedLength=f,m};function r(t){this.segmentLength=0,this.points=Array(t)}function s(t,e){this.partialLength=t,this.point=e}var n,a=(n={},function(t,i,a,o){var h=(t[0]+"_"+t[1]+"_"+i[0]+"_"+i[1]+"_"+a[0]+"_"+a[1]+"_"+o[0]+"_"+o[1]).replace(/\./g,"p");if(!n[h]){var l,p,f,u,c,m,d,g=getDefaultCurveSegments(),v=0,y=null;2===t.length&&(t[0]!==i[0]||t[1]!==i[1])&&e(t[0],t[1],i[0],i[1],t[0]+a[0],t[1]+a[1])&&e(t[0],t[1],i[0],i[1],i[0]+o[0],i[1]+o[1])&&(g=2);var b=new r(g);for(f=a.length,l=0;l<g;l+=1){for(d=createSizedArray(f),c=l/(g-1),m=0,p=0;p<f;p+=1)u=bmPow(1-c,3)*t[p]+3*bmPow(1-c,2)*c*(t[p]+a[p])+3*(1-c)*bmPow(c,2)*(i[p]+o[p])+bmPow(c,3)*i[p],d[p]=u,null!==y&&(m+=bmPow(d[p]-y[p],2));v+=m=bmSqrt(m),b.points[l]=new s(m,d),y=d}b.segmentLength=v,n[h]=b}return n[h]});function o(t,e){var i=e.percents,r=e.lengths,s=i.length,n=bmFloor((s-1)*t),a=t*e.addedLength,o=0;if(n===s-1||0===n||a===r[n])return i[n];for(var h=r[n]>a?-1:1,l=!0;l;)if(r[n]<=a&&r[n+1]>a?(o=(a-r[n])/(r[n+1]-r[n]),l=!1):n+=h,n<0||n>=s-1){if(n===s-1)return i[n];l=!1}return i[n]+(i[n+1]-i[n])*o}var h=createTypedArray("float32",8);return{getSegmentsLength:function(t){var e,r=segmentsLengthPool.newElement(),s=t.c,n=t.v,a=t.o,o=t.i,h=t._length,l=r.lengths,p=0;for(e=0;e<h-1;e+=1)l[e]=i(n[e],n[e+1],a[e],o[e+1]),p+=l[e].addedLength;return s&&h&&(l[e]=i(n[e],n[0],a[e],o[0]),p+=l[e].addedLength),r.totalLength=p,r},getNewSegment:function(e,i,r,s,n,a,l){n<0?n=0:n>1&&(n=1);var p,f=o(n,l),u=o(a=a>1?1:a,l),c=e.length,m=1-f,d=1-u,g=m*m*m,v=f*m*m*3,y=f*f*m*3,b=f*f*f,x=m*m*d,_=f*m*d+m*f*d+m*m*u,A=f*f*d+m*f*u+f*m*u,k=f*f*u,w=m*d*d,P=f*d*d+m*u*d+m*d*u,C=f*u*d+m*u*u+f*d*u,S=f*u*u,D=d*d*d,T=u*d*d+d*u*d+d*d*u,E=u*u*d+d*u*u+u*d*u,M=u*u*u;for(p=0;p<c;p+=1)h[4*p]=t.round(1e3*(g*e[p]+v*r[p]+y*s[p]+b*i[p]))/1e3,h[4*p+1]=t.round(1e3*(x*e[p]+_*r[p]+A*s[p]+k*i[p]))/1e3,h[4*p+2]=t.round(1e3*(w*e[p]+P*r[p]+C*s[p]+S*i[p]))/1e3,h[4*p+3]=t.round(1e3*(D*e[p]+T*r[p]+E*s[p]+M*i[p]))/1e3;return h},getPointInSegment:function(e,i,r,s,n,a){var h=o(n,a),l=1-h;return[t.round(1e3*(l*l*l*e[0]+(h*l*l+l*h*l+l*l*h)*r[0]+(h*h*l+l*h*h+h*l*h)*s[0]+h*h*h*i[0]))/1e3,t.round(1e3*(l*l*l*e[1]+(h*l*l+l*h*l+l*l*h)*r[1]+(h*h*l+l*h*h+h*l*h)*s[1]+h*h*h*i[1]))/1e3]},buildBezierData:a,pointOnLine2D:e,pointOnLine3D:function(i,r,s,n,a,o,h,l,p){if(0===s&&0===o&&0===p)return e(i,r,n,a,h,l);var f,u=t.sqrt(t.pow(n-i,2)+t.pow(a-r,2)+t.pow(o-s,2)),c=t.sqrt(t.pow(h-i,2)+t.pow(l-r,2)+t.pow(p-s,2)),m=t.sqrt(t.pow(h-n,2)+t.pow(l-a,2)+t.pow(p-o,2));return(f=u>c?u>m?u-c-m:m-c-u:m>c?m-c-u:c-u-m)>-1e-4&&f<1e-4}}}var bez=bezFunction(),initFrame=initialDefaultFrame,mathAbs=Math.abs;function interpolateValue(t,e){var i,r,s,n,a,o,h=this.offsetTime;"multidimensional"===this.propType&&(o=createTypedArray("float32",this.pv.length));for(var l,p,f,u,c,m,d,g,v,y=e.lastIndex,b=y,x=this.keyframes.length-1,_=!0;_;){if(l=this.keyframes[b],p=this.keyframes[b+1],b===x-1&&t>=p.t-h){l.h&&(l=p),y=0;break}if(p.t-h>t){y=b;break}b<x-1?b+=1:(y=0,_=!1)}f=this.keyframesMetadata[b]||{};var A,k=p.t-h,w=l.t-h;if(l.to){f.bezierData||(f.bezierData=bez.buildBezierData(l.s,p.s||l.e,l.to,l.ti));var P=f.bezierData;if(t>=k||t<w){var C=t>=k?P.points.length-1:0;for(c=P.points[C].point.length,u=0;u<c;u+=1)o[u]=P.points[C].point[u]}else{f.__fnct?v=f.__fnct:f.__fnct=v=BezierFactory.getBezierEasing(l.o.x,l.o.y,l.i.x,l.i.y,l.n).get,m=v((t-w)/(k-w));var S,D=P.segmentLength*m,T=e.lastFrame<t&&e._lastKeyframeIndex===b?e._lastAddedLength:0;for(g=e.lastFrame<t&&e._lastKeyframeIndex===b?e._lastPoint:0,_=!0,d=P.points.length;_;){if(T+=P.points[g].partialLength,0===D||0===m||g===P.points.length-1){for(c=P.points[g].point.length,u=0;u<c;u+=1)o[u]=P.points[g].point[u];break}if(D>=T&&D<T+P.points[g+1].partialLength){for(S=(D-T)/P.points[g+1].partialLength,c=P.points[g].point.length,u=0;u<c;u+=1)o[u]=P.points[g].point[u]+(P.points[g+1].point[u]-P.points[g].point[u])*S;break}g<d-1?g+=1:_=!1}e._lastPoint=g,e._lastAddedLength=T-P.points[g].partialLength,e._lastKeyframeIndex=b}}else if(x=l.s.length,A=p.s||l.e,this.sh&&1!==l.h)t>=k?(o[0]=A[0],o[1]=A[1],o[2]=A[2]):t<=w?(o[0]=l.s[0],o[1]=l.s[1],o[2]=l.s[2]):quaternionToEuler(o,slerp(createQuaternion(l.s),createQuaternion(A),(t-w)/(k-w)));else for(b=0;b<x;b+=1)1!==l.h&&(t>=k?m=1:t<w?m=0:(l.o.x.constructor===Array?(f.__fnct||(f.__fnct=[]),f.__fnct[b]?v=f.__fnct[b]:(i=void 0===l.o.x[b]?l.o.x[0]:l.o.x[b],r=void 0===l.o.y[b]?l.o.y[0]:l.o.y[b],s=void 0===l.i.x[b]?l.i.x[0]:l.i.x[b],n=void 0===l.i.y[b]?l.i.y[0]:l.i.y[b],v=BezierFactory.getBezierEasing(i,r,s,n).get,f.__fnct[b]=v)):f.__fnct?v=f.__fnct:(i=l.o.x,r=l.o.y,s=l.i.x,n=l.i.y,v=BezierFactory.getBezierEasing(i,r,s,n).get,l.keyframeMetadata=v),m=v((t-w)/(k-w)))),A=p.s||l.e,a=1===l.h?l.s[b]:l.s[b]+(A[b]-l.s[b])*m,"multidimensional"===this.propType?o[b]=a:o=a;return e.lastIndex=y,o}function slerp(t,e,i){var r,s,n,a,o,h=[],l=t[0],p=t[1],f=t[2],u=t[3],c=e[0],m=e[1],d=e[2],g=e[3];return(s=l*c+p*m+f*d+u*g)<0&&(s=-s,c=-c,m=-m,d=-d,g=-g),1-s>1e-6?(n=Math.sin(r=Math.acos(s)),a=Math.sin((1-i)*r)/n,o=Math.sin(i*r)/n):(a=1-i,o=i),h[0]=a*l+o*c,h[1]=a*p+o*m,h[2]=a*f+o*d,h[3]=a*u+o*g,h}function quaternionToEuler(t,e){var i=e[0],r=e[1],s=e[2],n=e[3],a=Math.atan2(2*r*n-2*i*s,1-2*r*r-2*s*s),o=Math.asin(2*i*r+2*s*n),h=Math.atan2(2*i*n-2*r*s,1-2*i*i-2*s*s);t[0]=a/degToRads,t[1]=o/degToRads,t[2]=h/degToRads}function createQuaternion(t){var e=t[0]*degToRads,i=t[1]*degToRads,r=t[2]*degToRads,s=Math.cos(e/2),n=Math.cos(i/2),a=Math.cos(r/2),o=Math.sin(e/2),h=Math.sin(i/2),l=Math.sin(r/2);return[o*h*a+s*n*l,o*n*a+s*h*l,s*h*a-o*n*l,s*n*a-o*h*l]}function getValueAtCurrentTime(){var t=this.comp.renderedFrame-this.offsetTime,e=this.keyframes[0].t-this.offsetTime,i=this.keyframes[this.keyframes.length-1].t-this.offsetTime;if(!(t===this._caching.lastFrame||this._caching.lastFrame!==initFrame&&(this._caching.lastFrame>=i&&t>=i||this._caching.lastFrame<e&&t<e))){this._caching.lastFrame>=t&&(this._caching._lastKeyframeIndex=-1,this._caching.lastIndex=0);var r=this.interpolateValue(t,this._caching);this.pv=r}return this._caching.lastFrame=t,this.pv}function setVValue(t){var e;if("unidimensional"===this.propType)e=t*this.mult,mathAbs(this.v-e)>1e-5&&(this.v=e,this._mdf=!0);else for(var i=0,r=this.v.length;i<r;)e=t[i]*this.mult,mathAbs(this.v[i]-e)>1e-5&&(this.v[i]=e,this._mdf=!0),i+=1}function processEffectsSequence(){if(this.elem.globalData.frameId!==this.frameId&&this.effectsSequence.length)if(this.lock)this.setVValue(this.pv);else{this.lock=!0,this._mdf=this._isFirstFrame;var t,e=this.effectsSequence.length,i=this.kf?this.pv:this.data.k;for(t=0;t<e;t+=1)i=this.effectsSequence[t](i);this.setVValue(i),this._isFirstFrame=!1,this.lock=!1,this.frameId=this.elem.globalData.frameId}}function addEffect(t){this.effectsSequence.push(t),this.container.addDynamicProperty(this)}function ValueProperty(t,e,i,r){this.propType="unidimensional",this.mult=i||1,this.data=e,this.v=i?e.k*i:e.k,this.pv=e.k,this._mdf=!1,this.elem=t,this.container=r,this.comp=t.comp,this.k=!1,this.kf=!1,this.vel=0,this.effectsSequence=[],this._isFirstFrame=!0,this.getValue=processEffectsSequence,this.setVValue=setVValue,this.addEffect=addEffect}function MultiDimensionalProperty(t,e,i,r){this.propType="multidimensional",this.mult=i||1,this.data=e,this._mdf=!1,this.elem=t,this.container=r,this.comp=t.comp,this.k=!1,this.kf=!1,this.frameId=-1;var s,n=e.k.length;for(this.v=createTypedArray("float32",n),this.pv=createTypedArray("float32",n),this.vel=createTypedArray("float32",n),s=0;s<n;s+=1)this.v[s]=e.k[s]*this.mult,this.pv[s]=e.k[s];this._isFirstFrame=!0,this.effectsSequence=[],this.getValue=processEffectsSequence,this.setVValue=setVValue,this.addEffect=addEffect}function KeyframedValueProperty(t,e,i,r){this.propType="unidimensional",this.keyframes=e.k,this.keyframesMetadata=[],this.offsetTime=t.data.st,this.frameId=-1,this._caching={lastFrame:initFrame,lastIndex:0,value:0,_lastKeyframeIndex:-1},this.k=!0,this.kf=!0,this.data=e,this.mult=i||1,this.elem=t,this.container=r,this.comp=t.comp,this.v=initFrame,this.pv=initFrame,this._isFirstFrame=!0,this.getValue=processEffectsSequence,this.setVValue=setVValue,this.interpolateValue=interpolateValue,this.effectsSequence=[getValueAtCurrentTime.bind(this)],this.addEffect=addEffect}function KeyframedMultidimensionalProperty(t,e,i,r){this.propType="multidimensional";var s,n,a,o,h,l=e.k.length;for(s=0;s<l-1;s+=1)e.k[s].to&&e.k[s].s&&e.k[s+1]&&e.k[s+1].s&&(n=e.k[s].s,a=e.k[s+1].s,o=e.k[s].to,h=e.k[s].ti,(2===n.length&&(n[0]!==a[0]||n[1]!==a[1])&&bez.pointOnLine2D(n[0],n[1],a[0],a[1],n[0]+o[0],n[1]+o[1])&&bez.pointOnLine2D(n[0],n[1],a[0],a[1],a[0]+h[0],a[1]+h[1])||3===n.length&&(n[0]!==a[0]||n[1]!==a[1]||n[2]!==a[2])&&bez.pointOnLine3D(n[0],n[1],n[2],a[0],a[1],a[2],n[0]+o[0],n[1]+o[1],n[2]+o[2])&&bez.pointOnLine3D(n[0],n[1],n[2],a[0],a[1],a[2],a[0]+h[0],a[1]+h[1],a[2]+h[2]))&&(e.k[s].to=null,e.k[s].ti=null),n[0]===a[0]&&n[1]===a[1]&&0===o[0]&&0===o[1]&&0===h[0]&&0===h[1]&&(2===n.length||n[2]===a[2]&&0===o[2]&&0===h[2])&&(e.k[s].to=null,e.k[s].ti=null));this.effectsSequence=[getValueAtCurrentTime.bind(this)],this.data=e,this.keyframes=e.k,this.keyframesMetadata=[],this.offsetTime=t.data.st,this.k=!0,this.kf=!0,this._isFirstFrame=!0,this.mult=i||1,this.elem=t,this.container=r,this.comp=t.comp,this.getValue=processEffectsSequence,this.setVValue=setVValue,this.interpolateValue=interpolateValue,this.frameId=-1;var p=e.k[0].s.length;for(this.v=createTypedArray("float32",p),this.pv=createTypedArray("float32",p),s=0;s<p;s+=1)this.v[s]=initFrame,this.pv[s]=initFrame;this._caching={lastFrame:initFrame,lastIndex:0,value:createTypedArray("float32",p)},this.addEffect=addEffect}var PropertyFactory={getProp:function(t,e,i,r,s){var n;if(e.sid&&(e=t.globalData.slotManager.getProp(e)),e.k.length)if("number"==typeof e.k[0])n=new MultiDimensionalProperty(t,e,r,s);else switch(i){case 0:n=new KeyframedValueProperty(t,e,r,s);break;case 1:n=new KeyframedMultidimensionalProperty(t,e,r,s)}else n=new ValueProperty(t,e,r,s);return n.effectsSequence.length&&s.addDynamicProperty(n),n}};function DynamicPropertyContainer(){}DynamicPropertyContainer.prototype={addDynamicProperty:function(t){-1===this.dynamicProperties.indexOf(t)&&(this.dynamicProperties.push(t),this.container.addDynamicProperty(this),this._isAnimated=!0)},iterateDynamicProperties:function(){this._mdf=!1;var t,e=this.dynamicProperties.length;for(t=0;t<e;t+=1)this.dynamicProperties[t].getValue(),this.dynamicProperties[t]._mdf&&(this._mdf=!0)},initDynamicPropertyContainer:function(t){this.container=t,this.dynamicProperties=[],this._mdf=!1,this._isAnimated=!1}};var pointPool=poolFactory(8,function(){return createTypedArray("float32",2)});function ShapePath(){this.c=!1,this._length=0,this._maxLength=8,this.v=createSizedArray(this._maxLength),this.o=createSizedArray(this._maxLength),this.i=createSizedArray(this._maxLength)}ShapePath.prototype.setPathData=function(t,e){this.c=t,this.setLength(e);for(var i=0;i<e;)this.v[i]=pointPool.newElement(),this.o[i]=pointPool.newElement(),this.i[i]=pointPool.newElement(),i+=1},ShapePath.prototype.setLength=function(t){for(;this._maxLength<t;)this.doubleArrayLength();this._length=t},ShapePath.prototype.doubleArrayLength=function(){this.v=this.v.concat(createSizedArray(this._maxLength)),this.i=this.i.concat(createSizedArray(this._maxLength)),this.o=this.o.concat(createSizedArray(this._maxLength)),this._maxLength*=2},ShapePath.prototype.setXYAt=function(t,e,i,r,s){var n;switch(this._length=Math.max(this._length,r+1),this._length>=this._maxLength&&this.doubleArrayLength(),i){case"v":n=this.v;break;case"i":n=this.i;break;case"o":n=this.o;break;default:n=[]}n[r]&&(!n[r]||s)||(n[r]=pointPool.newElement()),n[r][0]=t,n[r][1]=e},ShapePath.prototype.setTripleAt=function(t,e,i,r,s,n,a,o){this.setXYAt(t,e,"v",a,o),this.setXYAt(i,r,"o",a,o),this.setXYAt(s,n,"i",a,o)},ShapePath.prototype.reverse=function(){var t=new ShapePath;t.setPathData(this.c,this._length);var e=this.v,i=this.o,r=this.i,s=0;this.c&&(t.setTripleAt(e[0][0],e[0][1],r[0][0],r[0][1],i[0][0],i[0][1],0,!1),s=1);var n,a=this._length-1,o=this._length;for(n=s;n<o;n+=1)t.setTripleAt(e[a][0],e[a][1],r[a][0],r[a][1],i[a][0],i[a][1],n,!1),a-=1;return t},ShapePath.prototype.length=function(){return this._length};var factory,shapePool=(factory=poolFactory(4,function(){return new ShapePath},function(t){var e,i=t._length;for(e=0;e<i;e+=1)pointPool.release(t.v[e]),pointPool.release(t.i[e]),pointPool.release(t.o[e]),t.v[e]=null,t.i[e]=null,t.o[e]=null;t._length=0,t.c=!1}),factory.clone=function(t){var e,i=factory.newElement(),r=void 0===t._length?t.v.length:t._length;for(i.setLength(r),i.c=t.c,e=0;e<r;e+=1)i.setTripleAt(t.v[e][0],t.v[e][1],t.o[e][0],t.o[e][1],t.i[e][0],t.i[e][1],e);return i},factory);function ShapeCollection(){this._length=0,this._maxLength=4,this.shapes=createSizedArray(this._maxLength)}ShapeCollection.prototype.addShape=function(t){this._length===this._maxLength&&(this.shapes=this.shapes.concat(createSizedArray(this._maxLength)),this._maxLength*=2),this.shapes[this._length]=t,this._length+=1},ShapeCollection.prototype.releaseShapes=function(){var t;for(t=0;t<this._length;t+=1)shapePool.release(this.shapes[t]);this._length=0};var ob,_length,_maxLength,pool,shapeCollectionPool=(ob={newShapeCollection:function(){return _length?pool[_length-=1]:new ShapeCollection},release:function(t){var e,i=t._length;for(e=0;e<i;e+=1)shapePool.release(t.shapes[e]);t._length=0,_length===_maxLength&&(pool=pooling.double(pool),_maxLength*=2),pool[_length]=t,_length+=1}},_length=0,_maxLength=4,pool=createSizedArray(_maxLength),ob),ShapePropertyFactory=function(){var t=-999999;function e(t,e,i){var r,s,n,a,o,h,l,p,f,u=i.lastIndex,c=this.keyframes;if(t<c[0].t-this.offsetTime)r=c[0].s[0],n=!0,u=0;else if(t>=c[c.length-1].t-this.offsetTime)r=c[c.length-1].s?c[c.length-1].s[0]:c[c.length-2].e[0],n=!0;else{for(var m,d,g,v,y=u,b=c.length-1,x=!0;x&&(d=c[y],!((g=c[y+1]).t-this.offsetTime>t));)y<b-1?y+=1:x=!1;v=this.keyframesMetadata[y]||{},u=y,(n=1===d.h)||(t>=g.t-this.offsetTime?p=1:t<d.t-this.offsetTime?p=0:(v.__fnct?m=v.__fnct:v.__fnct=m=BezierFactory.getBezierEasing(d.o.x,d.o.y,d.i.x,d.i.y).get,p=m((t-(d.t-this.offsetTime))/(g.t-this.offsetTime-(d.t-this.offsetTime)))),s=g.s?g.s[0]:d.e[0]),r=d.s[0]}for(h=e._length,l=r.i[0].length,i.lastIndex=u,a=0;a<h;a+=1)for(o=0;o<l;o+=1)f=n?r.i[a][o]:r.i[a][o]+(s.i[a][o]-r.i[a][o])*p,e.i[a][o]=f,f=n?r.o[a][o]:r.o[a][o]+(s.o[a][o]-r.o[a][o])*p,e.o[a][o]=f,f=n?r.v[a][o]:r.v[a][o]+(s.v[a][o]-r.v[a][o])*p,e.v[a][o]=f}function i(){var e=this.comp.renderedFrame-this.offsetTime,i=this.keyframes[0].t-this.offsetTime,r=this.keyframes[this.keyframes.length-1].t-this.offsetTime,s=this._caching.lastFrame;return s!==t&&(s<i&&e<i||s>r&&e>r)||(this._caching.lastIndex=s<e?this._caching.lastIndex:0,this.interpolateShape(e,this.pv,this._caching)),this._caching.lastFrame=e,this.pv}function r(){this.paths=this.localShapeCollection}function s(t){(function(t,e){if(t._length!==e._length||t.c!==e.c)return!1;var i,r=t._length;for(i=0;i<r;i+=1)if(t.v[i][0]!==e.v[i][0]||t.v[i][1]!==e.v[i][1]||t.o[i][0]!==e.o[i][0]||t.o[i][1]!==e.o[i][1]||t.i[i][0]!==e.i[i][0]||t.i[i][1]!==e.i[i][1])return!1;return!0})(this.v,t)||(this.v=shapePool.clone(t),this.localShapeCollection.releaseShapes(),this.localShapeCollection.addShape(this.v),this._mdf=!0,this.paths=this.localShapeCollection)}function n(){if(this.elem.globalData.frameId!==this.frameId)if(this.effectsSequence.length)if(this.lock)this.setVValue(this.pv);else{this.lock=!0,this._mdf=!1,t=this.kf?this.pv:this.data.ks?this.data.ks.k:this.data.pt.k;var t,e,i=this.effectsSequence.length;for(e=0;e<i;e+=1)t=this.effectsSequence[e](t);this.setVValue(t),this.lock=!1,this.frameId=this.elem.globalData.frameId}else this._mdf=!1}function a(t,e,i){this.propType="shape",this.comp=t.comp,this.container=t,this.elem=t,this.data=e,this.k=!1,this.kf=!1,this._mdf=!1;var s=3===i?e.pt.k:e.ks.k;this.v=shapePool.clone(s),this.pv=shapePool.clone(this.v),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.paths=this.localShapeCollection,this.paths.addShape(this.v),this.reset=r,this.effectsSequence=[]}function o(t){this.effectsSequence.push(t),this.container.addDynamicProperty(this)}function h(e,s,n){this.propType="shape",this.comp=e.comp,this.elem=e,this.container=e,this.offsetTime=e.data.st,this.keyframes=3===n?s.pt.k:s.ks.k,this.keyframesMetadata=[],this.k=!0,this.kf=!0;var a=this.keyframes[0].s[0].i.length;this.v=shapePool.newElement(),this.v.setPathData(this.keyframes[0].s[0].c,a),this.pv=shapePool.clone(this.v),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.paths=this.localShapeCollection,this.paths.addShape(this.v),this.lastFrame=t,this.reset=r,this._caching={lastFrame:t,lastIndex:0},this.effectsSequence=[i.bind(this)]}a.prototype.interpolateShape=e,a.prototype.getValue=n,a.prototype.setVValue=s,a.prototype.addEffect=o,h.prototype.getValue=n,h.prototype.interpolateShape=e,h.prototype.setVValue=s,h.prototype.addEffect=o;var l=function(){var t=roundCorner;function e(t,e){this.v=shapePool.newElement(),this.v.setPathData(!0,4),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.paths=this.localShapeCollection,this.localShapeCollection.addShape(this.v),this.d=e.d,this.elem=t,this.comp=t.comp,this.frameId=-1,this.initDynamicPropertyContainer(t),this.p=PropertyFactory.getProp(t,e.p,1,0,this),this.s=PropertyFactory.getProp(t,e.s,1,0,this),this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertEllToPath())}return e.prototype={reset:r,getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertEllToPath())},convertEllToPath:function(){var e=this.p.v[0],i=this.p.v[1],r=this.s.v[0]/2,s=this.s.v[1]/2,n=3!==this.d,a=this.v;a.v[0][0]=e,a.v[0][1]=i-s,a.v[1][0]=n?e+r:e-r,a.v[1][1]=i,a.v[2][0]=e,a.v[2][1]=i+s,a.v[3][0]=n?e-r:e+r,a.v[3][1]=i,a.i[0][0]=n?e-r*t:e+r*t,a.i[0][1]=i-s,a.i[1][0]=n?e+r:e-r,a.i[1][1]=i-s*t,a.i[2][0]=n?e+r*t:e-r*t,a.i[2][1]=i+s,a.i[3][0]=n?e-r:e+r,a.i[3][1]=i+s*t,a.o[0][0]=n?e+r*t:e-r*t,a.o[0][1]=i-s,a.o[1][0]=n?e+r:e-r,a.o[1][1]=i+s*t,a.o[2][0]=n?e-r*t:e+r*t,a.o[2][1]=i+s,a.o[3][0]=n?e-r:e+r,a.o[3][1]=i-s*t}},extendPrototype([DynamicPropertyContainer],e),e}(),p=function(){function t(t,e){this.v=shapePool.newElement(),this.v.setPathData(!0,0),this.elem=t,this.comp=t.comp,this.data=e,this.frameId=-1,this.d=e.d,this.initDynamicPropertyContainer(t),1===e.sy?(this.ir=PropertyFactory.getProp(t,e.ir,0,0,this),this.is=PropertyFactory.getProp(t,e.is,0,.01,this),this.convertToPath=this.convertStarToPath):this.convertToPath=this.convertPolygonToPath,this.pt=PropertyFactory.getProp(t,e.pt,0,0,this),this.p=PropertyFactory.getProp(t,e.p,1,0,this),this.r=PropertyFactory.getProp(t,e.r,0,degToRads,this),this.or=PropertyFactory.getProp(t,e.or,0,0,this),this.os=PropertyFactory.getProp(t,e.os,0,.01,this),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.localShapeCollection.addShape(this.v),this.paths=this.localShapeCollection,this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertToPath())}return t.prototype={reset:r,getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertToPath())},convertStarToPath:function(){var t,e,i,r,s=2*Math.floor(this.pt.v),n=2*Math.PI/s,a=!0,o=this.or.v,h=this.ir.v,l=this.os.v,p=this.is.v,f=2*Math.PI*o/(2*s),u=2*Math.PI*h/(2*s),c=-Math.PI/2;c+=this.r.v;var m=3===this.data.d?-1:1;for(this.v._length=0,t=0;t<s;t+=1){i=a?l:p,r=a?f:u;var d=(e=a?o:h)*Math.cos(c),g=e*Math.sin(c),v=0===d&&0===g?0:g/Math.sqrt(d*d+g*g),y=0===d&&0===g?0:-d/Math.sqrt(d*d+g*g);d+=+this.p.v[0],g+=+this.p.v[1],this.v.setTripleAt(d,g,d-v*r*i*m,g-y*r*i*m,d+v*r*i*m,g+y*r*i*m,t,!0),a=!a,c+=n*m}},convertPolygonToPath:function(){var t,e=Math.floor(this.pt.v),i=2*Math.PI/e,r=this.or.v,s=this.os.v,n=2*Math.PI*r/(4*e),a=-(.5*Math.PI),o=3===this.data.d?-1:1;for(a+=this.r.v,this.v._length=0,t=0;t<e;t+=1){var h=r*Math.cos(a),l=r*Math.sin(a),p=0===h&&0===l?0:l/Math.sqrt(h*h+l*l),f=0===h&&0===l?0:-h/Math.sqrt(h*h+l*l);h+=+this.p.v[0],l+=+this.p.v[1],this.v.setTripleAt(h,l,h-p*n*s*o,l-f*n*s*o,h+p*n*s*o,l+f*n*s*o,t,!0),a+=i*o}this.paths.length=0,this.paths[0]=this.v}},extendPrototype([DynamicPropertyContainer],t),t}(),f=function(){function t(t,e){this.v=shapePool.newElement(),this.v.c=!0,this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.localShapeCollection.addShape(this.v),this.paths=this.localShapeCollection,this.elem=t,this.comp=t.comp,this.frameId=-1,this.d=e.d,this.initDynamicPropertyContainer(t),this.p=PropertyFactory.getProp(t,e.p,1,0,this),this.s=PropertyFactory.getProp(t,e.s,1,0,this),this.r=PropertyFactory.getProp(t,e.r,0,0,this),this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertRectToPath())}return t.prototype={convertRectToPath:function(){var t=this.p.v[0],e=this.p.v[1],i=this.s.v[0]/2,r=this.s.v[1]/2,s=bmMin(i,r,this.r.v),n=s*(1-roundCorner);this.v._length=0,2===this.d||1===this.d?(this.v.setTripleAt(t+i,e-r+s,t+i,e-r+s,t+i,e-r+n,0,!0),this.v.setTripleAt(t+i,e+r-s,t+i,e+r-n,t+i,e+r-s,1,!0),0!==s?(this.v.setTripleAt(t+i-s,e+r,t+i-s,e+r,t+i-n,e+r,2,!0),this.v.setTripleAt(t-i+s,e+r,t-i+n,e+r,t-i+s,e+r,3,!0),this.v.setTripleAt(t-i,e+r-s,t-i,e+r-s,t-i,e+r-n,4,!0),this.v.setTripleAt(t-i,e-r+s,t-i,e-r+n,t-i,e-r+s,5,!0),this.v.setTripleAt(t-i+s,e-r,t-i+s,e-r,t-i+n,e-r,6,!0),this.v.setTripleAt(t+i-s,e-r,t+i-n,e-r,t+i-s,e-r,7,!0)):(this.v.setTripleAt(t-i,e+r,t-i+n,e+r,t-i,e+r,2),this.v.setTripleAt(t-i,e-r,t-i,e-r+n,t-i,e-r,3))):(this.v.setTripleAt(t+i,e-r+s,t+i,e-r+n,t+i,e-r+s,0,!0),0!==s?(this.v.setTripleAt(t+i-s,e-r,t+i-s,e-r,t+i-n,e-r,1,!0),this.v.setTripleAt(t-i+s,e-r,t-i+n,e-r,t-i+s,e-r,2,!0),this.v.setTripleAt(t-i,e-r+s,t-i,e-r+s,t-i,e-r+n,3,!0),this.v.setTripleAt(t-i,e+r-s,t-i,e+r-n,t-i,e+r-s,4,!0),this.v.setTripleAt(t-i+s,e+r,t-i+s,e+r,t-i+n,e+r,5,!0),this.v.setTripleAt(t+i-s,e+r,t+i-n,e+r,t+i-s,e+r,6,!0),this.v.setTripleAt(t+i,e+r-s,t+i,e+r-s,t+i,e+r-n,7,!0)):(this.v.setTripleAt(t-i,e-r,t-i+n,e-r,t-i,e-r,1,!0),this.v.setTripleAt(t-i,e+r,t-i,e+r-n,t-i,e+r,2,!0),this.v.setTripleAt(t+i,e+r,t+i-n,e+r,t+i,e+r,3,!0)))},getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertRectToPath())},reset:r},extendPrototype([DynamicPropertyContainer],t),t}();return{getShapeProp:function(t,e,i){var r;return 3===i||4===i?r=(3===i?e.pt:e.ks).k.length?new h(t,e,i):new a(t,e,i):5===i?r=new f(t,e):6===i?r=new l(t,e):7===i&&(r=new p(t,e)),r.k&&t.addDynamicProperty(r),r},getConstructorFunction:function(){return a},getKeyframedConstructorFunction:function(){return h}}}(),Matrix=function(){var t=Math.cos,e=Math.sin,i=Math.tan,r=Math.round;function s(){return this.props[0]=1,this.props[1]=0,this.props[2]=0,this.props[3]=0,this.props[4]=0,this.props[5]=1,this.props[6]=0,this.props[7]=0,this.props[8]=0,this.props[9]=0,this.props[10]=1,this.props[11]=0,this.props[12]=0,this.props[13]=0,this.props[14]=0,this.props[15]=1,this}function n(i){if(0===i)return this;var r=t(i),s=e(i);return this._t(r,-s,0,0,s,r,0,0,0,0,1,0,0,0,0,1)}function a(i){if(0===i)return this;var r=t(i),s=e(i);return this._t(1,0,0,0,0,r,-s,0,0,s,r,0,0,0,0,1)}function o(i){if(0===i)return this;var r=t(i),s=e(i);return this._t(r,0,s,0,0,1,0,0,-s,0,r,0,0,0,0,1)}function h(i){if(0===i)return this;var r=t(i),s=e(i);return this._t(r,-s,0,0,s,r,0,0,0,0,1,0,0,0,0,1)}function l(t,e){return this._t(1,e,t,1,0,0)}function p(t,e){return this.shear(i(t),i(e))}function f(r,s){var n=t(s),a=e(s);return this._t(n,a,0,0,-a,n,0,0,0,0,1,0,0,0,0,1)._t(1,0,0,0,i(r),1,0,0,0,0,1,0,0,0,0,1)._t(n,-a,0,0,a,n,0,0,0,0,1,0,0,0,0,1)}function u(t,e,i){return i||0===i||(i=1),1===t&&1===e&&1===i?this:this._t(t,0,0,0,0,e,0,0,0,0,i,0,0,0,0,1)}function c(t,e,i,r,s,n,a,o,h,l,p,f,u,c,m,d){return this.props[0]=t,this.props[1]=e,this.props[2]=i,this.props[3]=r,this.props[4]=s,this.props[5]=n,this.props[6]=a,this.props[7]=o,this.props[8]=h,this.props[9]=l,this.props[10]=p,this.props[11]=f,this.props[12]=u,this.props[13]=c,this.props[14]=m,this.props[15]=d,this}function m(t,e,i){return i=i||0,0!==t||0!==e||0!==i?this._t(1,0,0,0,0,1,0,0,0,0,1,0,t,e,i,1):this}function d(t,e,i,r,s,n,a,o,h,l,p,f,u,c,m,d){var g=this.props;if(1===t&&0===e&&0===i&&0===r&&0===s&&1===n&&0===a&&0===o&&0===h&&0===l&&1===p&&0===f)return g[12]=g[12]*t+g[15]*u,g[13]=g[13]*n+g[15]*c,g[14]=g[14]*p+g[15]*m,g[15]*=d,this._identityCalculated=!1,this;var v=g[0],y=g[1],b=g[2],x=g[3],_=g[4],A=g[5],k=g[6],w=g[7],P=g[8],C=g[9],S=g[10],D=g[11],T=g[12],E=g[13],M=g[14],F=g[15];return g[0]=v*t+y*s+b*h+x*u,g[1]=v*e+y*n+b*l+x*c,g[2]=v*i+y*a+b*p+x*m,g[3]=v*r+y*o+b*f+x*d,g[4]=_*t+A*s+k*h+w*u,g[5]=_*e+A*n+k*l+w*c,g[6]=_*i+A*a+k*p+w*m,g[7]=_*r+A*o+k*f+w*d,g[8]=P*t+C*s+S*h+D*u,g[9]=P*e+C*n+S*l+D*c,g[10]=P*i+C*a+S*p+D*m,g[11]=P*r+C*o+S*f+D*d,g[12]=T*t+E*s+M*h+F*u,g[13]=T*e+E*n+M*l+F*c,g[14]=T*i+E*a+M*p+F*m,g[15]=T*r+E*o+M*f+F*d,this._identityCalculated=!1,this}function g(t){var e=t.props;return this.transform(e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15])}function v(){return this._identityCalculated||(this._identity=1===this.props[0]&&0===this.props[1]&&0===this.props[2]&&0===this.props[3]&&0===this.props[4]&&1===this.props[5]&&0===this.props[6]&&0===this.props[7]&&0===this.props[8]&&0===this.props[9]&&1===this.props[10]&&0===this.props[11]&&0===this.props[12]&&0===this.props[13]&&0===this.props[14]&&1===this.props[15],this._identityCalculated=!0),this._identity}function y(t){for(var e=0;e<16;){if(t.props[e]!==this.props[e])return!1;e+=1}return!0}function b(t){var e;for(e=0;e<16;e+=1)t.props[e]=this.props[e];return t}function x(t){var e;for(e=0;e<16;e+=1)this.props[e]=t[e]}function _(t,e,i){return{x:t*this.props[0]+e*this.props[4]+i*this.props[8]+this.props[12],y:t*this.props[1]+e*this.props[5]+i*this.props[9]+this.props[13],z:t*this.props[2]+e*this.props[6]+i*this.props[10]+this.props[14]}}function A(t,e,i){return t*this.props[0]+e*this.props[4]+i*this.props[8]+this.props[12]}function k(t,e,i){return t*this.props[1]+e*this.props[5]+i*this.props[9]+this.props[13]}function w(t,e,i){return t*this.props[2]+e*this.props[6]+i*this.props[10]+this.props[14]}function P(){var t=this.props[0]*this.props[5]-this.props[1]*this.props[4],e=this.props[5]/t,i=-this.props[1]/t,r=-this.props[4]/t,s=this.props[0]/t,n=(this.props[4]*this.props[13]-this.props[5]*this.props[12])/t,a=-(this.props[0]*this.props[13]-this.props[1]*this.props[12])/t,o=new Matrix;return o.props[0]=e,o.props[1]=i,o.props[4]=r,o.props[5]=s,o.props[12]=n,o.props[13]=a,o}function C(t){return this.getInverseMatrix().applyToPointArray(t[0],t[1],t[2]||0)}function S(t){var e,i=t.length,r=[];for(e=0;e<i;e+=1)r[e]=C(t[e]);return r}function D(t,e,i){var r=createTypedArray("float32",6);if(this.isIdentity())r[0]=t[0],r[1]=t[1],r[2]=e[0],r[3]=e[1],r[4]=i[0],r[5]=i[1];else{var s=this.props[0],n=this.props[1],a=this.props[4],o=this.props[5],h=this.props[12],l=this.props[13];r[0]=t[0]*s+t[1]*a+h,r[1]=t[0]*n+t[1]*o+l,r[2]=e[0]*s+e[1]*a+h,r[3]=e[0]*n+e[1]*o+l,r[4]=i[0]*s+i[1]*a+h,r[5]=i[0]*n+i[1]*o+l}return r}function T(t,e,i){return this.isIdentity()?[t,e,i]:[t*this.props[0]+e*this.props[4]+i*this.props[8]+this.props[12],t*this.props[1]+e*this.props[5]+i*this.props[9]+this.props[13],t*this.props[2]+e*this.props[6]+i*this.props[10]+this.props[14]]}function E(t,e){if(this.isIdentity())return t+","+e;var i=this.props;return Math.round(100*(t*i[0]+e*i[4]+i[12]))/100+","+Math.round(100*(t*i[1]+e*i[5]+i[13]))/100}function M(){for(var t=0,e=this.props,i="matrix3d(";t<16;)i+=r(1e4*e[t])/1e4,i+=15===t?")":",",t+=1;return i}function F(t){return t<1e-6&&t>0||t>-1e-6&&t<0?r(1e4*t)/1e4:t}function I(){var t=this.props;return"matrix("+F(t[0])+","+F(t[1])+","+F(t[4])+","+F(t[5])+","+F(t[12])+","+F(t[13])+")"}return function(){this.reset=s,this.rotate=n,this.rotateX=a,this.rotateY=o,this.rotateZ=h,this.skew=p,this.skewFromAxis=f,this.shear=l,this.scale=u,this.setTransform=c,this.translate=m,this.transform=d,this.multiply=g,this.applyToPoint=_,this.applyToX=A,this.applyToY=k,this.applyToZ=w,this.applyToPointArray=T,this.applyToTriplePoints=D,this.applyToPointStringified=E,this.toCSS=M,this.to2dCSS=I,this.clone=b,this.cloneFromProps=x,this.equals=y,this.inversePoints=S,this.inversePoint=C,this.getInverseMatrix=P,this._t=this.transform,this.isIdentity=v,this._identity=!0,this._identityCalculated=!1,this.props=createTypedArray("float32",16),this.reset()}}();function _typeof$3(t){return(_typeof$3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var lottie={},standalone="__[STANDALONE]__",animationData="__[ANIMATIONDATA]__",renderer="";function setLocation(t){setLocationHref(t)}function searchAnimations(){!0===standalone?animationManager.searchAnimations(animationData,standalone,renderer):animationManager.searchAnimations()}function setSubframeRendering(t){setSubframeEnabled(t)}function setPrefix(t){setIdPrefix(t)}function loadAnimation(t){return!0===standalone&&(t.animationData=JSON.parse(animationData)),animationManager.loadAnimation(t)}function setQuality(t){if("string"==typeof t)switch(t){case"high":setDefaultCurveSegments(200);break;default:case"medium":setDefaultCurveSegments(50);break;case"low":setDefaultCurveSegments(10)}else!isNaN(t)&&t>1&&setDefaultCurveSegments(t);getDefaultCurveSegments()>=50?roundValues(!1):roundValues(!0)}function inBrowser(){return"undefined"!=typeof navigator}function installPlugin(t,e){"expressions"===t&&setExpressionsPlugin(e)}function getFactory(t){switch(t){case"propertyFactory":return PropertyFactory;case"shapePropertyFactory":return ShapePropertyFactory;case"matrix":return Matrix;default:return null}}function checkReady(){"complete"===document.readyState&&(clearInterval(readyStateCheckInterval),searchAnimations())}function getQueryVariable(t){for(var e=queryString.split("&"),i=0;i<e.length;i+=1){var r=e[i].split("=");if(decodeURIComponent(r[0])==t)return decodeURIComponent(r[1])}return null}lottie.play=animationManager.play,lottie.pause=animationManager.pause,lottie.setLocationHref=setLocation,lottie.togglePause=animationManager.togglePause,lottie.setSpeed=animationManager.setSpeed,lottie.setDirection=animationManager.setDirection,lottie.stop=animationManager.stop,lottie.searchAnimations=searchAnimations,lottie.registerAnimation=animationManager.registerAnimation,lottie.loadAnimation=loadAnimation,lottie.setSubframeRendering=setSubframeRendering,lottie.resize=animationManager.resize,lottie.goToAndStop=animationManager.goToAndStop,lottie.destroy=animationManager.destroy,lottie.setQuality=setQuality,lottie.inBrowser=inBrowser,lottie.installPlugin=installPlugin,lottie.freeze=animationManager.freeze,lottie.unfreeze=animationManager.unfreeze,lottie.setVolume=animationManager.setVolume,lottie.mute=animationManager.mute,lottie.unmute=animationManager.unmute,lottie.getRegisteredAnimations=animationManager.getRegisteredAnimations,lottie.useWebWorker=setWebWorker,lottie.setIDPrefix=setPrefix,lottie.__getFactory=getFactory,lottie.version="5.12.2";var queryString="";if(standalone){var scripts=document.getElementsByTagName("script"),index=scripts.length-1,myScript=scripts[index]||{src:""};queryString=myScript.src?myScript.src.replace(/^[^\?]+\??/,""):"",renderer=getQueryVariable("renderer")}var readyStateCheckInterval=setInterval(checkReady,100);try{"object"===_typeof$3(exports)||"function"==typeof define&&define.amd||(window.bodymovin=lottie)}catch(t){}var ShapeModifiers=function(){var t={},e={};return t.registerModifier=function(t,i){e[t]||(e[t]=i)},t.getModifier=function(t,i,r){return new e[t](i,r)},t}();function ShapeModifier(){}function TrimModifier(){}function PuckerAndBloatModifier(){}ShapeModifier.prototype.initModifierProperties=function(){},ShapeModifier.prototype.addShapeToModifier=function(){},ShapeModifier.prototype.addShape=function(t){if(!this.closed){t.sh.container.addDynamicProperty(t.sh);var e={shape:t.sh,data:t,localShapeCollection:shapeCollectionPool.newShapeCollection()};this.shapes.push(e),this.addShapeToModifier(e),this._isAnimated&&t.setAsAnimated()}},ShapeModifier.prototype.init=function(t,e){this.shapes=[],this.elem=t,this.initDynamicPropertyContainer(t),this.initModifierProperties(t,e),this.frameId=initialDefaultFrame,this.closed=!1,this.k=!1,this.dynamicProperties.length?this.k=!0:this.getValue(!0)},ShapeModifier.prototype.processKeys=function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties())},extendPrototype([DynamicPropertyContainer],ShapeModifier),extendPrototype([ShapeModifier],TrimModifier),TrimModifier.prototype.initModifierProperties=function(t,e){this.s=PropertyFactory.getProp(t,e.s,0,.01,this),this.e=PropertyFactory.getProp(t,e.e,0,.01,this),this.o=PropertyFactory.getProp(t,e.o,0,0,this),this.sValue=0,this.eValue=0,this.getValue=this.processKeys,this.m=e.m,this._isAnimated=!!this.s.effectsSequence.length||!!this.e.effectsSequence.length||!!this.o.effectsSequence.length},TrimModifier.prototype.addShapeToModifier=function(t){t.pathsData=[]},TrimModifier.prototype.calculateShapeEdges=function(t,e,i,r,s){var n,a=[];e<=1?a.push({s:t,e:e}):t>=1?a.push({s:t-1,e:e-1}):(a.push({s:t,e:1}),a.push({s:0,e:e-1}));var o,h,l=[],p=a.length;for(o=0;o<p;o+=1)(h=a[o]).e*s<r||h.s*s>r+i||(n=h.s*s<=r?0:(h.s*s-r)/i,l.push([n,h.e*s>=r+i?1:(h.e*s-r)/i]));return l.length||l.push([0,0]),l},TrimModifier.prototype.releasePathsData=function(t){var e,i=t.length;for(e=0;e<i;e+=1)segmentsLengthPool.release(t[e]);return t.length=0,t},TrimModifier.prototype.processShapes=function(t){if(this._mdf||t){var e=this.o.v%360/360;if(e<0&&(e+=1),(r=this.s.v>1?1+e:this.s.v<0?0+e:this.s.v+e)>(s=this.e.v>1?1+e:this.e.v<0?0+e:this.e.v+e)){var i=r;r=s,s=i}r=1e-4*Math.round(1e4*r),s=1e-4*Math.round(1e4*s),this.sValue=r,this.eValue=s}else r=this.sValue,s=this.eValue;var r,s,n,a,o,h,l,p,f,u=this.shapes.length,c=0;if(s===r)for(a=0;a<u;a+=1)this.shapes[a].localShapeCollection.releaseShapes(),this.shapes[a].shape._mdf=!0,this.shapes[a].shape.paths=this.shapes[a].localShapeCollection,this._mdf&&(this.shapes[a].pathsData.length=0);else if(1===s&&0===r||0===s&&1===r){if(this._mdf)for(a=0;a<u;a+=1)this.shapes[a].pathsData.length=0,this.shapes[a].shape._mdf=!0}else{var m,d,g=[];for(a=0;a<u;a+=1)if((m=this.shapes[a]).shape._mdf||this._mdf||t||2===this.m){if(h=(n=m.shape.paths)._length,f=0,!m.shape._mdf&&m.pathsData.length)f=m.totalShapeLength;else{for(l=this.releasePathsData(m.pathsData),o=0;o<h;o+=1)p=bez.getSegmentsLength(n.shapes[o]),l.push(p),f+=p.totalLength;m.totalShapeLength=f,m.pathsData=l}c+=f,m.shape._mdf=!0}else m.shape.paths=m.localShapeCollection;var v,y=r,b=s,x=0;for(a=u-1;a>=0;a-=1)if((m=this.shapes[a]).shape._mdf){for((d=m.localShapeCollection).releaseShapes(),2===this.m&&u>1?(v=this.calculateShapeEdges(r,s,m.totalShapeLength,x,c),x+=m.totalShapeLength):v=[[y,b]],h=v.length,o=0;o<h;o+=1){y=v[o][0],b=v[o][1],g.length=0,b<=1?g.push({s:m.totalShapeLength*y,e:m.totalShapeLength*b}):y>=1?g.push({s:m.totalShapeLength*(y-1),e:m.totalShapeLength*(b-1)}):(g.push({s:m.totalShapeLength*y,e:m.totalShapeLength}),g.push({s:0,e:m.totalShapeLength*(b-1)}));var _=this.addShapes(m,g[0]);if(g[0].s!==g[0].e){if(g.length>1)if(m.shape.paths.shapes[m.shape.paths._length-1].c){var A=_.pop();this.addPaths(_,d),_=this.addShapes(m,g[1],A)}else this.addPaths(_,d),_=this.addShapes(m,g[1]);this.addPaths(_,d)}}m.shape.paths=d}}},TrimModifier.prototype.addPaths=function(t,e){var i,r=t.length;for(i=0;i<r;i+=1)e.addShape(t[i])},TrimModifier.prototype.addSegment=function(t,e,i,r,s,n,a){s.setXYAt(e[0],e[1],"o",n),s.setXYAt(i[0],i[1],"i",n+1),a&&s.setXYAt(t[0],t[1],"v",n),s.setXYAt(r[0],r[1],"v",n+1)},TrimModifier.prototype.addSegmentFromArray=function(t,e,i,r){e.setXYAt(t[1],t[5],"o",i),e.setXYAt(t[2],t[6],"i",i+1),r&&e.setXYAt(t[0],t[4],"v",i),e.setXYAt(t[3],t[7],"v",i+1)},TrimModifier.prototype.addShapes=function(t,e,i){var r,s,n,a,o,h,l,p,f=t.pathsData,u=t.shape.paths.shapes,c=t.shape.paths._length,m=0,d=[],g=!0;for(i?(o=i._length,p=i._length):(i=shapePool.newElement(),o=0,p=0),d.push(i),r=0;r<c;r+=1){for(h=f[r].lengths,i.c=u[r].c,n=u[r].c?h.length:h.length+1,s=1;s<n;s+=1)if(m+(a=h[s-1]).addedLength<e.s)m+=a.addedLength,i.c=!1;else{if(m>e.e){i.c=!1;break}e.s<=m&&e.e>=m+a.addedLength?(this.addSegment(u[r].v[s-1],u[r].o[s-1],u[r].i[s],u[r].v[s],i,o,g),g=!1):(l=bez.getNewSegment(u[r].v[s-1],u[r].v[s],u[r].o[s-1],u[r].i[s],(e.s-m)/a.addedLength,(e.e-m)/a.addedLength,h[s-1]),this.addSegmentFromArray(l,i,o,g),g=!1,i.c=!1),m+=a.addedLength,o+=1}if(u[r].c&&h.length){if(a=h[s-1],m<=e.e){var v=h[s-1].addedLength;e.s<=m&&e.e>=m+v?(this.addSegment(u[r].v[s-1],u[r].o[s-1],u[r].i[0],u[r].v[0],i,o,g),g=!1):(l=bez.getNewSegment(u[r].v[s-1],u[r].v[0],u[r].o[s-1],u[r].i[0],(e.s-m)/v,(e.e-m)/v,h[s-1]),this.addSegmentFromArray(l,i,o,g),g=!1,i.c=!1)}else i.c=!1;m+=a.addedLength,o+=1}if(i._length&&(i.setXYAt(i.v[p][0],i.v[p][1],"i",p),i.setXYAt(i.v[i._length-1][0],i.v[i._length-1][1],"o",i._length-1)),m>e.e)break;r<c-1&&(i=shapePool.newElement(),g=!0,d.push(i),o=0)}return d},extendPrototype([ShapeModifier],PuckerAndBloatModifier),PuckerAndBloatModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.amount=PropertyFactory.getProp(t,e.a,0,null,this),this._isAnimated=!!this.amount.effectsSequence.length},PuckerAndBloatModifier.prototype.processPath=function(t,e){var i=e/100,r=[0,0],s=t._length,n=0;for(n=0;n<s;n+=1)r[0]+=t.v[n][0],r[1]+=t.v[n][1];r[0]/=s,r[1]/=s;var a,o,h,l,p,f,u=shapePool.newElement();for(u.c=t.c,n=0;n<s;n+=1)a=t.v[n][0]+(r[0]-t.v[n][0])*i,o=t.v[n][1]+(r[1]-t.v[n][1])*i,h=t.o[n][0]+-((r[0]-t.o[n][0])*i),l=t.o[n][1]+-((r[1]-t.o[n][1])*i),p=t.i[n][0]+-((r[0]-t.i[n][0])*i),f=t.i[n][1]+-((r[1]-t.i[n][1])*i),u.setTripleAt(a,o,h,l,p,f,n);return u},PuckerAndBloatModifier.prototype.processShapes=function(t){var e,i,r,s,n,a,o=this.shapes.length,h=this.amount.v;if(0!==h)for(i=0;i<o;i+=1){if(a=(n=this.shapes[i]).localShapeCollection,n.shape._mdf||this._mdf||t)for(a.releaseShapes(),n.shape._mdf=!0,e=n.shape.paths.shapes,s=n.shape.paths._length,r=0;r<s;r+=1)a.addShape(this.processPath(e[r],h));n.shape.paths=n.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)};var TransformPropertyFactory=function(){var t=[0,0];function e(t,e,i){if(this.elem=t,this.frameId=-1,this.propType="transform",this.data=e,this.v=new Matrix,this.pre=new Matrix,this.appliedTransformations=0,this.initDynamicPropertyContainer(i||t),e.p&&e.p.s?(this.px=PropertyFactory.getProp(t,e.p.x,0,0,this),this.py=PropertyFactory.getProp(t,e.p.y,0,0,this),e.p.z&&(this.pz=PropertyFactory.getProp(t,e.p.z,0,0,this))):this.p=PropertyFactory.getProp(t,e.p||{k:[0,0,0]},1,0,this),e.rx){if(this.rx=PropertyFactory.getProp(t,e.rx,0,degToRads,this),this.ry=PropertyFactory.getProp(t,e.ry,0,degToRads,this),this.rz=PropertyFactory.getProp(t,e.rz,0,degToRads,this),e.or.k[0].ti){var r,s=e.or.k.length;for(r=0;r<s;r+=1)e.or.k[r].to=null,e.or.k[r].ti=null}this.or=PropertyFactory.getProp(t,e.or,1,degToRads,this),this.or.sh=!0}else this.r=PropertyFactory.getProp(t,e.r||{k:0},0,degToRads,this);e.sk&&(this.sk=PropertyFactory.getProp(t,e.sk,0,degToRads,this),this.sa=PropertyFactory.getProp(t,e.sa,0,degToRads,this)),this.a=PropertyFactory.getProp(t,e.a||{k:[0,0,0]},1,0,this),this.s=PropertyFactory.getProp(t,e.s||{k:[100,100,100]},1,.01,this),e.o?this.o=PropertyFactory.getProp(t,e.o,0,.01,t):this.o={_mdf:!1,v:1},this._isDirty=!0,this.dynamicProperties.length||this.getValue(!0)}return e.prototype={applyToMatrix:function(t){var e=this._mdf;this.iterateDynamicProperties(),this._mdf=this._mdf||e,this.a&&t.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.s&&t.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.sk&&t.skewFromAxis(-this.sk.v,this.sa.v),this.r?t.rotate(-this.r.v):t.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.data.p.s?this.data.p.z?t.translate(this.px.v,this.py.v,-this.pz.v):t.translate(this.px.v,this.py.v,0):t.translate(this.p.v[0],this.p.v[1],-this.p.v[2])},getValue:function(e){if(this.elem.globalData.frameId!==this.frameId){if(this._isDirty&&(this.precalculateMatrix(),this._isDirty=!1),this.iterateDynamicProperties(),this._mdf||e){if(this.v.cloneFromProps(this.pre.props),this.appliedTransformations<1&&this.v.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.appliedTransformations<2&&this.v.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.sk&&this.appliedTransformations<3&&this.v.skewFromAxis(-this.sk.v,this.sa.v),this.r&&this.appliedTransformations<4?this.v.rotate(-this.r.v):!this.r&&this.appliedTransformations<4&&this.v.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.autoOriented){if(i=this.elem.globalData.frameRate,this.p&&this.p.keyframes&&this.p.getValueAtTime)this.p._caching.lastFrame+this.p.offsetTime<=this.p.keyframes[0].t?(r=this.p.getValueAtTime((this.p.keyframes[0].t+.01)/i,0),s=this.p.getValueAtTime(this.p.keyframes[0].t/i,0)):this.p._caching.lastFrame+this.p.offsetTime>=this.p.keyframes[this.p.keyframes.length-1].t?(r=this.p.getValueAtTime(this.p.keyframes[this.p.keyframes.length-1].t/i,0),s=this.p.getValueAtTime((this.p.keyframes[this.p.keyframes.length-1].t-.05)/i,0)):(r=this.p.pv,s=this.p.getValueAtTime((this.p._caching.lastFrame+this.p.offsetTime-.01)/i,this.p.offsetTime));else if(this.px&&this.px.keyframes&&this.py.keyframes&&this.px.getValueAtTime&&this.py.getValueAtTime){r=[],s=[];var i,r,s,n=this.px,a=this.py;n._caching.lastFrame+n.offsetTime<=n.keyframes[0].t?(r[0]=n.getValueAtTime((n.keyframes[0].t+.01)/i,0),r[1]=a.getValueAtTime((a.keyframes[0].t+.01)/i,0),s[0]=n.getValueAtTime(n.keyframes[0].t/i,0),s[1]=a.getValueAtTime(a.keyframes[0].t/i,0)):n._caching.lastFrame+n.offsetTime>=n.keyframes[n.keyframes.length-1].t?(r[0]=n.getValueAtTime(n.keyframes[n.keyframes.length-1].t/i,0),r[1]=a.getValueAtTime(a.keyframes[a.keyframes.length-1].t/i,0),s[0]=n.getValueAtTime((n.keyframes[n.keyframes.length-1].t-.01)/i,0),s[1]=a.getValueAtTime((a.keyframes[a.keyframes.length-1].t-.01)/i,0)):(r=[n.pv,a.pv],s[0]=n.getValueAtTime((n._caching.lastFrame+n.offsetTime-.01)/i,n.offsetTime),s[1]=a.getValueAtTime((a._caching.lastFrame+a.offsetTime-.01)/i,a.offsetTime))}else r=s=t;this.v.rotate(-Math.atan2(r[1]-s[1],r[0]-s[0]))}this.data.p&&this.data.p.s?this.data.p.z?this.v.translate(this.px.v,this.py.v,-this.pz.v):this.v.translate(this.px.v,this.py.v,0):this.v.translate(this.p.v[0],this.p.v[1],-this.p.v[2])}this.frameId=this.elem.globalData.frameId}},precalculateMatrix:function(){if(this.appliedTransformations=0,this.pre.reset(),!this.a.effectsSequence.length&&(this.pre.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.appliedTransformations=1,!this.s.effectsSequence.length)){if(this.pre.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.appliedTransformations=2,this.sk){if(this.sk.effectsSequence.length||this.sa.effectsSequence.length)return;this.pre.skewFromAxis(-this.sk.v,this.sa.v),this.appliedTransformations=3}this.r?this.r.effectsSequence.length||(this.pre.rotate(-this.r.v),this.appliedTransformations=4):this.rz.effectsSequence.length||this.ry.effectsSequence.length||this.rx.effectsSequence.length||this.or.effectsSequence.length||(this.pre.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.appliedTransformations=4)}},autoOrient:function(){}},extendPrototype([DynamicPropertyContainer],e),e.prototype.addDynamicProperty=function(t){this._addDynamicProperty(t),this.elem.addDynamicProperty(t),this._isDirty=!0},e.prototype._addDynamicProperty=DynamicPropertyContainer.prototype.addDynamicProperty,{getTransformProperty:function(t,i,r){return new e(t,i,r)}}}();function RepeaterModifier(){}function RoundCornersModifier(){}function floatEqual(t,e){return 1e5*Math.abs(t-e)<=Math.min(Math.abs(t),Math.abs(e))}function floatZero(t){return 1e-5>=Math.abs(t)}function lerp(t,e,i){return t*(1-i)+e*i}function lerpPoint(t,e,i){return[lerp(t[0],e[0],i),lerp(t[1],e[1],i)]}function quadRoots(t,e,i){if(0===t)return[];var r=e*e-4*t*i;if(r<0)return[];var s=-e/(2*t);if(0===r)return[s];var n=Math.sqrt(r)/(2*t);return[s-n,s+n]}function polynomialCoefficients(t,e,i,r){return[3*e-t-3*i+r,3*t-6*e+3*i,-3*t+3*e,t]}function singlePoint(t){return new PolynomialBezier(t,t,t,t,!1)}function PolynomialBezier(t,e,i,r,s){s&&pointEqual(t,e)&&(e=lerpPoint(t,r,1/3)),s&&pointEqual(i,r)&&(i=lerpPoint(t,r,2/3));var n=polynomialCoefficients(t[0],e[0],i[0],r[0]),a=polynomialCoefficients(t[1],e[1],i[1],r[1]);this.a=[n[0],a[0]],this.b=[n[1],a[1]],this.c=[n[2],a[2]],this.d=[n[3],a[3]],this.points=[t,e,i,r]}function extrema(t,e){var i=t.points[0][e],r=t.points[t.points.length-1][e];if(i>r){var s=r;r=i,i=s}for(var n=quadRoots(3*t.a[e],2*t.b[e],t.c[e]),a=0;a<n.length;a+=1)if(n[a]>0&&n[a]<1){var o=t.point(n[a])[e];o<i?i=o:o>r&&(r=o)}return{min:i,max:r}}function intersectData(t,e,i){var r=t.boundingBox();return{cx:r.cx,cy:r.cy,width:r.width,height:r.height,bez:t,t:(e+i)/2,t1:e,t2:i}}function splitData(t){var e=t.bez.split(.5);return[intersectData(e[0],t.t1,t.t),intersectData(e[1],t.t,t.t2)]}function boxIntersect(t,e){return 2*Math.abs(t.cx-e.cx)<t.width+e.width&&2*Math.abs(t.cy-e.cy)<t.height+e.height}function intersectsImpl(t,e,i,r,s,n){if(boxIntersect(t,e))if(i>=n||t.width<=r&&t.height<=r&&e.width<=r&&e.height<=r)s.push([t.t,e.t]);else{var a=splitData(t),o=splitData(e);intersectsImpl(a[0],o[0],i+1,r,s,n),intersectsImpl(a[0],o[1],i+1,r,s,n),intersectsImpl(a[1],o[0],i+1,r,s,n),intersectsImpl(a[1],o[1],i+1,r,s,n)}}function crossProduct(t,e){return[t[1]*e[2]-t[2]*e[1],t[2]*e[0]-t[0]*e[2],t[0]*e[1]-t[1]*e[0]]}function lineIntersection(t,e,i,r){var s=[t[0],t[1],1],n=[e[0],e[1],1],a=[i[0],i[1],1],o=[r[0],r[1],1],h=crossProduct(crossProduct(s,n),crossProduct(a,o));return floatZero(h[2])?null:[h[0]/h[2],h[1]/h[2]]}function polarOffset(t,e,i){return[t[0]+Math.cos(e)*i,t[1]-Math.sin(e)*i]}function pointDistance(t,e){return Math.hypot(t[0]-e[0],t[1]-e[1])}function pointEqual(t,e){return floatEqual(t[0],e[0])&&floatEqual(t[1],e[1])}function ZigZagModifier(){}function setPoint(t,e,i,r,s,n,a){var o=i-Math.PI/2,h=i+Math.PI/2,l=e[0]+Math.cos(i)*r*s,p=e[1]-Math.sin(i)*r*s;t.setTripleAt(l,p,l+Math.cos(o)*n,p-Math.sin(o)*n,l+Math.cos(h)*a,p-Math.sin(h)*a,t.length())}function getPerpendicularVector(t,e){var i=[e[0]-t[0],e[1]-t[1]],r=-(.5*Math.PI);return[Math.cos(r)*i[0]-Math.sin(r)*i[1],Math.sin(r)*i[0]+Math.cos(r)*i[1]]}function getProjectingAngle(t,e){var i=0===e?t.length()-1:e-1,r=(e+1)%t.length(),s=getPerpendicularVector(t.v[i],t.v[r]);return Math.atan2(0,1)-Math.atan2(s[1],s[0])}function zigZagCorner(t,e,i,r,s,n,a){var o=getProjectingAngle(e,i),h=e.v[i%e._length],l=e.v[0===i?e._length-1:i-1],p=e.v[(i+1)%e._length],f=2===n?Math.sqrt(Math.pow(h[0]-l[0],2)+Math.pow(h[1]-l[1],2)):0,u=2===n?Math.sqrt(Math.pow(h[0]-p[0],2)+Math.pow(h[1]-p[1],2)):0;setPoint(t,e.v[i%e._length],o,a,r,u/(2*(s+1)),f/(2*(s+1)),n)}function zigZagSegment(t,e,i,r,s,n){for(var a=0;a<r;a+=1){var o=(a+1)/(r+1),h=2===s?Math.sqrt(Math.pow(e.points[3][0]-e.points[0][0],2)+Math.pow(e.points[3][1]-e.points[0][1],2)):0,l=e.normalAngle(o);setPoint(t,e.point(o),l,n,i,h/(2*(r+1)),h/(2*(r+1)),s),n=-n}return n}function linearOffset(t,e,i){var r=Math.atan2(e[0]-t[0],e[1]-t[1]);return[polarOffset(t,r,i),polarOffset(e,r,i)]}function offsetSegment(t,e){i=(h=linearOffset(t.points[0],t.points[1],e))[0],r=h[1],s=(h=linearOffset(t.points[1],t.points[2],e))[0],n=h[1],a=(h=linearOffset(t.points[2],t.points[3],e))[0],o=h[1];var i,r,s,n,a,o,h,l=lineIntersection(i,r,s,n);null===l&&(l=r);var p=lineIntersection(a,o,s,n);return null===p&&(p=a),new PolynomialBezier(i,l,p,o)}function joinLines(t,e,i,r,s){var n=e.points[3],a=i.points[0];if(3===r||pointEqual(n,a))return n;if(2===r){var o=-e.tangentAngle(1),h=-i.tangentAngle(0)+Math.PI,l=lineIntersection(n,polarOffset(n,o+Math.PI/2,100),a,polarOffset(a,o+Math.PI/2,100)),p=l?pointDistance(l,n):pointDistance(n,a)/2,f=polarOffset(n,o,2*p*roundCorner);return t.setXYAt(f[0],f[1],"o",t.length()-1),f=polarOffset(a,h,2*p*roundCorner),t.setTripleAt(a[0],a[1],a[0],a[1],f[0],f[1],t.length()),a}var u=lineIntersection(pointEqual(n,e.points[2])?e.points[0]:e.points[2],n,a,pointEqual(a,i.points[1])?i.points[3]:i.points[1]);return u&&pointDistance(u,n)<s?(t.setTripleAt(u[0],u[1],u[0],u[1],u[0],u[1],t.length()),u):n}function getIntersection(t,e){var i=t.intersections(e);return i.length&&floatEqual(i[0][0],1)&&i.shift(),i.length?i[0]:null}function pruneSegmentIntersection(t,e){var i=t.slice(),r=e.slice(),s=getIntersection(t[t.length-1],e[0]);return s&&(i[t.length-1]=t[t.length-1].split(s[0])[0],r[0]=e[0].split(s[1])[1]),t.length>1&&e.length>1&&(s=getIntersection(t[0],e[e.length-1]))?[[t[0].split(s[0])[0]],[e[e.length-1].split(s[1])[1]]]:[i,r]}function pruneIntersections(t){for(var e,i=1;i<t.length;i+=1)e=pruneSegmentIntersection(t[i-1],t[i]),t[i-1]=e[0],t[i]=e[1];return t.length>1&&(e=pruneSegmentIntersection(t[t.length-1],t[0]),t[t.length-1]=e[0],t[0]=e[1]),t}function offsetSegmentSplit(t,e){var i,r,s,n,a=t.inflectionPoints();if(0===a.length)return[offsetSegment(t,e)];if(1===a.length||floatEqual(a[1],1))return i=(s=t.split(a[0]))[0],r=s[1],[offsetSegment(i,e),offsetSegment(r,e)];i=(s=t.split(a[0]))[0];var o=(a[1]-a[0])/(1-a[0]);return n=(s=s[1].split(o))[0],r=s[1],[offsetSegment(i,e),offsetSegment(n,e),offsetSegment(r,e)]}function OffsetPathModifier(){}function getFontProperties(t){for(var e=t.fStyle?t.fStyle.split(" "):[],i="normal",r="normal",s=e.length,n=0;n<s;n+=1)switch(e[n].toLowerCase()){case"italic":r="italic";break;case"bold":i="700";break;case"black":i="900";break;case"medium":i="500";break;case"regular":case"normal":i="400";break;case"light":case"thin":i="200"}return{style:r,weight:t.fWeight||i}}extendPrototype([ShapeModifier],RepeaterModifier),RepeaterModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.c=PropertyFactory.getProp(t,e.c,0,null,this),this.o=PropertyFactory.getProp(t,e.o,0,null,this),this.tr=TransformPropertyFactory.getTransformProperty(t,e.tr,this),this.so=PropertyFactory.getProp(t,e.tr.so,0,.01,this),this.eo=PropertyFactory.getProp(t,e.tr.eo,0,.01,this),this.data=e,this.dynamicProperties.length||this.getValue(!0),this._isAnimated=!!this.dynamicProperties.length,this.pMatrix=new Matrix,this.rMatrix=new Matrix,this.sMatrix=new Matrix,this.tMatrix=new Matrix,this.matrix=new Matrix},RepeaterModifier.prototype.applyTransforms=function(t,e,i,r,s,n){var a=n?-1:1,o=r.s.v[0]+(1-r.s.v[0])*(1-s),h=r.s.v[1]+(1-r.s.v[1])*(1-s);t.translate(r.p.v[0]*a*s,r.p.v[1]*a*s,r.p.v[2]),e.translate(-r.a.v[0],-r.a.v[1],r.a.v[2]),e.rotate(-r.r.v*a*s),e.translate(r.a.v[0],r.a.v[1],r.a.v[2]),i.translate(-r.a.v[0],-r.a.v[1],r.a.v[2]),i.scale(n?1/o:o,n?1/h:h),i.translate(r.a.v[0],r.a.v[1],r.a.v[2])},RepeaterModifier.prototype.init=function(t,e,i,r){for(this.elem=t,this.arr=e,this.pos=i,this.elemsData=r,this._currentCopies=0,this._elements=[],this._groups=[],this.frameId=-1,this.initDynamicPropertyContainer(t),this.initModifierProperties(t,e[i]);i>0;)i-=1,this._elements.unshift(e[i]);this.dynamicProperties.length?this.k=!0:this.getValue(!0)},RepeaterModifier.prototype.resetElements=function(t){var e,i=t.length;for(e=0;e<i;e+=1)t[e]._processed=!1,"gr"===t[e].ty&&this.resetElements(t[e].it)},RepeaterModifier.prototype.cloneElements=function(t){var e=JSON.parse(JSON.stringify(t));return this.resetElements(e),e},RepeaterModifier.prototype.changeGroupRender=function(t,e){var i,r=t.length;for(i=0;i<r;i+=1)t[i]._render=e,"gr"===t[i].ty&&this.changeGroupRender(t[i].it,e)},RepeaterModifier.prototype.processShapes=function(t){var e,i,r,s,n,a=!1;if(this._mdf||t){var o,h=Math.ceil(this.c.v);if(this._groups.length<h){for(;this._groups.length<h;){var l={it:this.cloneElements(this._elements),ty:"gr"};l.it.push({a:{a:0,ix:1,k:[0,0]},nm:"Transform",o:{a:0,ix:7,k:100},p:{a:0,ix:2,k:[0,0]},r:{a:1,ix:6,k:[{s:0,e:0,t:0},{s:0,e:0,t:1}]},s:{a:0,ix:3,k:[100,100]},sa:{a:0,ix:5,k:0},sk:{a:0,ix:4,k:0},ty:"tr"}),this.arr.splice(0,0,l),this._groups.splice(0,0,l),this._currentCopies+=1}this.elem.reloadShapes(),a=!0}for(n=0,r=0;r<=this._groups.length-1;r+=1){if(o=n<h,this._groups[r]._render=o,this.changeGroupRender(this._groups[r].it,o),!o){var p=this.elemsData[r].it,f=p[p.length-1];0!==f.transform.op.v?(f.transform.op._mdf=!0,f.transform.op.v=0):f.transform.op._mdf=!1}n+=1}this._currentCopies=h;var u=this.o.v,c=u%1,m=u>0?Math.floor(u):Math.ceil(u),d=this.pMatrix.props,g=this.rMatrix.props,v=this.sMatrix.props;this.pMatrix.reset(),this.rMatrix.reset(),this.sMatrix.reset(),this.tMatrix.reset(),this.matrix.reset();var y,b,x=0;if(u>0){for(;x<m;)this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!1),x+=1;c&&(this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,c,!1),x+=c)}else if(u<0){for(;x>m;)this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!0),x-=1;c&&(this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,-c,!0),x-=c)}for(r=1===this.data.m?0:this._currentCopies-1,s=1===this.data.m?1:-1,n=this._currentCopies;n;){if(b=(i=(e=this.elemsData[r].it)[e.length-1].transform.mProps.v.props).length,e[e.length-1].transform.mProps._mdf=!0,e[e.length-1].transform.op._mdf=!0,e[e.length-1].transform.op.v=1===this._currentCopies?this.so.v:this.so.v+(this.eo.v-this.so.v)*(r/(this._currentCopies-1)),0!==x){for((0!==r&&1===s||r!==this._currentCopies-1&&-1===s)&&this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!1),this.matrix.transform(g[0],g[1],g[2],g[3],g[4],g[5],g[6],g[7],g[8],g[9],g[10],g[11],g[12],g[13],g[14],g[15]),this.matrix.transform(v[0],v[1],v[2],v[3],v[4],v[5],v[6],v[7],v[8],v[9],v[10],v[11],v[12],v[13],v[14],v[15]),this.matrix.transform(d[0],d[1],d[2],d[3],d[4],d[5],d[6],d[7],d[8],d[9],d[10],d[11],d[12],d[13],d[14],d[15]),y=0;y<b;y+=1)i[y]=this.matrix.props[y];this.matrix.reset()}else for(this.matrix.reset(),y=0;y<b;y+=1)i[y]=this.matrix.props[y];x+=1,n-=1,r+=s}}else for(n=this._currentCopies,r=0,s=1;n;)i=(e=this.elemsData[r].it)[e.length-1].transform.mProps.v.props,e[e.length-1].transform.mProps._mdf=!1,e[e.length-1].transform.op._mdf=!1,n-=1,r+=s;return a},RepeaterModifier.prototype.addShape=function(){},extendPrototype([ShapeModifier],RoundCornersModifier),RoundCornersModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.rd=PropertyFactory.getProp(t,e.r,0,null,this),this._isAnimated=!!this.rd.effectsSequence.length},RoundCornersModifier.prototype.processPath=function(t,e){var i,r=shapePool.newElement();r.c=t.c;var s,n,a,o,h,l,p,f,u,c,m,d,g=t._length,v=0;for(i=0;i<g;i+=1)s=t.v[i],a=t.o[i],n=t.i[i],s[0]===a[0]&&s[1]===a[1]&&s[0]===n[0]&&s[1]===n[1]?0!==i&&i!==g-1||t.c?(o=0===i?t.v[g-1]:t.v[i-1],l=(h=Math.sqrt(Math.pow(s[0]-o[0],2)+Math.pow(s[1]-o[1],2)))?Math.min(h/2,e)/h:0,p=m=s[0]+(o[0]-s[0])*l,f=d=s[1]-(s[1]-o[1])*l,u=p-(p-s[0])*roundCorner,c=f-(f-s[1])*roundCorner,r.setTripleAt(p,f,u,c,m,d,v),v+=1,o=i===g-1?t.v[0]:t.v[i+1],l=(h=Math.sqrt(Math.pow(s[0]-o[0],2)+Math.pow(s[1]-o[1],2)))?Math.min(h/2,e)/h:0,p=u=s[0]+(o[0]-s[0])*l,f=c=s[1]+(o[1]-s[1])*l,m=p-(p-s[0])*roundCorner,d=f-(f-s[1])*roundCorner,r.setTripleAt(p,f,u,c,m,d,v)):r.setTripleAt(s[0],s[1],a[0],a[1],n[0],n[1],v):r.setTripleAt(t.v[i][0],t.v[i][1],t.o[i][0],t.o[i][1],t.i[i][0],t.i[i][1],v),v+=1;return r},RoundCornersModifier.prototype.processShapes=function(t){var e,i,r,s,n,a,o=this.shapes.length,h=this.rd.v;if(0!==h)for(i=0;i<o;i+=1){if(a=(n=this.shapes[i]).localShapeCollection,n.shape._mdf||this._mdf||t)for(a.releaseShapes(),n.shape._mdf=!0,e=n.shape.paths.shapes,s=n.shape.paths._length,r=0;r<s;r+=1)a.addShape(this.processPath(e[r],h));n.shape.paths=n.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)},PolynomialBezier.prototype.point=function(t){return[((this.a[0]*t+this.b[0])*t+this.c[0])*t+this.d[0],((this.a[1]*t+this.b[1])*t+this.c[1])*t+this.d[1]]},PolynomialBezier.prototype.derivative=function(t){return[(3*t*this.a[0]+2*this.b[0])*t+this.c[0],(3*t*this.a[1]+2*this.b[1])*t+this.c[1]]},PolynomialBezier.prototype.tangentAngle=function(t){var e=this.derivative(t);return Math.atan2(e[1],e[0])},PolynomialBezier.prototype.normalAngle=function(t){var e=this.derivative(t);return Math.atan2(e[0],e[1])},PolynomialBezier.prototype.inflectionPoints=function(){var t=this.a[1]*this.b[0]-this.a[0]*this.b[1];if(floatZero(t))return[];var e=-.5*(this.a[1]*this.c[0]-this.a[0]*this.c[1])/t,i=e*e-1/3*(this.b[1]*this.c[0]-this.b[0]*this.c[1])/t;if(i<0)return[];var r=Math.sqrt(i);return floatZero(r)?r>0&&r<1?[e]:[]:[e-r,e+r].filter(function(t){return t>0&&t<1})},PolynomialBezier.prototype.split=function(t){if(t<=0)return[singlePoint(this.points[0]),this];if(t>=1)return[this,singlePoint(this.points[this.points.length-1])];var e=lerpPoint(this.points[0],this.points[1],t),i=lerpPoint(this.points[1],this.points[2],t),r=lerpPoint(this.points[2],this.points[3],t),s=lerpPoint(e,i,t),n=lerpPoint(i,r,t),a=lerpPoint(s,n,t);return[new PolynomialBezier(this.points[0],e,s,a,!0),new PolynomialBezier(a,n,r,this.points[3],!0)]},PolynomialBezier.prototype.bounds=function(){return{x:extrema(this,0),y:extrema(this,1)}},PolynomialBezier.prototype.boundingBox=function(){var t=this.bounds();return{left:t.x.min,right:t.x.max,top:t.y.min,bottom:t.y.max,width:t.x.max-t.x.min,height:t.y.max-t.y.min,cx:(t.x.max+t.x.min)/2,cy:(t.y.max+t.y.min)/2}},PolynomialBezier.prototype.intersections=function(t,e,i){void 0===e&&(e=2),void 0===i&&(i=7);var r=[];return intersectsImpl(intersectData(this,0,1),intersectData(t,0,1),0,e,r,i),r},PolynomialBezier.shapeSegment=function(t,e){var i=(e+1)%t.length();return new PolynomialBezier(t.v[e],t.o[e],t.i[i],t.v[i],!0)},PolynomialBezier.shapeSegmentInverted=function(t,e){var i=(e+1)%t.length();return new PolynomialBezier(t.v[i],t.i[i],t.o[e],t.v[e],!0)},extendPrototype([ShapeModifier],ZigZagModifier),ZigZagModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.amplitude=PropertyFactory.getProp(t,e.s,0,null,this),this.frequency=PropertyFactory.getProp(t,e.r,0,null,this),this.pointsType=PropertyFactory.getProp(t,e.pt,0,null,this),this._isAnimated=0!==this.amplitude.effectsSequence.length||0!==this.frequency.effectsSequence.length||0!==this.pointsType.effectsSequence.length},ZigZagModifier.prototype.processPath=function(t,e,i,r){var s=t._length,n=shapePool.newElement();if(n.c=t.c,t.c||(s-=1),0===s)return n;var a=-1,o=PolynomialBezier.shapeSegment(t,0);zigZagCorner(n,t,0,e,i,r,a);for(var h=0;h<s;h+=1)a=zigZagSegment(n,o,e,i,r,-a),o=h!==s-1||t.c?PolynomialBezier.shapeSegment(t,(h+1)%s):null,zigZagCorner(n,t,h+1,e,i,r,a);return n},ZigZagModifier.prototype.processShapes=function(t){var e,i,r,s,n,a,o=this.shapes.length,h=this.amplitude.v,l=Math.max(0,Math.round(this.frequency.v)),p=this.pointsType.v;if(0!==h)for(i=0;i<o;i+=1){if(a=(n=this.shapes[i]).localShapeCollection,n.shape._mdf||this._mdf||t)for(a.releaseShapes(),n.shape._mdf=!0,e=n.shape.paths.shapes,s=n.shape.paths._length,r=0;r<s;r+=1)a.addShape(this.processPath(e[r],h,l,p));n.shape.paths=n.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)},extendPrototype([ShapeModifier],OffsetPathModifier),OffsetPathModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.amount=PropertyFactory.getProp(t,e.a,0,null,this),this.miterLimit=PropertyFactory.getProp(t,e.ml,0,null,this),this.lineJoin=e.lj,this._isAnimated=0!==this.amount.effectsSequence.length},OffsetPathModifier.prototype.processPath=function(t,e,i,r){var s=shapePool.newElement();s.c=t.c;var n,a,o,h=t.length();t.c||(h-=1);var l=[];for(n=0;n<h;n+=1)o=PolynomialBezier.shapeSegment(t,n),l.push(offsetSegmentSplit(o,e));if(!t.c)for(n=h-1;n>=0;n-=1)o=PolynomialBezier.shapeSegmentInverted(t,n),l.push(offsetSegmentSplit(o,e));l=pruneIntersections(l);var p=null,f=null;for(n=0;n<l.length;n+=1){var u=l[n];for(f&&(p=joinLines(s,f,u[0],i,r)),f=u[u.length-1],a=0;a<u.length;a+=1)o=u[a],p&&pointEqual(o.points[0],p)?s.setXYAt(o.points[1][0],o.points[1][1],"o",s.length()-1):s.setTripleAt(o.points[0][0],o.points[0][1],o.points[1][0],o.points[1][1],o.points[0][0],o.points[0][1],s.length()),s.setTripleAt(o.points[3][0],o.points[3][1],o.points[3][0],o.points[3][1],o.points[2][0],o.points[2][1],s.length()),p=o.points[3]}return l.length&&joinLines(s,f,l[0][0],i,r),s},OffsetPathModifier.prototype.processShapes=function(t){var e,i,r,s,n,a,o=this.shapes.length,h=this.amount.v,l=this.miterLimit.v,p=this.lineJoin;if(0!==h)for(i=0;i<o;i+=1){if(a=(n=this.shapes[i]).localShapeCollection,n.shape._mdf||this._mdf||t)for(a.releaseShapes(),n.shape._mdf=!0,e=n.shape.paths.shapes,s=n.shape.paths._length,r=0;r<s;r+=1)a.addShape(this.processPath(e[r],h,p,l));n.shape.paths=n.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)};var FontManager=function(){var t={w:0,size:0,shapes:[],data:{shapes:[]}},e=[];e=e.concat([2304,2305,2306,2307,2362,2363,2364,2364,2366,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2387,2388,2389,2390,2391,2402,2403]);var i=127988,r=["d83cdffb","d83cdffc","d83cdffd","d83cdffe","d83cdfff"];function s(t,e){var i=createTag("span");i.setAttribute("aria-hidden",!0),i.style.fontFamily=e;var r=createTag("span");r.innerText="giItT1WQy@!-/#",i.style.position="absolute",i.style.left="-10000px",i.style.top="-10000px",i.style.fontSize="300px",i.style.fontVariant="normal",i.style.fontStyle="normal",i.style.fontWeight="normal",i.style.letterSpacing="0",i.appendChild(r),document.body.appendChild(i);var s=r.offsetWidth;return r.style.fontFamily=function(t){var e,i=t.split(","),r=i.length,s=[];for(e=0;e<r;e+=1)"sans-serif"!==i[e]&&"monospace"!==i[e]&&s.push(i[e]);return s.join(",")}(t)+", "+e,{node:r,w:s,parent:i}}function n(t,e){var i,r=document.body&&e?"svg":"canvas",s=getFontProperties(t);if("svg"===r){var n=createNS("text");n.style.fontSize="100px",n.setAttribute("font-family",t.fFamily),n.setAttribute("font-style",s.style),n.setAttribute("font-weight",s.weight),n.textContent="1",t.fClass?(n.style.fontFamily="inherit",n.setAttribute("class",t.fClass)):n.style.fontFamily=t.fFamily,e.appendChild(n),i=n}else{var a=new OffscreenCanvas(500,500).getContext("2d");a.font=s.style+" "+s.weight+" 100px "+t.fFamily,i=a}return{measureText:function(t){return"svg"===r?(i.textContent=t,i.getComputedTextLength()):i.measureText(t).width}}}function a(t){var e=0,i=t.charCodeAt(0);if(i>=55296&&i<=56319){var r=t.charCodeAt(1);r>=56320&&r<=57343&&(e=1024*(i-55296)+r-56320+65536)}return e}function o(t){var e=a(t);return e>=127462&&e<=127487}var h=function(){this.fonts=[],this.chars=null,this.typekitLoaded=0,this.isLoaded=!1,this._warned=!1,this.initTime=Date.now(),this.setIsLoadedBinded=this.setIsLoaded.bind(this),this.checkLoadedFontsBinded=this.checkLoadedFonts.bind(this)};return h.isModifier=function(t,e){var i=t.toString(16)+e.toString(16);return -1!==r.indexOf(i)},h.isZeroWidthJoiner=function(t){return 8205===t},h.isFlagEmoji=function(t){return o(t.substr(0,2))&&o(t.substr(2,2))},h.isRegionalCode=o,h.isCombinedCharacter=function(t){return -1!==e.indexOf(t)},h.isRegionalFlag=function(t,e){var r=a(t.substr(e,2));if(r!==i)return!1;var s=0;for(e+=2;s<5;){if((r=a(t.substr(e,2)))<917601||r>917626)return!1;s+=1,e+=2}return 917631===a(t.substr(e,2))},h.isVariationSelector=function(t){return 65039===t},h.BLACK_FLAG_CODE_POINT=i,h.prototype={addChars:function(t){if(t){this.chars||(this.chars=[]);var e,i,r,s=t.length,n=this.chars.length;for(e=0;e<s;e+=1){for(i=0,r=!1;i<n;)this.chars[i].style===t[e].style&&this.chars[i].fFamily===t[e].fFamily&&this.chars[i].ch===t[e].ch&&(r=!0),i+=1;r||(this.chars.push(t[e]),n+=1)}}},addFonts:function(t,e){if(t){if(this.chars)return this.isLoaded=!0,void(this.fonts=t.list);if(!document.body)return this.isLoaded=!0,t.list.forEach(function(t){t.helper=n(t),t.cache={}}),void(this.fonts=t.list);var i,r=t.list,a=r.length,o=a;for(i=0;i<a;i+=1){var h,l,p=!0;if(r[i].loaded=!1,r[i].monoCase=s(r[i].fFamily,"monospace"),r[i].sansCase=s(r[i].fFamily,"sans-serif"),r[i].fPath){if("p"===r[i].fOrigin||3===r[i].origin){if((h=document.querySelectorAll('style[f-forigin="p"][f-family="'+r[i].fFamily+'"], style[f-origin="3"][f-family="'+r[i].fFamily+'"]')).length>0&&(p=!1),p){var f=createTag("style");f.setAttribute("f-forigin",r[i].fOrigin),f.setAttribute("f-origin",r[i].origin),f.setAttribute("f-family",r[i].fFamily),f.type="text/css",f.innerText="@font-face {font-family: "+r[i].fFamily+"; font-style: normal; src: url('"+r[i].fPath+"');}",e.appendChild(f)}}else if("g"===r[i].fOrigin||1===r[i].origin){for(h=document.querySelectorAll('link[f-forigin="g"], link[f-origin="1"]'),l=0;l<h.length;l+=1)-1!==h[l].href.indexOf(r[i].fPath)&&(p=!1);if(p){var u=createTag("link");u.setAttribute("f-forigin",r[i].fOrigin),u.setAttribute("f-origin",r[i].origin),u.type="text/css",u.rel="stylesheet",u.href=r[i].fPath,document.body.appendChild(u)}}else if("t"===r[i].fOrigin||2===r[i].origin){for(h=document.querySelectorAll('script[f-forigin="t"], script[f-origin="2"]'),l=0;l<h.length;l+=1)r[i].fPath===h[l].src&&(p=!1);if(p){var c=createTag("link");c.setAttribute("f-forigin",r[i].fOrigin),c.setAttribute("f-origin",r[i].origin),c.setAttribute("rel","stylesheet"),c.setAttribute("href",r[i].fPath),e.appendChild(c)}}}else r[i].loaded=!0,o-=1;r[i].helper=n(r[i],e),r[i].cache={},this.fonts.push(r[i])}0===o?this.isLoaded=!0:setTimeout(this.checkLoadedFonts.bind(this),100)}else this.isLoaded=!0},getCharData:function(e,i,r){for(var s=0,n=this.chars.length;s<n;){if(this.chars[s].ch===e&&this.chars[s].style===i&&this.chars[s].fFamily===r)return this.chars[s];s+=1}return("string"==typeof e&&13!==e.charCodeAt(0)||!e)&&console&&console.warn&&!this._warned&&(this._warned=!0,console.warn("Missing character from exported characters list: ",e,i,r)),t},getFontByName:function(t){for(var e=0,i=this.fonts.length;e<i;){if(this.fonts[e].fName===t)return this.fonts[e];e+=1}return this.fonts[0]},measureText:function(t,e,i){var r=this.getFontByName(e),s=t;if(!r.cache[s]){var n=r.helper;if(" "===t){var a=n.measureText("|"+t+"|"),o=n.measureText("||");r.cache[s]=(a-o)/100}else r.cache[s]=n.measureText(t)/100}return r.cache[s]*i},checkLoadedFonts:function(){var t,e,i,r=this.fonts.length,s=r;for(t=0;t<r;t+=1)this.fonts[t].loaded?s-=1:"n"===this.fonts[t].fOrigin||0===this.fonts[t].origin?this.fonts[t].loaded=!0:(e=this.fonts[t].monoCase.node,i=this.fonts[t].monoCase.w,e.offsetWidth!==i?(s-=1,this.fonts[t].loaded=!0):(e=this.fonts[t].sansCase.node,i=this.fonts[t].sansCase.w,e.offsetWidth!==i&&(s-=1,this.fonts[t].loaded=!0)),this.fonts[t].loaded&&(this.fonts[t].sansCase.parent.parentNode.removeChild(this.fonts[t].sansCase.parent),this.fonts[t].monoCase.parent.parentNode.removeChild(this.fonts[t].monoCase.parent)));0!==s&&Date.now()-this.initTime<5e3?setTimeout(this.checkLoadedFontsBinded,20):setTimeout(this.setIsLoadedBinded,10)},setIsLoaded:function(){this.isLoaded=!0}},h}();function SlotManager(t){this.animationData=t}function slotFactory(t){return new SlotManager(t)}function RenderableElement(){}SlotManager.prototype.getProp=function(t){return this.animationData.slots&&this.animationData.slots[t.sid]?Object.assign(t,this.animationData.slots[t.sid].p):t},RenderableElement.prototype={initRenderable:function(){this.isInRange=!1,this.hidden=!1,this.isTransparent=!1,this.renderableComponents=[]},addRenderableComponent:function(t){-1===this.renderableComponents.indexOf(t)&&this.renderableComponents.push(t)},removeRenderableComponent:function(t){-1!==this.renderableComponents.indexOf(t)&&this.renderableComponents.splice(this.renderableComponents.indexOf(t),1)},prepareRenderableFrame:function(t){this.checkLayerLimits(t)},checkTransparency:function(){this.finalTransform.mProp.o.v<=0?!this.isTransparent&&this.globalData.renderConfig.hideOnTransparent&&(this.isTransparent=!0,this.hide()):this.isTransparent&&(this.isTransparent=!1,this.show())},checkLayerLimits:function(t){this.data.ip-this.data.st<=t&&this.data.op-this.data.st>t?!0!==this.isInRange&&(this.globalData._mdf=!0,this._mdf=!0,this.isInRange=!0,this.show()):!1!==this.isInRange&&(this.globalData._mdf=!0,this.isInRange=!1,this.hide())},renderRenderable:function(){var t,e=this.renderableComponents.length;for(t=0;t<e;t+=1)this.renderableComponents[t].renderFrame(this._isFirstFrame)},sourceRectAtTime:function(){return{top:0,left:0,width:100,height:100}},getLayerSize:function(){return 5===this.data.ty?{w:this.data.textData.width,h:this.data.textData.height}:{w:this.data.width,h:this.data.height}}};var blendModeEnums,getBlendMode=(blendModeEnums={0:"source-over",1:"multiply",2:"screen",3:"overlay",4:"darken",5:"lighten",6:"color-dodge",7:"color-burn",8:"hard-light",9:"soft-light",10:"difference",11:"exclusion",12:"hue",13:"saturation",14:"color",15:"luminosity"},function(t){return blendModeEnums[t]||""});function SliderEffect(t,e,i){this.p=PropertyFactory.getProp(e,t.v,0,0,i)}function AngleEffect(t,e,i){this.p=PropertyFactory.getProp(e,t.v,0,0,i)}function ColorEffect(t,e,i){this.p=PropertyFactory.getProp(e,t.v,1,0,i)}function PointEffect(t,e,i){this.p=PropertyFactory.getProp(e,t.v,1,0,i)}function LayerIndexEffect(t,e,i){this.p=PropertyFactory.getProp(e,t.v,0,0,i)}function MaskIndexEffect(t,e,i){this.p=PropertyFactory.getProp(e,t.v,0,0,i)}function CheckboxEffect(t,e,i){this.p=PropertyFactory.getProp(e,t.v,0,0,i)}function NoValueEffect(){this.p={}}function EffectsManager(t,e){var i,r=t.ef||[];this.effectElements=[];var s,n=r.length;for(i=0;i<n;i+=1)s=new GroupEffect(r[i],e),this.effectElements.push(s)}function GroupEffect(t,e){this.init(t,e)}function BaseElement(){}function FrameElement(){}function FootageElement(t,e,i){this.initFrame(),this.initRenderable(),this.assetData=e.getAssetData(t.refId),this.footageData=e.imageLoader.getAsset(this.assetData),this.initBaseData(t,e,i)}function AudioElement(t,e,i){this.initFrame(),this.initRenderable(),this.assetData=e.getAssetData(t.refId),this.initBaseData(t,e,i),this._isPlaying=!1,this._canPlay=!1;var r=this.globalData.getAssetsPath(this.assetData);this.audio=this.globalData.audioController.createAudio(r),this._currentTime=0,this.globalData.audioController.addAudio(this),this._volumeMultiplier=1,this._volume=1,this._previousVolume=null,this.tm=t.tm?PropertyFactory.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0},this.lv=PropertyFactory.getProp(this,t.au&&t.au.lv?t.au.lv:{k:[100]},1,.01,this)}function BaseRenderer(){}extendPrototype([DynamicPropertyContainer],GroupEffect),GroupEffect.prototype.getValue=GroupEffect.prototype.iterateDynamicProperties,GroupEffect.prototype.init=function(t,e){this.data=t,this.effectElements=[],this.initDynamicPropertyContainer(e);var i,r,s=this.data.ef.length,n=this.data.ef;for(i=0;i<s;i+=1){switch(r=null,n[i].ty){case 0:r=new SliderEffect(n[i],e,this);break;case 1:r=new AngleEffect(n[i],e,this);break;case 2:r=new ColorEffect(n[i],e,this);break;case 3:r=new PointEffect(n[i],e,this);break;case 4:case 7:r=new CheckboxEffect(n[i],e,this);break;case 10:r=new LayerIndexEffect(n[i],e,this);break;case 11:r=new MaskIndexEffect(n[i],e,this);break;case 5:r=new EffectsManager(n[i],e,this);break;default:r=new NoValueEffect(n[i],e,this)}r&&this.effectElements.push(r)}},BaseElement.prototype={checkMasks:function(){if(!this.data.hasMask)return!1;for(var t=0,e=this.data.masksProperties.length;t<e;){if("n"!==this.data.masksProperties[t].mode&&!1!==this.data.masksProperties[t].cl)return!0;t+=1}return!1},initExpressions:function(){var t=getExpressionInterfaces();if(t){var e=t("layer"),i=t("effects"),r=t("shape"),s=t("text"),n=t("comp");this.layerInterface=e(this),this.data.hasMask&&this.maskManager&&this.layerInterface.registerMaskInterface(this.maskManager);var a=i.createEffectsInterface(this,this.layerInterface);this.layerInterface.registerEffectsInterface(a),0===this.data.ty||this.data.xt?this.compInterface=n(this):4===this.data.ty?(this.layerInterface.shapeInterface=r(this.shapesData,this.itemsData,this.layerInterface),this.layerInterface.content=this.layerInterface.shapeInterface):5===this.data.ty&&(this.layerInterface.textInterface=s(this),this.layerInterface.text=this.layerInterface.textInterface)}},setBlendMode:function(){var t=getBlendMode(this.data.bm);(this.baseElement||this.layerElement).style["mix-blend-mode"]=t},initBaseData:function(t,e,i){this.globalData=e,this.comp=i,this.data=t,this.layerId=createElementID(),this.data.sr||(this.data.sr=1),this.effectsManager=new EffectsManager(this.data,this,this.dynamicProperties)},getType:function(){return this.type},sourceRectAtTime:function(){}},FrameElement.prototype={initFrame:function(){this._isFirstFrame=!1,this.dynamicProperties=[],this._mdf=!1},prepareProperties:function(t,e){var i,r=this.dynamicProperties.length;for(i=0;i<r;i+=1)(e||this._isParent&&"transform"===this.dynamicProperties[i].propType)&&(this.dynamicProperties[i].getValue(),this.dynamicProperties[i]._mdf&&(this.globalData._mdf=!0,this._mdf=!0))},addDynamicProperty:function(t){-1===this.dynamicProperties.indexOf(t)&&this.dynamicProperties.push(t)}},FootageElement.prototype.prepareFrame=function(){},extendPrototype([RenderableElement,BaseElement,FrameElement],FootageElement),FootageElement.prototype.getBaseElement=function(){return null},FootageElement.prototype.renderFrame=function(){},FootageElement.prototype.destroy=function(){},FootageElement.prototype.initExpressions=function(){var t=getExpressionInterfaces();if(t){var e=t("footage");this.layerInterface=e(this)}},FootageElement.prototype.getFootageData=function(){return this.footageData},AudioElement.prototype.prepareFrame=function(t){if(this.prepareRenderableFrame(t,!0),this.prepareProperties(t,!0),this.tm._placeholder)this._currentTime=t/this.data.sr;else{var e=this.tm.v;this._currentTime=e}this._volume=this.lv.v[0];var i=this._volume*this._volumeMultiplier;this._previousVolume!==i&&(this._previousVolume=i,this.audio.volume(i))},extendPrototype([RenderableElement,BaseElement,FrameElement],AudioElement),AudioElement.prototype.renderFrame=function(){this.isInRange&&this._canPlay&&(this._isPlaying?(!this.audio.playing()||Math.abs(this._currentTime/this.globalData.frameRate-this.audio.seek())>.1)&&this.audio.seek(this._currentTime/this.globalData.frameRate):(this.audio.play(),this.audio.seek(this._currentTime/this.globalData.frameRate),this._isPlaying=!0))},AudioElement.prototype.show=function(){},AudioElement.prototype.hide=function(){this.audio.pause(),this._isPlaying=!1},AudioElement.prototype.pause=function(){this.audio.pause(),this._isPlaying=!1,this._canPlay=!1},AudioElement.prototype.resume=function(){this._canPlay=!0},AudioElement.prototype.setRate=function(t){this.audio.rate(t)},AudioElement.prototype.volume=function(t){this._volumeMultiplier=t,this._previousVolume=t*this._volume,this.audio.volume(this._previousVolume)},AudioElement.prototype.getBaseElement=function(){return null},AudioElement.prototype.destroy=function(){},AudioElement.prototype.sourceRectAtTime=function(){},AudioElement.prototype.initExpressions=function(){},BaseRenderer.prototype.checkLayers=function(t){var e,i,r=this.layers.length;for(this.completeLayers=!0,e=r-1;e>=0;e-=1)this.elements[e]||(i=this.layers[e]).ip-i.st<=t-this.layers[e].st&&i.op-i.st>t-this.layers[e].st&&this.buildItem(e),this.completeLayers=!!this.elements[e]&&this.completeLayers;this.checkPendingElements()},BaseRenderer.prototype.createItem=function(t){switch(t.ty){case 2:return this.createImage(t);case 0:return this.createComp(t);case 1:return this.createSolid(t);case 3:default:return this.createNull(t);case 4:return this.createShape(t);case 5:return this.createText(t);case 6:return this.createAudio(t);case 13:return this.createCamera(t);case 15:return this.createFootage(t)}},BaseRenderer.prototype.createCamera=function(){throw Error("You're using a 3d camera. Try the html renderer.")},BaseRenderer.prototype.createAudio=function(t){return new AudioElement(t,this.globalData,this)},BaseRenderer.prototype.createFootage=function(t){return new FootageElement(t,this.globalData,this)},BaseRenderer.prototype.buildAllItems=function(){var t,e=this.layers.length;for(t=0;t<e;t+=1)this.buildItem(t);this.checkPendingElements()},BaseRenderer.prototype.includeLayers=function(t){this.completeLayers=!1;var e,i,r=t.length,s=this.layers.length;for(e=0;e<r;e+=1)for(i=0;i<s;){if(this.layers[i].id===t[e].id){this.layers[i]=t[e];break}i+=1}},BaseRenderer.prototype.setProjectInterface=function(t){this.globalData.projectInterface=t},BaseRenderer.prototype.initItems=function(){this.globalData.progressiveLoad||this.buildAllItems()},BaseRenderer.prototype.buildElementParenting=function(t,e,i){for(var r=this.elements,s=this.layers,n=0,a=s.length;n<a;)s[n].ind==e&&(r[n]&&!0!==r[n]?(i.push(r[n]),r[n].setAsParent(),void 0!==s[n].parent?this.buildElementParenting(t,s[n].parent,i):t.setHierarchy(i)):(this.buildItem(n),this.addPendingElement(t))),n+=1},BaseRenderer.prototype.addPendingElement=function(t){this.pendingElements.push(t)},BaseRenderer.prototype.searchExtraCompositions=function(t){var e,i=t.length;for(e=0;e<i;e+=1)if(t[e].xt){var r=this.createComp(t[e]);r.initExpressions(),this.globalData.projectInterface.registerComposition(r)}},BaseRenderer.prototype.getElementById=function(t){var e,i=this.elements.length;for(e=0;e<i;e+=1)if(this.elements[e].data.ind===t)return this.elements[e];return null},BaseRenderer.prototype.getElementByPath=function(t){var e,i=t.shift();if("number"==typeof i)e=this.elements[i];else{var r,s=this.elements.length;for(r=0;r<s;r+=1)if(this.elements[r].data.nm===i){e=this.elements[r];break}}return 0===t.length?e:e.getElementByPath(t)},BaseRenderer.prototype.setupGlobalData=function(t,e){this.globalData.fontManager=new FontManager,this.globalData.slotManager=slotFactory(t),this.globalData.fontManager.addChars(t.chars),this.globalData.fontManager.addFonts(t.fonts,e),this.globalData.getAssetData=this.animationItem.getAssetData.bind(this.animationItem),this.globalData.getAssetsPath=this.animationItem.getAssetsPath.bind(this.animationItem),this.globalData.imageLoader=this.animationItem.imagePreloader,this.globalData.audioController=this.animationItem.audioController,this.globalData.frameId=0,this.globalData.frameRate=t.fr,this.globalData.nm=t.nm,this.globalData.compSize={w:t.w,h:t.h}};var effectTypes={TRANSFORM_EFFECT:"transformEFfect"};function TransformElement(){}function MaskElement(t,e,i){this.data=t,this.element=e,this.globalData=i,this.storedData=[],this.masksProperties=this.data.masksProperties||[],this.maskElement=null;var r,s,n,a=this.globalData.defs,o=this.masksProperties?this.masksProperties.length:0;this.viewData=createSizedArray(o),this.solidPath="";var h,l,p,f,u,c,m=this.masksProperties,d=0,g=[],v=createElementID(),y="clipPath",b="clip-path";for(s=0;s<o;s+=1)if(("a"!==m[s].mode&&"n"!==m[s].mode||m[s].inv||100!==m[s].o.k||m[s].o.x)&&(y="mask",b="mask"),"s"!==m[s].mode&&"i"!==m[s].mode||0!==d?p=null:((p=createNS("rect")).setAttribute("fill","#ffffff"),p.setAttribute("width",this.element.comp.data.w||0),p.setAttribute("height",this.element.comp.data.h||0),g.push(p)),n=createNS("path"),"n"===m[s].mode)this.viewData[s]={op:PropertyFactory.getProp(this.element,m[s].o,0,.01,this.element),prop:ShapePropertyFactory.getShapeProp(this.element,m[s],3),elem:n,lastPath:""},a.appendChild(n);else{if(d+=1,n.setAttribute("fill","s"===m[s].mode?"#000000":"#ffffff"),n.setAttribute("clip-rule","nonzero"),0!==m[s].x.k?(y="mask",b="mask",c=PropertyFactory.getProp(this.element,m[s].x,0,null,this.element),r=createElementID(),(f=createNS("filter")).setAttribute("id",r),(u=createNS("feMorphology")).setAttribute("operator","erode"),u.setAttribute("in","SourceGraphic"),u.setAttribute("radius","0"),f.appendChild(u),a.appendChild(f),n.setAttribute("stroke","s"===m[s].mode?"#000000":"#ffffff")):(u=null,c=null),this.storedData[s]={elem:n,x:c,expan:u,lastPath:"",lastOperator:"",filterId:r,lastRadius:0},"i"===m[s].mode){l=g.length;var x=createNS("g");for(h=0;h<l;h+=1)x.appendChild(g[h]);var _=createNS("mask");_.setAttribute("mask-type","alpha"),_.setAttribute("id",v+"_"+d),_.appendChild(n),a.appendChild(_),x.setAttribute("mask","url("+getLocationHref()+"#"+v+"_"+d+")"),g.length=0,g.push(x)}else g.push(n);m[s].inv&&!this.solidPath&&(this.solidPath=this.createLayerSolidPath()),this.viewData[s]={elem:n,lastPath:"",op:PropertyFactory.getProp(this.element,m[s].o,0,.01,this.element),prop:ShapePropertyFactory.getShapeProp(this.element,m[s],3),invRect:p},this.viewData[s].prop.k||this.drawPath(m[s],this.viewData[s].prop.v,this.viewData[s])}for(this.maskElement=createNS(y),o=g.length,s=0;s<o;s+=1)this.maskElement.appendChild(g[s]);d>0&&(this.maskElement.setAttribute("id",v),this.element.maskedElement.setAttribute(b,"url("+getLocationHref()+"#"+v+")"),a.appendChild(this.maskElement)),this.viewData.length&&this.element.addRenderableComponent(this)}TransformElement.prototype={initTransform:function(){var t=new Matrix;this.finalTransform={mProp:this.data.ks?TransformPropertyFactory.getTransformProperty(this,this.data.ks,this):{o:0},_matMdf:!1,_localMatMdf:!1,_opMdf:!1,mat:t,localMat:t,localOpacity:1},this.data.ao&&(this.finalTransform.mProp.autoOriented=!0),this.data.ty},renderTransform:function(){if(this.finalTransform._opMdf=this.finalTransform.mProp.o._mdf||this._isFirstFrame,this.finalTransform._matMdf=this.finalTransform.mProp._mdf||this._isFirstFrame,this.hierarchy){var t,e=this.finalTransform.mat,i=0,r=this.hierarchy.length;if(!this.finalTransform._matMdf)for(;i<r;){if(this.hierarchy[i].finalTransform.mProp._mdf){this.finalTransform._matMdf=!0;break}i+=1}if(this.finalTransform._matMdf)for(t=this.finalTransform.mProp.v.props,e.cloneFromProps(t),i=0;i<r;i+=1)e.multiply(this.hierarchy[i].finalTransform.mProp.v)}this.finalTransform._matMdf&&(this.finalTransform._localMatMdf=this.finalTransform._matMdf),this.finalTransform._opMdf&&(this.finalTransform.localOpacity=this.finalTransform.mProp.o.v)},renderLocalTransform:function(){if(this.localTransforms){var t=0,e=this.localTransforms.length;if(this.finalTransform._localMatMdf=this.finalTransform._matMdf,!this.finalTransform._localMatMdf||!this.finalTransform._opMdf)for(;t<e;)this.localTransforms[t]._mdf&&(this.finalTransform._localMatMdf=!0),this.localTransforms[t]._opMdf&&!this.finalTransform._opMdf&&(this.finalTransform.localOpacity=this.finalTransform.mProp.o.v,this.finalTransform._opMdf=!0),t+=1;if(this.finalTransform._localMatMdf){var i=this.finalTransform.localMat;for(this.localTransforms[0].matrix.clone(i),t=1;t<e;t+=1){var r=this.localTransforms[t].matrix;i.multiply(r)}i.multiply(this.finalTransform.mat)}if(this.finalTransform._opMdf){var s=this.finalTransform.localOpacity;for(t=0;t<e;t+=1)s*=.01*this.localTransforms[t].opacity;this.finalTransform.localOpacity=s}}},searchEffectTransforms:function(){if(this.renderableEffectsManager){var t=this.renderableEffectsManager.getEffects(effectTypes.TRANSFORM_EFFECT);if(t.length){this.localTransforms=[],this.finalTransform.localMat=new Matrix;var e=0,i=t.length;for(e=0;e<i;e+=1)this.localTransforms.push(t[e])}}},globalToLocal:function(t){var e=[];e.push(this.finalTransform);for(var i,r=!0,s=this.comp;r;)s.finalTransform?(s.data.hasMask&&e.splice(0,0,s.finalTransform),s=s.comp):r=!1;var n,a=e.length;for(i=0;i<a;i+=1)n=e[i].mat.applyToPointArray(0,0,0),t=[t[0]-n[0],t[1]-n[1],0];return t},mHelper:new Matrix},MaskElement.prototype.getMaskProperty=function(t){return this.viewData[t].prop},MaskElement.prototype.renderFrame=function(t){var e,i=this.element.finalTransform.mat,r=this.masksProperties.length;for(e=0;e<r;e+=1)if((this.viewData[e].prop._mdf||t)&&this.drawPath(this.masksProperties[e],this.viewData[e].prop.v,this.viewData[e]),(this.viewData[e].op._mdf||t)&&this.viewData[e].elem.setAttribute("fill-opacity",this.viewData[e].op.v),"n"!==this.masksProperties[e].mode&&(this.viewData[e].invRect&&(this.element.finalTransform.mProp._mdf||t)&&this.viewData[e].invRect.setAttribute("transform",i.getInverseMatrix().to2dCSS()),this.storedData[e].x&&(this.storedData[e].x._mdf||t))){var s=this.storedData[e].expan;this.storedData[e].x.v<0?("erode"!==this.storedData[e].lastOperator&&(this.storedData[e].lastOperator="erode",this.storedData[e].elem.setAttribute("filter","url("+getLocationHref()+"#"+this.storedData[e].filterId+")")),s.setAttribute("radius",-this.storedData[e].x.v)):("dilate"!==this.storedData[e].lastOperator&&(this.storedData[e].lastOperator="dilate",this.storedData[e].elem.setAttribute("filter",null)),this.storedData[e].elem.setAttribute("stroke-width",2*this.storedData[e].x.v))}},MaskElement.prototype.getMaskelement=function(){return this.maskElement},MaskElement.prototype.createLayerSolidPath=function(){return"M0,0 "+(" h"+this.globalData.compSize.w+" v"+this.globalData.compSize.h+" h-"+this.globalData.compSize.w+(" v-"+this.globalData.compSize.h))+" "},MaskElement.prototype.drawPath=function(t,e,i){var r,s,n=" M"+e.v[0][0]+","+e.v[0][1];for(s=e._length,r=1;r<s;r+=1)n+=" C"+e.o[r-1][0]+","+e.o[r-1][1]+" "+e.i[r][0]+","+e.i[r][1]+" "+e.v[r][0]+","+e.v[r][1];if(e.c&&s>1&&(n+=" C"+e.o[r-1][0]+","+e.o[r-1][1]+" "+e.i[0][0]+","+e.i[0][1]+" "+e.v[0][0]+","+e.v[0][1]),i.lastPath!==n){var a="";i.elem&&(e.c&&(a=t.inv?this.solidPath+n:n),i.elem.setAttribute("d",a)),i.lastPath=n}},MaskElement.prototype.destroy=function(){this.element=null,this.globalData=null,this.maskElement=null,this.data=null,this.masksProperties=null};var filtersFactory=function(){var t={};return t.createFilter=function(t,e){var i=createNS("filter");return i.setAttribute("id",t),!0!==e&&(i.setAttribute("filterUnits","objectBoundingBox"),i.setAttribute("x","0%"),i.setAttribute("y","0%"),i.setAttribute("width","100%"),i.setAttribute("height","100%")),i},t.createAlphaToLuminanceFilter=function(){var t=createNS("feColorMatrix");return t.setAttribute("type","matrix"),t.setAttribute("color-interpolation-filters","sRGB"),t.setAttribute("values","0 0 0 1 0  0 0 0 1 0  0 0 0 1 0  0 0 0 1 1"),t},t}(),featureSupport=function(){var t={maskType:!0,svgLumaHidden:!0,offscreenCanvas:"undefined"!=typeof OffscreenCanvas};return(/MSIE 10/i.test(navigator.userAgent)||/MSIE 9/i.test(navigator.userAgent)||/rv:11.0/i.test(navigator.userAgent)||/Edge\/\d./i.test(navigator.userAgent))&&(t.maskType=!1),/firefox/i.test(navigator.userAgent)&&(t.svgLumaHidden=!1),t}(),registeredEffects$1={},idPrefix="filter_result_";function SVGEffects(t){var e,i,r="SourceGraphic",s=t.data.ef?t.data.ef.length:0,n=createElementID(),a=filtersFactory.createFilter(n,!0),o=0;for(this.filters=[],e=0;e<s;e+=1){i=null;var h=t.data.ef[e].ty;registeredEffects$1[h]&&(i=new(0,registeredEffects$1[h].effect)(a,t.effectsManager.effectElements[e],t,idPrefix+o,r),r=idPrefix+o,registeredEffects$1[h].countsAsEffect&&(o+=1)),i&&this.filters.push(i)}o&&(t.globalData.defs.appendChild(a),t.layerElement.setAttribute("filter","url("+getLocationHref()+"#"+n+")")),this.filters.length&&t.addRenderableComponent(this)}function registerEffect$1(t,e,i){registeredEffects$1[t]={effect:e,countsAsEffect:i}}function SVGBaseElement(){}function HierarchyElement(){}function RenderableDOMElement(){}function IImageElement(t,e,i){this.assetData=e.getAssetData(t.refId),this.assetData&&this.assetData.sid&&(this.assetData=e.slotManager.getProp(this.assetData)),this.initElement(t,e,i),this.sourceRect={top:0,left:0,width:this.assetData.w,height:this.assetData.h}}function ProcessedElement(t,e){this.elem=t,this.pos=e}function IShapeElement(){}SVGEffects.prototype.renderFrame=function(t){var e,i=this.filters.length;for(e=0;e<i;e+=1)this.filters[e].renderFrame(t)},SVGEffects.prototype.getEffects=function(t){var e,i=this.filters.length,r=[];for(e=0;e<i;e+=1)this.filters[e].type===t&&r.push(this.filters[e]);return r},SVGBaseElement.prototype={initRendererElement:function(){this.layerElement=createNS("g")},createContainerElements:function(){this.matteElement=createNS("g"),this.transformedElement=this.layerElement,this.maskedElement=this.layerElement,this._sizeChanged=!1;var t=null;if(this.data.td){this.matteMasks={};var e=createNS("g");e.setAttribute("id",this.layerId),e.appendChild(this.layerElement),t=e,this.globalData.defs.appendChild(e)}else this.data.tt?(this.matteElement.appendChild(this.layerElement),t=this.matteElement,this.baseElement=this.matteElement):this.baseElement=this.layerElement;if(this.data.ln&&this.layerElement.setAttribute("id",this.data.ln),this.data.cl&&this.layerElement.setAttribute("class",this.data.cl),0===this.data.ty&&!this.data.hd){var i=createNS("clipPath"),r=createNS("path");r.setAttribute("d","M0,0 L"+this.data.w+",0 L"+this.data.w+","+this.data.h+" L0,"+this.data.h+"z");var s=createElementID();if(i.setAttribute("id",s),i.appendChild(r),this.globalData.defs.appendChild(i),this.checkMasks()){var n=createNS("g");n.setAttribute("clip-path","url("+getLocationHref()+"#"+s+")"),n.appendChild(this.layerElement),this.transformedElement=n,t?t.appendChild(this.transformedElement):this.baseElement=this.transformedElement}else this.layerElement.setAttribute("clip-path","url("+getLocationHref()+"#"+s+")")}0!==this.data.bm&&this.setBlendMode()},renderElement:function(){this.finalTransform._localMatMdf&&this.transformedElement.setAttribute("transform",this.finalTransform.localMat.to2dCSS()),this.finalTransform._opMdf&&this.transformedElement.setAttribute("opacity",this.finalTransform.localOpacity)},destroyBaseElement:function(){this.layerElement=null,this.matteElement=null,this.maskManager.destroy()},getBaseElement:function(){return this.data.hd?null:this.baseElement},createRenderableComponents:function(){this.maskManager=new MaskElement(this.data,this,this.globalData),this.renderableEffectsManager=new SVGEffects(this),this.searchEffectTransforms()},getMatte:function(t){if(this.matteMasks||(this.matteMasks={}),!this.matteMasks[t]){var e,i,r,s,n=this.layerId+"_"+t;if(1===t||3===t){var a=createNS("mask");a.setAttribute("id",n),a.setAttribute("mask-type",3===t?"luminance":"alpha"),(r=createNS("use")).setAttributeNS("http://www.w3.org/1999/xlink","href","#"+this.layerId),a.appendChild(r),this.globalData.defs.appendChild(a),featureSupport.maskType||1!==t||(a.setAttribute("mask-type","luminance"),e=createElementID(),i=filtersFactory.createFilter(e),this.globalData.defs.appendChild(i),i.appendChild(filtersFactory.createAlphaToLuminanceFilter()),(s=createNS("g")).appendChild(r),a.appendChild(s),s.setAttribute("filter","url("+getLocationHref()+"#"+e+")"))}else if(2===t){var o=createNS("mask");o.setAttribute("id",n),o.setAttribute("mask-type","alpha");var h=createNS("g");o.appendChild(h),e=createElementID(),i=filtersFactory.createFilter(e);var l=createNS("feComponentTransfer");l.setAttribute("in","SourceGraphic"),i.appendChild(l);var p=createNS("feFuncA");p.setAttribute("type","table"),p.setAttribute("tableValues","1.0 0.0"),l.appendChild(p),this.globalData.defs.appendChild(i);var f=createNS("rect");f.setAttribute("width",this.comp.data.w),f.setAttribute("height",this.comp.data.h),f.setAttribute("x","0"),f.setAttribute("y","0"),f.setAttribute("fill","#ffffff"),f.setAttribute("opacity","0"),h.setAttribute("filter","url("+getLocationHref()+"#"+e+")"),h.appendChild(f),(r=createNS("use")).setAttributeNS("http://www.w3.org/1999/xlink","href","#"+this.layerId),h.appendChild(r),featureSupport.maskType||(o.setAttribute("mask-type","luminance"),i.appendChild(filtersFactory.createAlphaToLuminanceFilter()),s=createNS("g"),h.appendChild(f),s.appendChild(this.layerElement),h.appendChild(s)),this.globalData.defs.appendChild(o)}this.matteMasks[t]=n}return this.matteMasks[t]},setMatte:function(t){this.matteElement&&this.matteElement.setAttribute("mask","url("+getLocationHref()+"#"+t+")")}},HierarchyElement.prototype={initHierarchy:function(){this.hierarchy=[],this._isParent=!1,this.checkParenting()},setHierarchy:function(t){this.hierarchy=t},setAsParent:function(){this._isParent=!0},checkParenting:function(){void 0!==this.data.parent&&this.comp.buildElementParenting(this,this.data.parent,[])}},extendPrototype([RenderableElement,createProxyFunction({initElement:function(t,e,i){this.initFrame(),this.initBaseData(t,e,i),this.initTransform(t,e,i),this.initHierarchy(),this.initRenderable(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),this.createContent(),this.hide()},hide:function(){this.hidden||this.isInRange&&!this.isTransparent||((this.baseElement||this.layerElement).style.display="none",this.hidden=!0)},show:function(){this.isInRange&&!this.isTransparent&&(this.data.hd||((this.baseElement||this.layerElement).style.display="block"),this.hidden=!1,this._isFirstFrame=!0)},renderFrame:function(){this.data.hd||this.hidden||(this.renderTransform(),this.renderRenderable(),this.renderLocalTransform(),this.renderElement(),this.renderInnerContent(),this._isFirstFrame&&(this._isFirstFrame=!1))},renderInnerContent:function(){},prepareFrame:function(t){this._mdf=!1,this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange),this.checkTransparency()},destroy:function(){this.innerElem=null,this.destroyBaseElement()}})],RenderableDOMElement),extendPrototype([BaseElement,TransformElement,SVGBaseElement,HierarchyElement,FrameElement,RenderableDOMElement],IImageElement),IImageElement.prototype.createContent=function(){var t=this.globalData.getAssetsPath(this.assetData);this.innerElem=createNS("image"),this.innerElem.setAttribute("width",this.assetData.w+"px"),this.innerElem.setAttribute("height",this.assetData.h+"px"),this.innerElem.setAttribute("preserveAspectRatio",this.assetData.pr||this.globalData.renderConfig.imagePreserveAspectRatio),this.innerElem.setAttributeNS("http://www.w3.org/1999/xlink","href",t),this.layerElement.appendChild(this.innerElem)},IImageElement.prototype.sourceRectAtTime=function(){return this.sourceRect},IShapeElement.prototype={addShapeToModifiers:function(t){var e,i=this.shapeModifiers.length;for(e=0;e<i;e+=1)this.shapeModifiers[e].addShape(t)},isShapeInAnimatedModifiers:function(t){for(var e=this.shapeModifiers.length;0<e;)if(this.shapeModifiers[0].isAnimatedWithShape(t))return!0;return!1},renderModifiers:function(){if(this.shapeModifiers.length){var t,e=this.shapes.length;for(t=0;t<e;t+=1)this.shapes[t].sh.reset();for(t=(e=this.shapeModifiers.length)-1;t>=0&&!this.shapeModifiers[t].processShapes(this._isFirstFrame);t-=1);}},searchProcessedElement:function(t){for(var e=this.processedElements,i=0,r=e.length;i<r;){if(e[i].elem===t)return e[i].pos;i+=1}return 0},addProcessedElement:function(t,e){for(var i=this.processedElements,r=i.length;r;)if(i[r-=1].elem===t)return void(i[r].pos=e);i.push(new ProcessedElement(t,e))},prepareFrame:function(t){this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange)}};var lineCapEnum={1:"butt",2:"round",3:"square"},lineJoinEnum={1:"miter",2:"round",3:"bevel"};function SVGShapeData(t,e,i){this.caches=[],this.styles=[],this.transformers=t,this.lStr="",this.sh=i,this.lvl=e,this._isAnimated=!!i.k;for(var r=0,s=t.length;r<s;){if(t[r].mProps.dynamicProperties.length){this._isAnimated=!0;break}r+=1}}function SVGStyleData(t,e){this.data=t,this.type=t.ty,this.d="",this.lvl=e,this._mdf=!1,this.closed=!0===t.hd,this.pElem=createNS("path"),this.msElem=null}function DashProperty(t,e,i,r){this.elem=t,this.frameId=-1,this.dataProps=createSizedArray(e.length),this.renderer=i,this.k=!1,this.dashStr="",this.dashArray=createTypedArray("float32",e.length?e.length-1:0),this.dashoffset=createTypedArray("float32",1),this.initDynamicPropertyContainer(r);var s,n,a=e.length||0;for(s=0;s<a;s+=1)n=PropertyFactory.getProp(t,e[s].v,0,0,this),this.k=n.k||this.k,this.dataProps[s]={n:e[s].n,p:n};this.k||this.getValue(!0),this._isAnimated=this.k}function SVGStrokeStyleData(t,e,i){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.o=PropertyFactory.getProp(t,e.o,0,.01,this),this.w=PropertyFactory.getProp(t,e.w,0,null,this),this.d=new DashProperty(t,e.d||{},"svg",this),this.c=PropertyFactory.getProp(t,e.c,1,255,this),this.style=i,this._isAnimated=!!this._isAnimated}function SVGFillStyleData(t,e,i){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.o=PropertyFactory.getProp(t,e.o,0,.01,this),this.c=PropertyFactory.getProp(t,e.c,1,255,this),this.style=i}function SVGNoStyleData(t,e,i){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.style=i}function GradientProperty(t,e,i){this.data=e,this.c=createTypedArray("uint8c",4*e.p);var r=e.k.k[0].s?e.k.k[0].s.length-4*e.p:e.k.k.length-4*e.p;this.o=createTypedArray("float32",r),this._cmdf=!1,this._omdf=!1,this._collapsable=this.checkCollapsable(),this._hasOpacity=r,this.initDynamicPropertyContainer(i),this.prop=PropertyFactory.getProp(t,e.k,1,null,this),this.k=this.prop.k,this.getValue(!0)}function SVGGradientFillStyleData(t,e,i){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.initGradientData(t,e,i)}function SVGGradientStrokeStyleData(t,e,i){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.w=PropertyFactory.getProp(t,e.w,0,null,this),this.d=new DashProperty(t,e.d||{},"svg",this),this.initGradientData(t,e,i),this._isAnimated=!!this._isAnimated}function ShapeGroupData(){this.it=[],this.prevViewData=[],this.gr=createNS("g")}function SVGTransformData(t,e,i){this.transform={mProps:t,op:e,container:i},this.elements=[],this._isAnimated=this.transform.mProps.dynamicProperties.length||this.transform.op.effectsSequence.length}SVGShapeData.prototype.setAsAnimated=function(){this._isAnimated=!0},SVGStyleData.prototype.reset=function(){this.d="",this._mdf=!1},DashProperty.prototype.getValue=function(t){if((this.elem.globalData.frameId!==this.frameId||t)&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf=this._mdf||t,this._mdf)){var e=0,i=this.dataProps.length;for("svg"===this.renderer&&(this.dashStr=""),e=0;e<i;e+=1)"o"!==this.dataProps[e].n?"svg"===this.renderer?this.dashStr+=" "+this.dataProps[e].p.v:this.dashArray[e]=this.dataProps[e].p.v:this.dashoffset[0]=this.dataProps[e].p.v}},extendPrototype([DynamicPropertyContainer],DashProperty),extendPrototype([DynamicPropertyContainer],SVGStrokeStyleData),extendPrototype([DynamicPropertyContainer],SVGFillStyleData),extendPrototype([DynamicPropertyContainer],SVGNoStyleData),GradientProperty.prototype.comparePoints=function(t,e){for(var i=0,r=this.o.length/2;i<r;){if(Math.abs(t[4*i]-t[4*e+2*i])>.01)return!1;i+=1}return!0},GradientProperty.prototype.checkCollapsable=function(){if(this.o.length/2!=this.c.length/4)return!1;if(this.data.k.k[0].s)for(var t=0,e=this.data.k.k.length;t<e;){if(!this.comparePoints(this.data.k.k[t].s,this.data.p))return!1;t+=1}else if(!this.comparePoints(this.data.k.k,this.data.p))return!1;return!0},GradientProperty.prototype.getValue=function(t){if(this.prop.getValue(),this._mdf=!1,this._cmdf=!1,this._omdf=!1,this.prop._mdf||t){var e,i,r,s=4*this.data.p;for(e=0;e<s;e+=1)i=e%4==0?100:255,r=Math.round(this.prop.v[e]*i),this.c[e]!==r&&(this.c[e]=r,this._cmdf=!t);if(this.o.length)for(s=this.prop.v.length,e=4*this.data.p;e<s;e+=1)i=e%2==0?100:1,r=e%2==0?Math.round(100*this.prop.v[e]):this.prop.v[e],this.o[e-4*this.data.p]!==r&&(this.o[e-4*this.data.p]=r,this._omdf=!t);this._mdf=!t}},extendPrototype([DynamicPropertyContainer],GradientProperty),SVGGradientFillStyleData.prototype.initGradientData=function(t,e,i){this.o=PropertyFactory.getProp(t,e.o,0,.01,this),this.s=PropertyFactory.getProp(t,e.s,1,null,this),this.e=PropertyFactory.getProp(t,e.e,1,null,this),this.h=PropertyFactory.getProp(t,e.h||{k:0},0,.01,this),this.a=PropertyFactory.getProp(t,e.a||{k:0},0,degToRads,this),this.g=new GradientProperty(t,e.g,this),this.style=i,this.stops=[],this.setGradientData(i.pElem,e),this.setGradientOpacity(e,i),this._isAnimated=!!this._isAnimated},SVGGradientFillStyleData.prototype.setGradientData=function(t,e){var i=createElementID(),r=createNS(1===e.t?"linearGradient":"radialGradient");r.setAttribute("id",i),r.setAttribute("spreadMethod","pad"),r.setAttribute("gradientUnits","userSpaceOnUse");var s,n,a,o=[];for(a=4*e.g.p,n=0;n<a;n+=4)s=createNS("stop"),r.appendChild(s),o.push(s);t.setAttribute("gf"===e.ty?"fill":"stroke","url("+getLocationHref()+"#"+i+")"),this.gf=r,this.cst=o},SVGGradientFillStyleData.prototype.setGradientOpacity=function(t,e){if(this.g._hasOpacity&&!this.g._collapsable){var i,r,s,n=createNS("mask"),a=createNS("path");n.appendChild(a);var o=createElementID(),h=createElementID();n.setAttribute("id",h);var l=createNS(1===t.t?"linearGradient":"radialGradient");l.setAttribute("id",o),l.setAttribute("spreadMethod","pad"),l.setAttribute("gradientUnits","userSpaceOnUse"),s=t.g.k.k[0].s?t.g.k.k[0].s.length:t.g.k.k.length;var p=this.stops;for(r=4*t.g.p;r<s;r+=2)(i=createNS("stop")).setAttribute("stop-color","rgb(255,255,255)"),l.appendChild(i),p.push(i);a.setAttribute("gf"===t.ty?"fill":"stroke","url("+getLocationHref()+"#"+o+")"),"gs"===t.ty&&(a.setAttribute("stroke-linecap",lineCapEnum[t.lc||2]),a.setAttribute("stroke-linejoin",lineJoinEnum[t.lj||2]),1===t.lj&&a.setAttribute("stroke-miterlimit",t.ml)),this.of=l,this.ms=n,this.ost=p,this.maskId=h,e.msElem=a}},extendPrototype([DynamicPropertyContainer],SVGGradientFillStyleData),extendPrototype([SVGGradientFillStyleData,DynamicPropertyContainer],SVGGradientStrokeStyleData);var buildShapeString=function(t,e,i,r){if(0===e)return"";var s,n=t.o,a=t.i,o=t.v,h=" M"+r.applyToPointStringified(o[0][0],o[0][1]);for(s=1;s<e;s+=1)h+=" C"+r.applyToPointStringified(n[s-1][0],n[s-1][1])+" "+r.applyToPointStringified(a[s][0],a[s][1])+" "+r.applyToPointStringified(o[s][0],o[s][1]);return i&&e&&(h+=" C"+r.applyToPointStringified(n[s-1][0],n[s-1][1])+" "+r.applyToPointStringified(a[0][0],a[0][1])+" "+r.applyToPointStringified(o[0][0],o[0][1])+"z"),h},SVGElementsRenderer=function(){var t=new Matrix,e=new Matrix;function i(t,e,i){(i||e.transform.op._mdf)&&e.transform.container.setAttribute("opacity",e.transform.op.v),(i||e.transform.mProps._mdf)&&e.transform.container.setAttribute("transform",e.transform.mProps.v.to2dCSS())}function r(){}function s(i,r,s){var n,a,o,h,l,p,f,u,c,m,d=r.styles.length,g=r.lvl;for(p=0;p<d;p+=1){if(h=r.sh._mdf||s,r.styles[p].lvl<g){for(u=e.reset(),c=g-r.styles[p].lvl,m=r.transformers.length-1;!h&&c>0;)h=r.transformers[m].mProps._mdf||h,c-=1,m-=1;if(h)for(c=g-r.styles[p].lvl,m=r.transformers.length-1;c>0;)u.multiply(r.transformers[m].mProps.v),c-=1,m-=1}else u=t;if(a=(f=r.sh.paths)._length,h){for(o="",n=0;n<a;n+=1)(l=f.shapes[n])&&l._length&&(o+=buildShapeString(l,l._length,l.c,u));r.caches[p]=o}else o=r.caches[p];r.styles[p].d+=!0===i.hd?"":o,r.styles[p]._mdf=h||r.styles[p]._mdf}}function n(t,e,i){var r=e.style;(e.c._mdf||i)&&r.pElem.setAttribute("fill","rgb("+bmFloor(e.c.v[0])+","+bmFloor(e.c.v[1])+","+bmFloor(e.c.v[2])+")"),(e.o._mdf||i)&&r.pElem.setAttribute("fill-opacity",e.o.v)}function a(t,e,i){o(t,e,i),h(t,e,i)}function o(t,e,i){var r,s,n,a,o,h=e.gf,l=e.g._hasOpacity,p=e.s.v,f=e.e.v;if(e.o._mdf||i){var u="gf"===t.ty?"fill-opacity":"stroke-opacity";e.style.pElem.setAttribute(u,e.o.v)}if(e.s._mdf||i){var c=1===t.t?"x1":"cx",m="x1"===c?"y1":"cy";h.setAttribute(c,p[0]),h.setAttribute(m,p[1]),l&&!e.g._collapsable&&(e.of.setAttribute(c,p[0]),e.of.setAttribute(m,p[1]))}if(e.g._cmdf||i){r=e.cst;var d=e.g.c;for(n=r.length,s=0;s<n;s+=1)(a=r[s]).setAttribute("offset",d[4*s]+"%"),a.setAttribute("stop-color","rgb("+d[4*s+1]+","+d[4*s+2]+","+d[4*s+3]+")")}if(l&&(e.g._omdf||i)){var g=e.g.o;for(n=(r=e.g._collapsable?e.cst:e.ost).length,s=0;s<n;s+=1)a=r[s],e.g._collapsable||a.setAttribute("offset",g[2*s]+"%"),a.setAttribute("stop-opacity",g[2*s+1])}if(1===t.t)(e.e._mdf||i)&&(h.setAttribute("x2",f[0]),h.setAttribute("y2",f[1]),l&&!e.g._collapsable&&(e.of.setAttribute("x2",f[0]),e.of.setAttribute("y2",f[1])));else if((e.s._mdf||e.e._mdf||i)&&(o=Math.sqrt(Math.pow(p[0]-f[0],2)+Math.pow(p[1]-f[1],2)),h.setAttribute("r",o),l&&!e.g._collapsable&&e.of.setAttribute("r",o)),e.e._mdf||e.h._mdf||e.a._mdf||i){o||(o=Math.sqrt(Math.pow(p[0]-f[0],2)+Math.pow(p[1]-f[1],2)));var v=Math.atan2(f[1]-p[1],f[0]-p[0]),y=e.h.v;y>=1?y=.99:y<=-1&&(y=-.99);var b=o*y,x=Math.cos(v+e.a.v)*b+p[0],_=Math.sin(v+e.a.v)*b+p[1];h.setAttribute("fx",x),h.setAttribute("fy",_),l&&!e.g._collapsable&&(e.of.setAttribute("fx",x),e.of.setAttribute("fy",_))}}function h(t,e,i){var r=e.style,s=e.d;s&&(s._mdf||i)&&s.dashStr&&(r.pElem.setAttribute("stroke-dasharray",s.dashStr),r.pElem.setAttribute("stroke-dashoffset",s.dashoffset[0])),e.c&&(e.c._mdf||i)&&r.pElem.setAttribute("stroke","rgb("+bmFloor(e.c.v[0])+","+bmFloor(e.c.v[1])+","+bmFloor(e.c.v[2])+")"),(e.o._mdf||i)&&r.pElem.setAttribute("stroke-opacity",e.o.v),(e.w._mdf||i)&&(r.pElem.setAttribute("stroke-width",e.w.v),r.msElem&&r.msElem.setAttribute("stroke-width",e.w.v))}return{createRenderFunction:function(t){switch(t.ty){case"fl":return n;case"gf":return o;case"gs":return a;case"st":return h;case"sh":case"el":case"rc":case"sr":return s;case"tr":return i;case"no":return r;default:return null}}}}();function SVGShapeElement(t,e,i){this.shapes=[],this.shapesData=t.shapes,this.stylesList=[],this.shapeModifiers=[],this.itemsData=[],this.processedElements=[],this.animatedContents=[],this.initElement(t,e,i),this.prevViewData=[]}function LetterProps(t,e,i,r,s,n){this.o=t,this.sw=e,this.sc=i,this.fc=r,this.m=s,this.p=n,this._mdf={o:!0,sw:!!e,sc:!!i,fc:!!r,m:!0,p:!0}}function TextProperty(t,e){this._frameId=initialDefaultFrame,this.pv="",this.v="",this.kf=!1,this._isFirstFrame=!0,this._mdf=!1,e.d&&e.d.sid&&(e.d=t.globalData.slotManager.getProp(e.d)),this.data=e,this.elem=t,this.comp=this.elem.comp,this.keysIndex=0,this.canResize=!1,this.minimumFontSize=1,this.effectsSequence=[],this.currentData={ascent:0,boxWidth:this.defaultBoxWidth,f:"",fStyle:"",fWeight:"",fc:"",j:"",justifyOffset:"",l:[],lh:0,lineWidths:[],ls:"",of:"",s:"",sc:"",sw:0,t:0,tr:0,sz:0,ps:null,fillColorAnim:!1,strokeColorAnim:!1,strokeWidthAnim:!1,yOffset:0,finalSize:0,finalText:[],finalLineHeight:0,__complete:!1},this.copyData(this.currentData,this.data.d.k[0].s),this.searchProperty()||this.completeTextData(this.currentData)}extendPrototype([BaseElement,TransformElement,SVGBaseElement,IShapeElement,HierarchyElement,FrameElement,RenderableDOMElement],SVGShapeElement),SVGShapeElement.prototype.initSecondaryElement=function(){},SVGShapeElement.prototype.identityMatrix=new Matrix,SVGShapeElement.prototype.buildExpressionInterface=function(){},SVGShapeElement.prototype.createContent=function(){this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.layerElement,0,[],!0),this.filterUniqueShapes()},SVGShapeElement.prototype.filterUniqueShapes=function(){var t,e,i,r,s=this.shapes.length,n=this.stylesList.length,a=[],o=!1;for(i=0;i<n;i+=1){for(r=this.stylesList[i],o=!1,a.length=0,t=0;t<s;t+=1)-1!==(e=this.shapes[t]).styles.indexOf(r)&&(a.push(e),o=e._isAnimated||o);a.length>1&&o&&this.setShapesAsAnimated(a)}},SVGShapeElement.prototype.setShapesAsAnimated=function(t){var e,i=t.length;for(e=0;e<i;e+=1)t[e].setAsAnimated()},SVGShapeElement.prototype.createStyleElement=function(t,e){var i,r=new SVGStyleData(t,e),s=r.pElem;return"st"===t.ty?i=new SVGStrokeStyleData(this,t,r):"fl"===t.ty?i=new SVGFillStyleData(this,t,r):"gf"===t.ty||"gs"===t.ty?(i=new("gf"===t.ty?SVGGradientFillStyleData:SVGGradientStrokeStyleData)(this,t,r),this.globalData.defs.appendChild(i.gf),i.maskId&&(this.globalData.defs.appendChild(i.ms),this.globalData.defs.appendChild(i.of),s.setAttribute("mask","url("+getLocationHref()+"#"+i.maskId+")"))):"no"===t.ty&&(i=new SVGNoStyleData(this,t,r)),"st"!==t.ty&&"gs"!==t.ty||(s.setAttribute("stroke-linecap",lineCapEnum[t.lc||2]),s.setAttribute("stroke-linejoin",lineJoinEnum[t.lj||2]),s.setAttribute("fill-opacity","0"),1===t.lj&&s.setAttribute("stroke-miterlimit",t.ml)),2===t.r&&s.setAttribute("fill-rule","evenodd"),t.ln&&s.setAttribute("id",t.ln),t.cl&&s.setAttribute("class",t.cl),t.bm&&(s.style["mix-blend-mode"]=getBlendMode(t.bm)),this.stylesList.push(r),this.addToAnimatedContents(t,i),i},SVGShapeElement.prototype.createGroupElement=function(t){var e=new ShapeGroupData;return t.ln&&e.gr.setAttribute("id",t.ln),t.cl&&e.gr.setAttribute("class",t.cl),t.bm&&(e.gr.style["mix-blend-mode"]=getBlendMode(t.bm)),e},SVGShapeElement.prototype.createTransformElement=function(t,e){var i=TransformPropertyFactory.getTransformProperty(this,t,this),r=new SVGTransformData(i,i.o,e);return this.addToAnimatedContents(t,r),r},SVGShapeElement.prototype.createShapeElement=function(t,e,i){var r=4;"rc"===t.ty?r=5:"el"===t.ty?r=6:"sr"===t.ty&&(r=7);var s=new SVGShapeData(e,i,ShapePropertyFactory.getShapeProp(this,t,r,this));return this.shapes.push(s),this.addShapeToModifiers(s),this.addToAnimatedContents(t,s),s},SVGShapeElement.prototype.addToAnimatedContents=function(t,e){for(var i=0,r=this.animatedContents.length;i<r;){if(this.animatedContents[i].element===e)return;i+=1}this.animatedContents.push({fn:SVGElementsRenderer.createRenderFunction(t),element:e,data:t})},SVGShapeElement.prototype.setElementStyles=function(t){var e,i=t.styles,r=this.stylesList.length;for(e=0;e<r;e+=1)this.stylesList[e].closed||i.push(this.stylesList[e])},SVGShapeElement.prototype.reloadShapes=function(){this._isFirstFrame=!0;var t,e=this.itemsData.length;for(t=0;t<e;t+=1)this.prevViewData[t]=this.itemsData[t];for(this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.layerElement,0,[],!0),this.filterUniqueShapes(),e=this.dynamicProperties.length,t=0;t<e;t+=1)this.dynamicProperties[t].getValue();this.renderModifiers()},SVGShapeElement.prototype.searchShapes=function(t,e,i,r,s,n,a){var o,h,l,p,f,u,c=[].concat(n),m=t.length-1,d=[],g=[];for(o=m;o>=0;o-=1){if((u=this.searchProcessedElement(t[o]))?e[o]=i[u-1]:t[o]._render=a,"fl"===t[o].ty||"st"===t[o].ty||"gf"===t[o].ty||"gs"===t[o].ty||"no"===t[o].ty)u?e[o].style.closed=!1:e[o]=this.createStyleElement(t[o],s),t[o]._render&&e[o].style.pElem.parentNode!==r&&r.appendChild(e[o].style.pElem),d.push(e[o].style);else if("gr"===t[o].ty){if(u)for(l=e[o].it.length,h=0;h<l;h+=1)e[o].prevViewData[h]=e[o].it[h];else e[o]=this.createGroupElement(t[o]);this.searchShapes(t[o].it,e[o].it,e[o].prevViewData,e[o].gr,s+1,c,a),t[o]._render&&e[o].gr.parentNode!==r&&r.appendChild(e[o].gr)}else"tr"===t[o].ty?(u||(e[o]=this.createTransformElement(t[o],r)),p=e[o].transform,c.push(p)):"sh"===t[o].ty||"rc"===t[o].ty||"el"===t[o].ty||"sr"===t[o].ty?(u||(e[o]=this.createShapeElement(t[o],c,s)),this.setElementStyles(e[o])):"tm"===t[o].ty||"rd"===t[o].ty||"ms"===t[o].ty||"pb"===t[o].ty||"zz"===t[o].ty||"op"===t[o].ty?(u?(f=e[o]).closed=!1:((f=ShapeModifiers.getModifier(t[o].ty)).init(this,t[o]),e[o]=f,this.shapeModifiers.push(f)),g.push(f)):"rp"===t[o].ty&&(u?(f=e[o]).closed=!0:(f=ShapeModifiers.getModifier(t[o].ty),e[o]=f,f.init(this,t,o,e),this.shapeModifiers.push(f),a=!1),g.push(f));this.addProcessedElement(t[o],o+1)}for(m=d.length,o=0;o<m;o+=1)d[o].closed=!0;for(m=g.length,o=0;o<m;o+=1)g[o].closed=!0},SVGShapeElement.prototype.renderInnerContent=function(){this.renderModifiers();var t,e=this.stylesList.length;for(t=0;t<e;t+=1)this.stylesList[t].reset();for(this.renderShape(),t=0;t<e;t+=1)(this.stylesList[t]._mdf||this._isFirstFrame)&&(this.stylesList[t].msElem&&(this.stylesList[t].msElem.setAttribute("d",this.stylesList[t].d),this.stylesList[t].d="M0 0"+this.stylesList[t].d),this.stylesList[t].pElem.setAttribute("d",this.stylesList[t].d||"M0 0"))},SVGShapeElement.prototype.renderShape=function(){var t,e,i=this.animatedContents.length;for(t=0;t<i;t+=1)e=this.animatedContents[t],(this._isFirstFrame||e.element._isAnimated)&&!0!==e.data&&e.fn(e.data,e.element,this._isFirstFrame)},SVGShapeElement.prototype.destroy=function(){this.destroyBaseElement(),this.shapesData=null,this.itemsData=null},LetterProps.prototype.update=function(t,e,i,r,s,n){this._mdf.o=!1,this._mdf.sw=!1,this._mdf.sc=!1,this._mdf.fc=!1,this._mdf.m=!1,this._mdf.p=!1;var a=!1;return this.o!==t&&(this.o=t,this._mdf.o=!0,a=!0),this.sw!==e&&(this.sw=e,this._mdf.sw=!0,a=!0),this.sc!==i&&(this.sc=i,this._mdf.sc=!0,a=!0),this.fc!==r&&(this.fc=r,this._mdf.fc=!0,a=!0),this.m!==s&&(this.m=s,this._mdf.m=!0,a=!0),n.length&&(this.p[0]!==n[0]||this.p[1]!==n[1]||this.p[4]!==n[4]||this.p[5]!==n[5]||this.p[12]!==n[12]||this.p[13]!==n[13])&&(this.p=n,this._mdf.p=!0,a=!0),a},TextProperty.prototype.defaultBoxWidth=[0,0],TextProperty.prototype.copyData=function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},TextProperty.prototype.setCurrentData=function(t){t.__complete||this.completeTextData(t),this.currentData=t,this.currentData.boxWidth=this.currentData.boxWidth||this.defaultBoxWidth,this._mdf=!0},TextProperty.prototype.searchProperty=function(){return this.searchKeyframes()},TextProperty.prototype.searchKeyframes=function(){return this.kf=this.data.d.k.length>1,this.kf&&this.addEffect(this.getKeyframeValue.bind(this)),this.kf},TextProperty.prototype.addEffect=function(t){this.effectsSequence.push(t),this.elem.addDynamicProperty(this)},TextProperty.prototype.getValue=function(t){if(this.elem.globalData.frameId!==this.frameId&&this.effectsSequence.length||t){this.currentData.t=this.data.d.k[this.keysIndex].s.t;var e=this.currentData,i=this.keysIndex;if(this.lock)this.setCurrentData(this.currentData);else{this.lock=!0,this._mdf=!1;var r,s=this.effectsSequence.length,n=t||this.data.d.k[this.keysIndex].s;for(r=0;r<s;r+=1)n=i!==this.keysIndex?this.effectsSequence[r](n,n.t):this.effectsSequence[r](this.currentData,n.t);e!==n&&this.setCurrentData(n),this.v=this.currentData,this.pv=this.v,this.lock=!1,this.frameId=this.elem.globalData.frameId}}},TextProperty.prototype.getKeyframeValue=function(){for(var t=this.data.d.k,e=this.elem.comp.renderedFrame,i=0,r=t.length;i<=r-1&&!(i===r-1||t[i+1].t>e);)i+=1;return this.keysIndex!==i&&(this.keysIndex=i),this.data.d.k[this.keysIndex].s},TextProperty.prototype.buildFinalText=function(t){for(var e,i,r=[],s=0,n=t.length,a=!1,o=!1,h="";s<n;)a=o,o=!1,e=t.charCodeAt(s),h=t.charAt(s),FontManager.isCombinedCharacter(e)?a=!0:e>=55296&&e<=56319?FontManager.isRegionalFlag(t,s)?h=t.substr(s,14):(i=t.charCodeAt(s+1))>=56320&&i<=57343&&(FontManager.isModifier(e,i)?(h=t.substr(s,2),a=!0):h=FontManager.isFlagEmoji(t.substr(s,4))?t.substr(s,4):t.substr(s,2)):e>56319?(i=t.charCodeAt(s+1),FontManager.isVariationSelector(e)&&(a=!0)):FontManager.isZeroWidthJoiner(e)&&(a=!0,o=!0),a?(r[r.length-1]+=h,a=!1):r.push(h),s+=h.length;return r},TextProperty.prototype.completeTextData=function(t){t.__complete=!0;var e,i,r,s,n,a,o,h=this.elem.globalData.fontManager,l=this.data,p=[],f=0,u=l.m.g,c=0,m=0,d=0,g=[],v=0,y=0,b=h.getFontByName(t.f),x=0,_=getFontProperties(b);t.fWeight=_.weight,t.fStyle=_.style,t.finalSize=t.s,t.finalText=this.buildFinalText(t.t),i=t.finalText.length,t.finalLineHeight=t.lh;var A,k=t.tr/1e3*t.finalSize;if(t.sz)for(var w,P,C=!0,S=t.sz[0],D=t.sz[1];C;){w=0,v=0,i=(P=this.buildFinalText(t.t)).length,k=t.tr/1e3*t.finalSize;var T=-1;for(e=0;e<i;e+=1)A=P[e].charCodeAt(0),r=!1," "===P[e]?T=e:13!==A&&3!==A||(v=0,r=!0,w+=t.finalLineHeight||1.2*t.finalSize),h.chars?(o=h.getCharData(P[e],b.fStyle,b.fFamily),x=r?0:o.w*t.finalSize/100):x=h.measureText(P[e],t.f,t.finalSize),v+x>S&&" "!==P[e]?(-1===T?i+=1:e=T,w+=t.finalLineHeight||1.2*t.finalSize,P.splice(e,+(T===e),"\r"),T=-1,v=0):(v+=x,v+=k);w+=b.ascent*t.finalSize/100,this.canResize&&t.finalSize>this.minimumFontSize&&D<w?(t.finalSize-=1,t.finalLineHeight=t.finalSize*t.lh/t.s):(t.finalText=P,i=t.finalText.length,C=!1)}v=-k,x=0;var E,M=0;for(e=0;e<i;e+=1)if(r=!1,13===(A=(E=t.finalText[e]).charCodeAt(0))||3===A?(M=0,g.push(v),y=v>y?v:y,v=-2*k,s="",r=!0,d+=1):s=E,h.chars?(o=h.getCharData(E,b.fStyle,h.getFontByName(t.f).fFamily),x=r?0:o.w*t.finalSize/100):x=h.measureText(s,t.f,t.finalSize)," "===E?M+=x+k:(v+=x+k+M,M=0),p.push({l:x,an:x,add:c,n:r,anIndexes:[],val:s,line:d,animatorJustifyOffset:0}),2==u){if(c+=x,""===s||" "===s||e===i-1){for(""!==s&&" "!==s||(c-=x);m<=e;)p[m].an=c,p[m].ind=f,p[m].extra=x,m+=1;f+=1,c=0}}else if(3==u){if(c+=x,""===s||e===i-1){for(""===s&&(c-=x);m<=e;)p[m].an=c,p[m].ind=f,p[m].extra=x,m+=1;c=0,f+=1}}else p[f].ind=f,p[f].extra=0,f+=1;if(t.l=p,y=v>y?v:y,g.push(v),t.sz)t.boxWidth=t.sz[0],t.justifyOffset=0;else switch(t.boxWidth=y,t.j){case 1:t.justifyOffset=-t.boxWidth;break;case 2:t.justifyOffset=-t.boxWidth/2;break;default:t.justifyOffset=0}t.lineWidths=g;var F,I,L,O,j=l.a;a=j.length;var B=[];for(n=0;n<a;n+=1){for((F=j[n]).a.sc&&(t.strokeColorAnim=!0),F.a.sw&&(t.strokeWidthAnim=!0),(F.a.fc||F.a.fh||F.a.fs||F.a.fb)&&(t.fillColorAnim=!0),O=0,L=F.s.b,e=0;e<i;e+=1)(I=p[e]).anIndexes[n]=O,(1==L&&""!==I.val||2==L&&""!==I.val&&" "!==I.val||3==L&&(I.n||" "==I.val||e==i-1)||4==L&&(I.n||e==i-1))&&(1===F.s.rn&&B.push(O),O+=1);l.a[n].s.totalChars=O;var R,z=-1;if(1===F.s.rn)for(e=0;e<i;e+=1)z!=(I=p[e]).anIndexes[n]&&(z=I.anIndexes[n],R=B.splice(Math.floor(Math.random()*B.length),1)[0]),I.anIndexes[n]=R}t.yOffset=t.finalLineHeight||1.2*t.finalSize,t.ls=t.ls||0,t.ascent=b.ascent*t.finalSize/100},TextProperty.prototype.updateDocumentData=function(t,e){e=void 0===e?this.keysIndex:e;var i=this.copyData({},this.data.d.k[e].s);i=this.copyData(i,t),this.data.d.k[e].s=i,this.recalculate(e),this.setCurrentData(i),this.elem.addDynamicProperty(this)},TextProperty.prototype.recalculate=function(t){var e=this.data.d.k[t].s;e.__complete=!1,this.keysIndex=0,this._isFirstFrame=!0,this.getValue(e)},TextProperty.prototype.canResizeFont=function(t){this.canResize=t,this.recalculate(this.keysIndex),this.elem.addDynamicProperty(this)},TextProperty.prototype.setMinimumFontSize=function(t){this.minimumFontSize=Math.floor(t)||1,this.recalculate(this.keysIndex),this.elem.addDynamicProperty(this)};var TextSelectorProp=function(){var t=Math.max,e=Math.min,i=Math.floor;function r(t,e){this._currentTextLength=-1,this.k=!1,this.data=e,this.elem=t,this.comp=t.comp,this.finalS=0,this.finalE=0,this.initDynamicPropertyContainer(t),this.s=PropertyFactory.getProp(t,e.s||{k:0},0,0,this),this.e="e"in e?PropertyFactory.getProp(t,e.e,0,0,this):{v:100},this.o=PropertyFactory.getProp(t,e.o||{k:0},0,0,this),this.xe=PropertyFactory.getProp(t,e.xe||{k:0},0,0,this),this.ne=PropertyFactory.getProp(t,e.ne||{k:0},0,0,this),this.sm=PropertyFactory.getProp(t,e.sm||{k:100},0,0,this),this.a=PropertyFactory.getProp(t,e.a,0,.01,this),this.dynamicProperties.length||this.getValue()}return r.prototype={getMult:function(r){this._currentTextLength!==this.elem.textProperty.currentData.l.length&&this.getValue();var s=0,n=0,a=1,o=1;this.ne.v>0?s=this.ne.v/100:n=-this.ne.v/100,this.xe.v>0?a=1-this.xe.v/100:o=1+this.xe.v/100;var h=BezierFactory.getBezierEasing(s,n,a,o).get,l=0,p=this.finalS,f=this.finalE,u=this.data.sh;if(2===u)l=h(l=f===p?+(r>=f):t(0,e(.5/(f-p)+(r-p)/(f-p),1)));else if(3===u)l=h(l=f===p?r>=f?0:1:1-t(0,e(.5/(f-p)+(r-p)/(f-p),1)));else if(4===u)f===p?l=0:(l=t(0,e(.5/(f-p)+(r-p)/(f-p),1)))<.5?l*=2:l=1-2*(l-.5),l=h(l);else if(5===u){if(f===p)l=0;else{var c=f-p,m=-c/2+(r=e(t(0,r+.5-p),f-p)),d=c/2;l=Math.sqrt(1-m*m/(d*d))}l=h(l)}else 6===u?l=h(l=f===p?0:(1+Math.cos(Math.PI+2*Math.PI*(r=e(t(0,r+.5-p),f-p))/(f-p)))/2):(r>=i(p)&&(l=t(0,e(r-p<0?e(f,1)-(p-r):f-r,1))),l=h(l));if(100!==this.sm.v){var g=.01*this.sm.v;0===g&&(g=1e-8);var v=.5-.5*g;l<v?l=0:(l=(l-v)/g)>1&&(l=1)}return l*this.a.v},getValue:function(t){this.iterateDynamicProperties(),this._mdf=t||this._mdf,this._currentTextLength=this.elem.textProperty.currentData.l.length||0,t&&2===this.data.r&&(this.e.v=this._currentTextLength);var e=2===this.data.r?1:100/this.data.totalChars,i=this.o.v/e,r=this.s.v/e+i,s=this.e.v/e+i;if(r>s){var n=r;r=s,s=n}this.finalS=r,this.finalE=s}},extendPrototype([DynamicPropertyContainer],r),{getTextSelectorProp:function(t,e,i){return new r(t,e,i)}}}();function TextAnimatorDataProperty(t,e,i){var r={propType:!1},s=PropertyFactory.getProp,n=e.a;this.a={r:n.r?s(t,n.r,0,degToRads,i):r,rx:n.rx?s(t,n.rx,0,degToRads,i):r,ry:n.ry?s(t,n.ry,0,degToRads,i):r,sk:n.sk?s(t,n.sk,0,degToRads,i):r,sa:n.sa?s(t,n.sa,0,degToRads,i):r,s:n.s?s(t,n.s,1,.01,i):r,a:n.a?s(t,n.a,1,0,i):r,o:n.o?s(t,n.o,0,.01,i):r,p:n.p?s(t,n.p,1,0,i):r,sw:n.sw?s(t,n.sw,0,0,i):r,sc:n.sc?s(t,n.sc,1,0,i):r,fc:n.fc?s(t,n.fc,1,0,i):r,fh:n.fh?s(t,n.fh,0,0,i):r,fs:n.fs?s(t,n.fs,0,.01,i):r,fb:n.fb?s(t,n.fb,0,.01,i):r,t:n.t?s(t,n.t,0,0,i):r},this.s=TextSelectorProp.getTextSelectorProp(t,e.s,i),this.s.t=e.s.t}function TextAnimatorProperty(t,e,i){this._isFirstFrame=!0,this._hasMaskedPath=!1,this._frameId=-1,this._textData=t,this._renderType=e,this._elem=i,this._animatorsData=createSizedArray(this._textData.a.length),this._pathData={},this._moreOptions={alignment:{}},this.renderedLetters=[],this.lettersChangedFlag=!1,this.initDynamicPropertyContainer(i)}function ITextElement(){}TextAnimatorProperty.prototype.searchProperties=function(){var t,e,i=this._textData.a.length,r=PropertyFactory.getProp;for(t=0;t<i;t+=1)e=this._textData.a[t],this._animatorsData[t]=new TextAnimatorDataProperty(this._elem,e,this);this._textData.p&&"m"in this._textData.p?(this._pathData={a:r(this._elem,this._textData.p.a,0,0,this),f:r(this._elem,this._textData.p.f,0,0,this),l:r(this._elem,this._textData.p.l,0,0,this),r:r(this._elem,this._textData.p.r,0,0,this),p:r(this._elem,this._textData.p.p,0,0,this),m:this._elem.maskManager.getMaskProperty(this._textData.p.m)},this._hasMaskedPath=!0):this._hasMaskedPath=!1,this._moreOptions.alignment=r(this._elem,this._textData.m.a,1,0,this)},TextAnimatorProperty.prototype.getMeasures=function(t,e){if(this.lettersChangedFlag=e,this._mdf||this._isFirstFrame||e||this._hasMaskedPath&&this._pathData.m._mdf){this._isFirstFrame=!1;var i,r,s,n,a,o,h,l,p,f,u,c,m,d,g,v,y,b,x,_=this._moreOptions.alignment.v,A=this._animatorsData,k=this._textData,w=this.mHelper,P=this._renderType,C=this.renderedLetters.length,S=t.l;if(this._hasMaskedPath){if(x=this._pathData.m,!this._pathData.n||this._pathData._mdf){var D,T=x.v;for(this._pathData.r.v&&(T=T.reverse()),a={tLength:0,segments:[]},n=T._length-1,v=0,s=0;s<n;s+=1)D=bez.buildBezierData(T.v[s],T.v[s+1],[T.o[s][0]-T.v[s][0],T.o[s][1]-T.v[s][1]],[T.i[s+1][0]-T.v[s+1][0],T.i[s+1][1]-T.v[s+1][1]]),a.tLength+=D.segmentLength,a.segments.push(D),v+=D.segmentLength;s=n,x.v.c&&(D=bez.buildBezierData(T.v[s],T.v[0],[T.o[s][0]-T.v[s][0],T.o[s][1]-T.v[s][1]],[T.i[0][0]-T.v[0][0],T.i[0][1]-T.v[0][1]]),a.tLength+=D.segmentLength,a.segments.push(D),v+=D.segmentLength),this._pathData.pi=a}if(a=this._pathData.pi,o=this._pathData.f.v,u=0,f=1,l=0,p=!0,d=a.segments,o<0&&x.v.c)for(a.tLength<Math.abs(o)&&(o=-Math.abs(o)%a.tLength),f=(m=d[u=d.length-1].points).length-1;o<0;)o+=m[f].partialLength,(f-=1)<0&&(f=(m=d[u-=1].points).length-1);c=(m=d[u].points)[f-1],g=(h=m[f]).partialLength}n=S.length,i=0,r=0;var E,M,F,I,L,O=1.2*t.finalSize*.714,j=!0;F=A.length;var B,R,z,V,N,G,q,W,Y,H,X,J,U=-1,K=o,Z=u,$=f,Q=-1,tt="",te=this.defaultPropsArray;if(2===t.j||1===t.j){var ti=0,tr=0,ts=2===t.j?-.5:-1,tn=0,ta=!0;for(s=0;s<n;s+=1)if(S[s].n){for(ti&&(ti+=tr);tn<s;)S[tn].animatorJustifyOffset=ti,tn+=1;ti=0,ta=!0}else{for(M=0;M<F;M+=1)(E=A[M].a).t.propType&&(ta&&2===t.j&&(tr+=E.t.v*ts),(L=A[M].s.getMult(S[s].anIndexes[M],k.a[M].s.totalChars)).length?ti+=E.t.v*L[0]*ts:ti+=E.t.v*L*ts);ta=!1}for(ti&&(ti+=tr);tn<s;)S[tn].animatorJustifyOffset=ti,tn+=1}for(s=0;s<n;s+=1){if(w.reset(),V=1,S[s].n)i=0,r+=t.yOffset,r+=+!!j,o=K,j=!1,this._hasMaskedPath&&(f=$,c=(m=d[u=Z].points)[f-1],g=(h=m[f]).partialLength,l=0),tt="",X="",Y="",J="",te=this.defaultPropsArray;else{if(this._hasMaskedPath){if(Q!==S[s].line){switch(t.j){case 1:o+=v-t.lineWidths[S[s].line];break;case 2:o+=(v-t.lineWidths[S[s].line])/2}Q=S[s].line}U!==S[s].ind&&(S[U]&&(o+=S[U].extra),o+=S[s].an/2,U=S[s].ind),o+=_[0]*S[s].an*.005;var to=0;for(M=0;M<F;M+=1)(E=A[M].a).p.propType&&((L=A[M].s.getMult(S[s].anIndexes[M],k.a[M].s.totalChars)).length?to+=E.p.v[0]*L[0]:to+=E.p.v[0]*L),E.a.propType&&((L=A[M].s.getMult(S[s].anIndexes[M],k.a[M].s.totalChars)).length?to+=E.a.v[0]*L[0]:to+=E.a.v[0]*L);for(p=!0,this._pathData.a.v&&(o=.5*S[0].an+(v-this._pathData.f.v-.5*S[0].an-.5*S[S.length-1].an)*U/(n-1)+this._pathData.f.v);p;)l+g>=o+to||!m?(y=(o+to-l)/h.partialLength,R=c.point[0]+(h.point[0]-c.point[0])*y,z=c.point[1]+(h.point[1]-c.point[1])*y,w.translate(-_[0]*S[s].an*.005,-_[1]*O*.01),p=!1):m&&(l+=h.partialLength,(f+=1)>=m.length&&(f=0,d[u+=1]?m=d[u].points:x.v.c?(f=0,m=d[u=0].points):(l-=h.partialLength,m=null)),m&&(c=h,g=(h=m[f]).partialLength));B=S[s].an/2-S[s].add,w.translate(-B,0,0)}else B=S[s].an/2-S[s].add,w.translate(-B,0,0),w.translate(-_[0]*S[s].an*.005,-_[1]*O*.01,0);for(M=0;M<F;M+=1)(E=A[M].a).t.propType&&(L=A[M].s.getMult(S[s].anIndexes[M],k.a[M].s.totalChars),0===i&&0===t.j||(this._hasMaskedPath?L.length?o+=E.t.v*L[0]:o+=E.t.v*L:L.length?i+=E.t.v*L[0]:i+=E.t.v*L));for(t.strokeWidthAnim&&(G=t.sw||0),t.strokeColorAnim&&(N=t.sc?[t.sc[0],t.sc[1],t.sc[2]]:[0,0,0]),t.fillColorAnim&&t.fc&&(q=[t.fc[0],t.fc[1],t.fc[2]]),M=0;M<F;M+=1)(E=A[M].a).a.propType&&((L=A[M].s.getMult(S[s].anIndexes[M],k.a[M].s.totalChars)).length?w.translate(-E.a.v[0]*L[0],-E.a.v[1]*L[1],E.a.v[2]*L[2]):w.translate(-E.a.v[0]*L,-E.a.v[1]*L,E.a.v[2]*L));for(M=0;M<F;M+=1)(E=A[M].a).s.propType&&((L=A[M].s.getMult(S[s].anIndexes[M],k.a[M].s.totalChars)).length?w.scale(1+(E.s.v[0]-1)*L[0],1+(E.s.v[1]-1)*L[1],1):w.scale(1+(E.s.v[0]-1)*L,1+(E.s.v[1]-1)*L,1));for(M=0;M<F;M+=1){if(E=A[M].a,L=A[M].s.getMult(S[s].anIndexes[M],k.a[M].s.totalChars),E.sk.propType&&(L.length?w.skewFromAxis(-E.sk.v*L[0],E.sa.v*L[1]):w.skewFromAxis(-E.sk.v*L,E.sa.v*L)),E.r.propType&&(L.length?w.rotateZ(-E.r.v*L[2]):w.rotateZ(-E.r.v*L)),E.ry.propType&&(L.length?w.rotateY(E.ry.v*L[1]):w.rotateY(E.ry.v*L)),E.rx.propType&&(L.length?w.rotateX(E.rx.v*L[0]):w.rotateX(E.rx.v*L)),E.o.propType&&(L.length?V+=(E.o.v*L[0]-V)*L[0]:V+=(E.o.v*L-V)*L),t.strokeWidthAnim&&E.sw.propType&&(L.length?G+=E.sw.v*L[0]:G+=E.sw.v*L),t.strokeColorAnim&&E.sc.propType)for(W=0;W<3;W+=1)L.length?N[W]+=(E.sc.v[W]-N[W])*L[0]:N[W]+=(E.sc.v[W]-N[W])*L;if(t.fillColorAnim&&t.fc){if(E.fc.propType)for(W=0;W<3;W+=1)L.length?q[W]+=(E.fc.v[W]-q[W])*L[0]:q[W]+=(E.fc.v[W]-q[W])*L;E.fh.propType&&(q=L.length?addHueToRGB(q,E.fh.v*L[0]):addHueToRGB(q,E.fh.v*L)),E.fs.propType&&(q=L.length?addSaturationToRGB(q,E.fs.v*L[0]):addSaturationToRGB(q,E.fs.v*L)),E.fb.propType&&(q=L.length?addBrightnessToRGB(q,E.fb.v*L[0]):addBrightnessToRGB(q,E.fb.v*L))}}for(M=0;M<F;M+=1)(E=A[M].a).p.propType&&(L=A[M].s.getMult(S[s].anIndexes[M],k.a[M].s.totalChars),this._hasMaskedPath?L.length?w.translate(0,E.p.v[1]*L[0],-E.p.v[2]*L[1]):w.translate(0,E.p.v[1]*L,-E.p.v[2]*L):L.length?w.translate(E.p.v[0]*L[0],E.p.v[1]*L[1],-E.p.v[2]*L[2]):w.translate(E.p.v[0]*L,E.p.v[1]*L,-E.p.v[2]*L));if(t.strokeWidthAnim&&(Y=G<0?0:G),t.strokeColorAnim&&(H="rgb("+Math.round(255*N[0])+","+Math.round(255*N[1])+","+Math.round(255*N[2])+")"),t.fillColorAnim&&t.fc&&(X="rgb("+Math.round(255*q[0])+","+Math.round(255*q[1])+","+Math.round(255*q[2])+")"),this._hasMaskedPath){if(w.translate(0,-t.ls),w.translate(0,_[1]*O*.01+r,0),this._pathData.p.v){var th=180*Math.atan(b=(h.point[1]-c.point[1])/(h.point[0]-c.point[0]))/Math.PI;h.point[0]<c.point[0]&&(th+=180),w.rotate(-th*Math.PI/180)}w.translate(R,z,0),o-=_[0]*S[s].an*.005,S[s+1]&&U!==S[s+1].ind&&(o+=S[s].an/2,o+=.001*t.tr*t.finalSize)}else{switch(w.translate(i,r,0),t.ps&&w.translate(t.ps[0],t.ps[1]+t.ascent,0),t.j){case 1:w.translate(S[s].animatorJustifyOffset+t.justifyOffset+(t.boxWidth-t.lineWidths[S[s].line]),0,0);break;case 2:w.translate(S[s].animatorJustifyOffset+t.justifyOffset+(t.boxWidth-t.lineWidths[S[s].line])/2,0,0)}w.translate(0,-t.ls),w.translate(B,0,0),w.translate(_[0]*S[s].an*.005,_[1]*O*.01,0),i+=S[s].l+.001*t.tr*t.finalSize}"html"===P?tt=w.toCSS():"svg"===P?tt=w.to2dCSS():te=[w.props[0],w.props[1],w.props[2],w.props[3],w.props[4],w.props[5],w.props[6],w.props[7],w.props[8],w.props[9],w.props[10],w.props[11],w.props[12],w.props[13],w.props[14],w.props[15]],J=V}C<=s?(I=new LetterProps(J,Y,H,X,tt,te),this.renderedLetters.push(I),C+=1,this.lettersChangedFlag=!0):(I=this.renderedLetters[s],this.lettersChangedFlag=I.update(J,Y,H,X,tt,te)||this.lettersChangedFlag)}}},TextAnimatorProperty.prototype.getValue=function(){this._elem.globalData.frameId!==this._frameId&&(this._frameId=this._elem.globalData.frameId,this.iterateDynamicProperties())},TextAnimatorProperty.prototype.mHelper=new Matrix,TextAnimatorProperty.prototype.defaultPropsArray=[],extendPrototype([DynamicPropertyContainer],TextAnimatorProperty),ITextElement.prototype.initElement=function(t,e,i){this.lettersChangedFlag=!0,this.initFrame(),this.initBaseData(t,e,i),this.textProperty=new TextProperty(this,t.t,this.dynamicProperties),this.textAnimator=new TextAnimatorProperty(t.t,this.renderType,this),this.initTransform(t,e,i),this.initHierarchy(),this.initRenderable(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),this.createContent(),this.hide(),this.textAnimator.searchProperties(this.dynamicProperties)},ITextElement.prototype.prepareFrame=function(t){this._mdf=!1,this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange)},ITextElement.prototype.createPathShape=function(t,e){var i,r,s=e.length,n="";for(i=0;i<s;i+=1)"sh"===e[i].ty&&(n+=buildShapeString(r=e[i].ks.k,r.i.length,!0,t));return n},ITextElement.prototype.updateDocumentData=function(t,e){this.textProperty.updateDocumentData(t,e)},ITextElement.prototype.canResizeFont=function(t){this.textProperty.canResizeFont(t)},ITextElement.prototype.setMinimumFontSize=function(t){this.textProperty.setMinimumFontSize(t)},ITextElement.prototype.applyTextPropertiesToMatrix=function(t,e,i,r,s){switch(t.ps&&e.translate(t.ps[0],t.ps[1]+t.ascent,0),e.translate(0,-t.ls,0),t.j){case 1:e.translate(t.justifyOffset+(t.boxWidth-t.lineWidths[i]),0,0);break;case 2:e.translate(t.justifyOffset+(t.boxWidth-t.lineWidths[i])/2,0,0)}e.translate(r,s,0)},ITextElement.prototype.buildColor=function(t){return"rgb("+Math.round(255*t[0])+","+Math.round(255*t[1])+","+Math.round(255*t[2])+")"},ITextElement.prototype.emptyProp=new LetterProps,ITextElement.prototype.destroy=function(){},ITextElement.prototype.validateText=function(){(this.textProperty._mdf||this.textProperty._isFirstFrame)&&(this.buildNewText(),this.textProperty._isFirstFrame=!1,this.textProperty._mdf=!1)};var emptyShapeData={shapes:[]};function SVGTextLottieElement(t,e,i){this.textSpans=[],this.renderType="svg",this.initElement(t,e,i)}function ISolidElement(t,e,i){this.initElement(t,e,i)}function NullElement(t,e,i){this.initFrame(),this.initBaseData(t,e,i),this.initFrame(),this.initTransform(t,e,i),this.initHierarchy()}function SVGRendererBase(){}function ICompElement(){}function SVGCompElement(t,e,i){this.layers=t.layers,this.supports3d=!0,this.completeLayers=!1,this.pendingElements=[],this.elements=this.layers?createSizedArray(this.layers.length):[],this.initElement(t,e,i),this.tm=t.tm?PropertyFactory.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0}}function SVGRenderer(t,e){this.animationItem=t,this.layers=null,this.renderedFrame=-1,this.svgElement=createNS("svg");var i="";if(e&&e.title){var r=createNS("title"),s=createElementID();r.setAttribute("id",s),r.textContent=e.title,this.svgElement.appendChild(r),i+=s}if(e&&e.description){var n=createNS("desc"),a=createElementID();n.setAttribute("id",a),n.textContent=e.description,this.svgElement.appendChild(n),i+=" "+a}i&&this.svgElement.setAttribute("aria-labelledby",i);var o=createNS("defs");this.svgElement.appendChild(o);var h=createNS("g");this.svgElement.appendChild(h),this.layerElement=h,this.renderConfig={preserveAspectRatio:e&&e.preserveAspectRatio||"xMidYMid meet",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",contentVisibility:e&&e.contentVisibility||"visible",progressiveLoad:e&&e.progressiveLoad||!1,hideOnTransparent:!(e&&!1===e.hideOnTransparent),viewBoxOnly:e&&e.viewBoxOnly||!1,viewBoxSize:e&&e.viewBoxSize||!1,className:e&&e.className||"",id:e&&e.id||"",focusable:e&&e.focusable,filterSize:{width:e&&e.filterSize&&e.filterSize.width||"100%",height:e&&e.filterSize&&e.filterSize.height||"100%",x:e&&e.filterSize&&e.filterSize.x||"0%",y:e&&e.filterSize&&e.filterSize.y||"0%"},width:e&&e.width,height:e&&e.height,runExpressions:!e||void 0===e.runExpressions||e.runExpressions},this.globalData={_mdf:!1,frameNum:-1,defs:o,renderConfig:this.renderConfig},this.elements=[],this.pendingElements=[],this.destroyed=!1,this.rendererType="svg"}function ShapeTransformManager(){this.sequences={},this.sequenceList=[],this.transform_key_count=0}extendPrototype([BaseElement,TransformElement,SVGBaseElement,HierarchyElement,FrameElement,RenderableDOMElement,ITextElement],SVGTextLottieElement),SVGTextLottieElement.prototype.createContent=function(){this.data.singleShape&&!this.globalData.fontManager.chars&&(this.textContainer=createNS("text"))},SVGTextLottieElement.prototype.buildTextContents=function(t){for(var e=0,i=t.length,r=[],s="";e<i;)"\r"===t[e]||"\x03"===t[e]?(r.push(s),s=""):s+=t[e],e+=1;return r.push(s),r},SVGTextLottieElement.prototype.buildShapeData=function(t,e){if(t.shapes&&t.shapes.length){var i=t.shapes[0];if(i.it){var r=i.it[i.it.length-1];r.s&&(r.s.k[0]=e,r.s.k[1]=e)}}return t},SVGTextLottieElement.prototype.buildNewText=function(){this.addDynamicProperty(this);var t=this.textProperty.currentData;this.renderedLetters=createSizedArray(t?t.l.length:0),t.fc?this.layerElement.setAttribute("fill",this.buildColor(t.fc)):this.layerElement.setAttribute("fill","rgba(0,0,0,0)"),t.sc&&(this.layerElement.setAttribute("stroke",this.buildColor(t.sc)),this.layerElement.setAttribute("stroke-width",t.sw)),this.layerElement.setAttribute("font-size",t.finalSize);var e=this.globalData.fontManager.getFontByName(t.f);if(e.fClass)this.layerElement.setAttribute("class",e.fClass);else{this.layerElement.setAttribute("font-family",e.fFamily);var i=t.fWeight,r=t.fStyle;this.layerElement.setAttribute("font-style",r),this.layerElement.setAttribute("font-weight",i)}this.layerElement.setAttribute("aria-label",t.t);var s,n,a,o=t.l||[],h=!!this.globalData.fontManager.chars;n=o.length;var l=this.mHelper,p=this.data.singleShape,f=0,u=0,c=!0,m=.001*t.tr*t.finalSize;if(!p||h||t.sz){var d,g=this.textSpans.length;for(s=0;s<n;s+=1){if(this.textSpans[s]||(this.textSpans[s]={span:null,childSpan:null,glyph:null}),!h||!p||0===s){if(a=g>s?this.textSpans[s].span:createNS(h?"g":"text"),g<=s){if(a.setAttribute("stroke-linecap","butt"),a.setAttribute("stroke-linejoin","round"),a.setAttribute("stroke-miterlimit","4"),this.textSpans[s].span=a,h){var v,y=createNS("g");a.appendChild(y),this.textSpans[s].childSpan=y}this.textSpans[s].span=a,this.layerElement.appendChild(a)}a.style.display="inherit"}if(l.reset(),p&&(o[s].n&&(f=-m,u+=t.yOffset,u+=+!!c,c=!1),this.applyTextPropertiesToMatrix(t,l,o[s].line,f,u),f+=o[s].l||0,f+=m),h){if(1===(d=this.globalData.fontManager.getCharData(t.finalText[s],e.fStyle,this.globalData.fontManager.getFontByName(t.f).fFamily)).t)v=new SVGCompElement(d.data,this.globalData,this);else{var b=emptyShapeData;d.data&&d.data.shapes&&(b=this.buildShapeData(d.data,t.finalSize)),v=new SVGShapeElement(b,this.globalData,this)}if(this.textSpans[s].glyph){var x=this.textSpans[s].glyph;this.textSpans[s].childSpan.removeChild(x.layerElement),x.destroy()}this.textSpans[s].glyph=v,v._debug=!0,v.prepareFrame(0),v.renderFrame(),this.textSpans[s].childSpan.appendChild(v.layerElement),1===d.t&&this.textSpans[s].childSpan.setAttribute("transform","scale("+t.finalSize/100+","+t.finalSize/100+")")}else p&&a.setAttribute("transform","translate("+l.props[12]+","+l.props[13]+")"),a.textContent=o[s].val,a.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve")}p&&a&&a.setAttribute("d","")}else{var _=this.textContainer,A="start";switch(t.j){case 1:A="end";break;case 2:A="middle";break;default:A="start"}_.setAttribute("text-anchor",A),_.setAttribute("letter-spacing",m);var k=this.buildTextContents(t.finalText);for(n=k.length,u=t.ps?t.ps[1]+t.ascent:0,s=0;s<n;s+=1)(a=this.textSpans[s].span||createNS("tspan")).textContent=k[s],a.setAttribute("x",0),a.setAttribute("y",u),a.style.display="inherit",_.appendChild(a),this.textSpans[s]||(this.textSpans[s]={span:null,glyph:null}),this.textSpans[s].span=a,u+=t.finalLineHeight;this.layerElement.appendChild(_)}for(;s<this.textSpans.length;)this.textSpans[s].span.style.display="none",s+=1;this._sizeChanged=!0},SVGTextLottieElement.prototype.sourceRectAtTime=function(){if(this.prepareFrame(this.comp.renderedFrame-this.data.st),this.renderInnerContent(),this._sizeChanged){this._sizeChanged=!1;var t=this.layerElement.getBBox();this.bbox={top:t.y,left:t.x,width:t.width,height:t.height}}return this.bbox},SVGTextLottieElement.prototype.getValue=function(){var t,e,i=this.textSpans.length;for(this.renderedFrame=this.comp.renderedFrame,t=0;t<i;t+=1)(e=this.textSpans[t].glyph)&&(e.prepareFrame(this.comp.renderedFrame-this.data.st),e._mdf&&(this._mdf=!0))},SVGTextLottieElement.prototype.renderInnerContent=function(){if(this.validateText(),(!this.data.singleShape||this._mdf)&&(this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag),this.lettersChangedFlag||this.textAnimator.lettersChangedFlag)){this._sizeChanged=!0;var t,e,i,r,s,n=this.textAnimator.renderedLetters,a=this.textProperty.currentData.l;for(e=a.length,t=0;t<e;t+=1)a[t].n||(i=n[t],r=this.textSpans[t].span,(s=this.textSpans[t].glyph)&&s.renderFrame(),i._mdf.m&&r.setAttribute("transform",i.m),i._mdf.o&&r.setAttribute("opacity",i.o),i._mdf.sw&&r.setAttribute("stroke-width",i.sw),i._mdf.sc&&r.setAttribute("stroke",i.sc),i._mdf.fc&&r.setAttribute("fill",i.fc))}},extendPrototype([IImageElement],ISolidElement),ISolidElement.prototype.createContent=function(){var t=createNS("rect");t.setAttribute("width",this.data.sw),t.setAttribute("height",this.data.sh),t.setAttribute("fill",this.data.sc),this.layerElement.appendChild(t)},NullElement.prototype.prepareFrame=function(t){this.prepareProperties(t,!0)},NullElement.prototype.renderFrame=function(){},NullElement.prototype.getBaseElement=function(){return null},NullElement.prototype.destroy=function(){},NullElement.prototype.sourceRectAtTime=function(){},NullElement.prototype.hide=function(){},extendPrototype([BaseElement,TransformElement,HierarchyElement,FrameElement],NullElement),extendPrototype([BaseRenderer],SVGRendererBase),SVGRendererBase.prototype.createNull=function(t){return new NullElement(t,this.globalData,this)},SVGRendererBase.prototype.createShape=function(t){return new SVGShapeElement(t,this.globalData,this)},SVGRendererBase.prototype.createText=function(t){return new SVGTextLottieElement(t,this.globalData,this)},SVGRendererBase.prototype.createImage=function(t){return new IImageElement(t,this.globalData,this)},SVGRendererBase.prototype.createSolid=function(t){return new ISolidElement(t,this.globalData,this)},SVGRendererBase.prototype.configAnimation=function(t){this.svgElement.setAttribute("xmlns","http://www.w3.org/2000/svg"),this.svgElement.setAttribute("xmlns:xlink","http://www.w3.org/1999/xlink"),this.renderConfig.viewBoxSize?this.svgElement.setAttribute("viewBox",this.renderConfig.viewBoxSize):this.svgElement.setAttribute("viewBox","0 0 "+t.w+" "+t.h),this.renderConfig.viewBoxOnly||(this.svgElement.setAttribute("width",t.w),this.svgElement.setAttribute("height",t.h),this.svgElement.style.width="100%",this.svgElement.style.height="100%",this.svgElement.style.transform="translate3d(0,0,0)",this.svgElement.style.contentVisibility=this.renderConfig.contentVisibility),this.renderConfig.width&&this.svgElement.setAttribute("width",this.renderConfig.width),this.renderConfig.height&&this.svgElement.setAttribute("height",this.renderConfig.height),this.renderConfig.className&&this.svgElement.setAttribute("class",this.renderConfig.className),this.renderConfig.id&&this.svgElement.setAttribute("id",this.renderConfig.id),void 0!==this.renderConfig.focusable&&this.svgElement.setAttribute("focusable",this.renderConfig.focusable),this.svgElement.setAttribute("preserveAspectRatio",this.renderConfig.preserveAspectRatio),this.animationItem.wrapper.appendChild(this.svgElement);var e=this.globalData.defs;this.setupGlobalData(t,e),this.globalData.progressiveLoad=this.renderConfig.progressiveLoad,this.data=t;var i=createNS("clipPath"),r=createNS("rect");r.setAttribute("width",t.w),r.setAttribute("height",t.h),r.setAttribute("x",0),r.setAttribute("y",0);var s=createElementID();i.setAttribute("id",s),i.appendChild(r),this.layerElement.setAttribute("clip-path","url("+getLocationHref()+"#"+s+")"),e.appendChild(i),this.layers=t.layers,this.elements=createSizedArray(t.layers.length)},SVGRendererBase.prototype.destroy=function(){this.animationItem.wrapper&&(this.animationItem.wrapper.innerText=""),this.layerElement=null,this.globalData.defs=null;var t,e=this.layers?this.layers.length:0;for(t=0;t<e;t+=1)this.elements[t]&&this.elements[t].destroy&&this.elements[t].destroy();this.elements.length=0,this.destroyed=!0,this.animationItem=null},SVGRendererBase.prototype.updateContainerSize=function(){},SVGRendererBase.prototype.findIndexByInd=function(t){var e=0,i=this.layers.length;for(e=0;e<i;e+=1)if(this.layers[e].ind===t)return e;return -1},SVGRendererBase.prototype.buildItem=function(t){var e=this.elements;if(!e[t]&&99!==this.layers[t].ty){e[t]=!0;var i=this.createItem(this.layers[t]);if(e[t]=i,getExpressionsPlugin()&&(0===this.layers[t].ty&&this.globalData.projectInterface.registerComposition(i),i.initExpressions()),this.appendElementInPos(i,t),this.layers[t].tt){var r="tp"in this.layers[t]?this.findIndexByInd(this.layers[t].tp):t-1;if(-1===r)return;if(this.elements[r]&&!0!==this.elements[r]){var s=e[r].getMatte(this.layers[t].tt);i.setMatte(s)}else this.buildItem(r),this.addPendingElement(i)}}},SVGRendererBase.prototype.checkPendingElements=function(){for(;this.pendingElements.length;){var t=this.pendingElements.pop();if(t.checkParenting(),t.data.tt)for(var e=0,i=this.elements.length;e<i;){if(this.elements[e]===t){var r="tp"in t.data?this.findIndexByInd(t.data.tp):e-1,s=this.elements[r].getMatte(this.layers[e].tt);t.setMatte(s);break}e+=1}}},SVGRendererBase.prototype.renderFrame=function(t){if(this.renderedFrame!==t&&!this.destroyed){null===t?t=this.renderedFrame:this.renderedFrame=t,this.globalData.frameNum=t,this.globalData.frameId+=1,this.globalData.projectInterface.currentFrame=t,this.globalData._mdf=!1;var e,i=this.layers.length;for(this.completeLayers||this.checkLayers(t),e=i-1;e>=0;e-=1)(this.completeLayers||this.elements[e])&&this.elements[e].prepareFrame(t-this.layers[e].st);if(this.globalData._mdf)for(e=0;e<i;e+=1)(this.completeLayers||this.elements[e])&&this.elements[e].renderFrame()}},SVGRendererBase.prototype.appendElementInPos=function(t,e){var i=t.getBaseElement();if(i){for(var r,s=0;s<e;)this.elements[s]&&!0!==this.elements[s]&&this.elements[s].getBaseElement()&&(r=this.elements[s].getBaseElement()),s+=1;r?this.layerElement.insertBefore(i,r):this.layerElement.appendChild(i)}},SVGRendererBase.prototype.hide=function(){this.layerElement.style.display="none"},SVGRendererBase.prototype.show=function(){this.layerElement.style.display="block"},extendPrototype([BaseElement,TransformElement,HierarchyElement,FrameElement,RenderableDOMElement],ICompElement),ICompElement.prototype.initElement=function(t,e,i){this.initFrame(),this.initBaseData(t,e,i),this.initTransform(t,e,i),this.initRenderable(),this.initHierarchy(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),!this.data.xt&&e.progressiveLoad||this.buildAllItems(),this.hide()},ICompElement.prototype.prepareFrame=function(t){if(this._mdf=!1,this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange),this.isInRange||this.data.xt){if(this.tm._placeholder)this.renderedFrame=t/this.data.sr;else{var e=this.tm.v;e===this.data.op&&(e=this.data.op-1),this.renderedFrame=e}var i,r=this.elements.length;for(this.completeLayers||this.checkLayers(this.renderedFrame),i=r-1;i>=0;i-=1)(this.completeLayers||this.elements[i])&&(this.elements[i].prepareFrame(this.renderedFrame-this.layers[i].st),this.elements[i]._mdf&&(this._mdf=!0))}},ICompElement.prototype.renderInnerContent=function(){var t,e=this.layers.length;for(t=0;t<e;t+=1)(this.completeLayers||this.elements[t])&&this.elements[t].renderFrame()},ICompElement.prototype.setElements=function(t){this.elements=t},ICompElement.prototype.getElements=function(){return this.elements},ICompElement.prototype.destroyElements=function(){var t,e=this.layers.length;for(t=0;t<e;t+=1)this.elements[t]&&this.elements[t].destroy()},ICompElement.prototype.destroy=function(){this.destroyElements(),this.destroyBaseElement()},extendPrototype([SVGRendererBase,ICompElement,SVGBaseElement],SVGCompElement),SVGCompElement.prototype.createComp=function(t){return new SVGCompElement(t,this.globalData,this)},extendPrototype([SVGRendererBase],SVGRenderer),SVGRenderer.prototype.createComp=function(t){return new SVGCompElement(t,this.globalData,this)},ShapeTransformManager.prototype={addTransformSequence:function(t){var e,i=t.length,r="_";for(e=0;e<i;e+=1)r+=t[e].transform.key+"_";var s=this.sequences[r];return s||(s={transforms:[].concat(t),finalTransform:new Matrix,_mdf:!1},this.sequences[r]=s,this.sequenceList.push(s)),s},processSequence:function(t,e){for(var i=0,r=t.transforms.length,s=e;i<r&&!e;){if(t.transforms[i].transform.mProps._mdf){s=!0;break}i+=1}if(s)for(t.finalTransform.reset(),i=r-1;i>=0;i-=1)t.finalTransform.multiply(t.transforms[i].transform.mProps.v);t._mdf=s},processSequences:function(t){var e,i=this.sequenceList.length;for(e=0;e<i;e+=1)this.processSequence(this.sequenceList[e],t)},getNewKey:function(){return this.transform_key_count+=1,"_"+this.transform_key_count}};var lumaLoader=function(){var t="__lottie_element_luma_buffer",e=null,i=null,r=null;function s(){var s,n,a;e||(s=createNS("svg"),n=createNS("filter"),a=createNS("feColorMatrix"),n.setAttribute("id",t),a.setAttribute("type","matrix"),a.setAttribute("color-interpolation-filters","sRGB"),a.setAttribute("values","0.3, 0.3, 0.3, 0, 0, 0.3, 0.3, 0.3, 0, 0, 0.3, 0.3, 0.3, 0, 0, 0.3, 0.3, 0.3, 0, 0"),n.appendChild(a),s.appendChild(n),s.setAttribute("id",t+"_svg"),featureSupport.svgLumaHidden&&(s.style.display="none"),r=s,document.body.appendChild(r),(i=(e=createTag("canvas")).getContext("2d")).filter="url(#"+t+")",i.fillStyle="rgba(0,0,0,0)",i.fillRect(0,0,1,1))}return{load:s,get:function(r){return e||s(),e.width=r.width,e.height=r.height,i.filter="url(#"+t+")",e}}};function createCanvas(t,e){if(featureSupport.offscreenCanvas)return new OffscreenCanvas(t,e);var i=createTag("canvas");return i.width=t,i.height=e,i}var assetLoader={loadLumaCanvas:lumaLoader.load,getLumaCanvas:lumaLoader.get,createCanvas:createCanvas},registeredEffects={};function CVEffects(t){var e,i,r=t.data.ef?t.data.ef.length:0;for(this.filters=[],e=0;e<r;e+=1){i=null;var s=t.data.ef[e].ty;registeredEffects[s]&&(i=new(0,registeredEffects[s].effect)(t.effectsManager.effectElements[e],t)),i&&this.filters.push(i)}this.filters.length&&t.addRenderableComponent(this)}function registerEffect(t,e){registeredEffects[t]={effect:e}}function CVMaskElement(t,e){this.data=t,this.element=e,this.masksProperties=this.data.masksProperties||[],this.viewData=createSizedArray(this.masksProperties.length);var i,r=this.masksProperties.length,s=!1;for(i=0;i<r;i+=1)"n"!==this.masksProperties[i].mode&&(s=!0),this.viewData[i]=ShapePropertyFactory.getShapeProp(this.element,this.masksProperties[i],3);this.hasMasks=s,s&&this.element.addRenderableComponent(this)}function CVBaseElement(){}CVEffects.prototype.renderFrame=function(t){var e,i=this.filters.length;for(e=0;e<i;e+=1)this.filters[e].renderFrame(t)},CVEffects.prototype.getEffects=function(t){var e,i=this.filters.length,r=[];for(e=0;e<i;e+=1)this.filters[e].type===t&&r.push(this.filters[e]);return r},CVMaskElement.prototype.renderFrame=function(){if(this.hasMasks){var t,e,i,r,s=this.element.finalTransform.mat,n=this.element.canvasContext,a=this.masksProperties.length;for(n.beginPath(),t=0;t<a;t+=1)if("n"!==this.masksProperties[t].mode){this.masksProperties[t].inv&&(n.moveTo(0,0),n.lineTo(this.element.globalData.compSize.w,0),n.lineTo(this.element.globalData.compSize.w,this.element.globalData.compSize.h),n.lineTo(0,this.element.globalData.compSize.h),n.lineTo(0,0)),r=this.viewData[t].v,e=s.applyToPointArray(r.v[0][0],r.v[0][1],0),n.moveTo(e[0],e[1]);var o,h=r._length;for(o=1;o<h;o+=1)i=s.applyToTriplePoints(r.o[o-1],r.i[o],r.v[o]),n.bezierCurveTo(i[0],i[1],i[2],i[3],i[4],i[5]);i=s.applyToTriplePoints(r.o[o-1],r.i[0],r.v[0]),n.bezierCurveTo(i[0],i[1],i[2],i[3],i[4],i[5])}this.element.globalData.renderer.save(!0),n.clip()}},CVMaskElement.prototype.getMaskProperty=MaskElement.prototype.getMaskProperty,CVMaskElement.prototype.destroy=function(){this.element=null};var operationsMap={1:"source-in",2:"source-out",3:"source-in",4:"source-out"};function CVShapeData(t,e,i,r){this.styledShapes=[],this.tr=[0,0,0,0,0,0];var s,n=4;"rc"===e.ty?n=5:"el"===e.ty?n=6:"sr"===e.ty&&(n=7),this.sh=ShapePropertyFactory.getShapeProp(t,e,n,t);var a,o=i.length;for(s=0;s<o;s+=1)i[s].closed||(a={transforms:r.addTransformSequence(i[s].transforms),trNodes:[]},this.styledShapes.push(a),i[s].elements.push(a))}function CVShapeElement(t,e,i){this.shapes=[],this.shapesData=t.shapes,this.stylesList=[],this.itemsData=[],this.prevViewData=[],this.shapeModifiers=[],this.processedElements=[],this.transformsManager=new ShapeTransformManager,this.initElement(t,e,i)}function CVTextElement(t,e,i){this.textSpans=[],this.yOffset=0,this.fillColorAnim=!1,this.strokeColorAnim=!1,this.strokeWidthAnim=!1,this.stroke=!1,this.fill=!1,this.justifyOffset=0,this.currentRender=null,this.renderType="canvas",this.values={fill:"rgba(0,0,0,0)",stroke:"rgba(0,0,0,0)",sWidth:0,fValue:""},this.initElement(t,e,i)}function CVImageElement(t,e,i){this.assetData=e.getAssetData(t.refId),this.img=e.imageLoader.getAsset(this.assetData),this.initElement(t,e,i)}function CVSolidElement(t,e,i){this.initElement(t,e,i)}function CanvasRendererBase(){}function CanvasContext(){this.opacity=-1,this.transform=createTypedArray("float32",16),this.fillStyle="",this.strokeStyle="",this.lineWidth="",this.lineCap="",this.lineJoin="",this.miterLimit="",this.id=Math.random()}function CVContextData(){var t;for(this.stack=[],this.cArrPos=0,this.cTr=new Matrix,t=0;t<15;t+=1){var e=new CanvasContext;this.stack[t]=e}this._length=15,this.nativeContext=null,this.transformMat=new Matrix,this.currentOpacity=1,this.currentFillStyle="",this.appliedFillStyle="",this.currentStrokeStyle="",this.appliedStrokeStyle="",this.currentLineWidth="",this.appliedLineWidth="",this.currentLineCap="",this.appliedLineCap="",this.currentLineJoin="",this.appliedLineJoin="",this.appliedMiterLimit="",this.currentMiterLimit=""}function CVCompElement(t,e,i){this.completeLayers=!1,this.layers=t.layers,this.pendingElements=[],this.elements=createSizedArray(this.layers.length),this.initElement(t,e,i),this.tm=t.tm?PropertyFactory.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0}}function CanvasRenderer(t,e){this.animationItem=t,this.renderConfig={clearCanvas:!e||void 0===e.clearCanvas||e.clearCanvas,context:e&&e.context||null,progressiveLoad:e&&e.progressiveLoad||!1,preserveAspectRatio:e&&e.preserveAspectRatio||"xMidYMid meet",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",contentVisibility:e&&e.contentVisibility||"visible",className:e&&e.className||"",id:e&&e.id||"",runExpressions:!e||void 0===e.runExpressions||e.runExpressions},this.renderConfig.dpr=e&&e.dpr||1,this.animationItem.wrapper&&(this.renderConfig.dpr=e&&e.dpr||window.devicePixelRatio||1),this.renderedFrame=-1,this.globalData={frameNum:-1,_mdf:!1,renderConfig:this.renderConfig,currentGlobalAlpha:-1},this.contextData=new CVContextData,this.elements=[],this.pendingElements=[],this.transformMat=new Matrix,this.completeLayers=!1,this.rendererType="canvas",this.renderConfig.clearCanvas&&(this.ctxTransform=this.contextData.transform.bind(this.contextData),this.ctxOpacity=this.contextData.opacity.bind(this.contextData),this.ctxFillStyle=this.contextData.fillStyle.bind(this.contextData),this.ctxStrokeStyle=this.contextData.strokeStyle.bind(this.contextData),this.ctxLineWidth=this.contextData.lineWidth.bind(this.contextData),this.ctxLineCap=this.contextData.lineCap.bind(this.contextData),this.ctxLineJoin=this.contextData.lineJoin.bind(this.contextData),this.ctxMiterLimit=this.contextData.miterLimit.bind(this.contextData),this.ctxFill=this.contextData.fill.bind(this.contextData),this.ctxFillRect=this.contextData.fillRect.bind(this.contextData),this.ctxStroke=this.contextData.stroke.bind(this.contextData),this.save=this.contextData.save.bind(this.contextData))}function HBaseElement(){}function HSolidElement(t,e,i){this.initElement(t,e,i)}function HShapeElement(t,e,i){this.shapes=[],this.shapesData=t.shapes,this.stylesList=[],this.shapeModifiers=[],this.itemsData=[],this.processedElements=[],this.animatedContents=[],this.shapesContainer=createNS("g"),this.initElement(t,e,i),this.prevViewData=[],this.currentBBox={x:999999,y:-999999,h:0,w:0}}function HTextElement(t,e,i){this.textSpans=[],this.textPaths=[],this.currentBBox={x:999999,y:-999999,h:0,w:0},this.renderType="svg",this.isMasked=!1,this.initElement(t,e,i)}function HCameraElement(t,e,i){this.initFrame(),this.initBaseData(t,e,i),this.initHierarchy();var r=PropertyFactory.getProp;if(this.pe=r(this,t.pe,0,0,this),t.ks.p.s?(this.px=r(this,t.ks.p.x,1,0,this),this.py=r(this,t.ks.p.y,1,0,this),this.pz=r(this,t.ks.p.z,1,0,this)):this.p=r(this,t.ks.p,1,0,this),t.ks.a&&(this.a=r(this,t.ks.a,1,0,this)),t.ks.or.k.length&&t.ks.or.k[0].to){var s,n=t.ks.or.k.length;for(s=0;s<n;s+=1)t.ks.or.k[s].to=null,t.ks.or.k[s].ti=null}this.or=r(this,t.ks.or,1,degToRads,this),this.or.sh=!0,this.rx=r(this,t.ks.rx,0,degToRads,this),this.ry=r(this,t.ks.ry,0,degToRads,this),this.rz=r(this,t.ks.rz,0,degToRads,this),this.mat=new Matrix,this._prevMat=new Matrix,this._isFirstFrame=!0,this.finalTransform={mProp:this}}function HImageElement(t,e,i){this.assetData=e.getAssetData(t.refId),this.initElement(t,e,i)}function HybridRendererBase(t,e){this.animationItem=t,this.layers=null,this.renderedFrame=-1,this.renderConfig={className:e&&e.className||"",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",hideOnTransparent:!(e&&!1===e.hideOnTransparent),filterSize:{width:e&&e.filterSize&&e.filterSize.width||"400%",height:e&&e.filterSize&&e.filterSize.height||"400%",x:e&&e.filterSize&&e.filterSize.x||"-100%",y:e&&e.filterSize&&e.filterSize.y||"-100%"}},this.globalData={_mdf:!1,frameNum:-1,renderConfig:this.renderConfig},this.pendingElements=[],this.elements=[],this.threeDElements=[],this.destroyed=!1,this.camera=null,this.supports3d=!0,this.rendererType="html"}function HCompElement(t,e,i){this.layers=t.layers,this.supports3d=!t.hasMask,this.completeLayers=!1,this.pendingElements=[],this.elements=this.layers?createSizedArray(this.layers.length):[],this.initElement(t,e,i),this.tm=t.tm?PropertyFactory.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0}}function HybridRenderer(t,e){this.animationItem=t,this.layers=null,this.renderedFrame=-1,this.renderConfig={className:e&&e.className||"",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",hideOnTransparent:!(e&&!1===e.hideOnTransparent),filterSize:{width:e&&e.filterSize&&e.filterSize.width||"400%",height:e&&e.filterSize&&e.filterSize.height||"400%",x:e&&e.filterSize&&e.filterSize.x||"-100%",y:e&&e.filterSize&&e.filterSize.y||"-100%"},runExpressions:!e||void 0===e.runExpressions||e.runExpressions},this.globalData={_mdf:!1,frameNum:-1,renderConfig:this.renderConfig},this.pendingElements=[],this.elements=[],this.threeDElements=[],this.destroyed=!1,this.camera=null,this.supports3d=!0,this.rendererType="html"}CVBaseElement.prototype={createElements:function(){},initRendererElement:function(){},createContainerElements:function(){if(this.data.tt>=1){this.buffers=[];var t=this.globalData.canvasContext,e=assetLoader.createCanvas(t.canvas.width,t.canvas.height);this.buffers.push(e);var i=assetLoader.createCanvas(t.canvas.width,t.canvas.height);this.buffers.push(i),this.data.tt>=3&&!document._isProxy&&assetLoader.loadLumaCanvas()}this.canvasContext=this.globalData.canvasContext,this.transformCanvas=this.globalData.transformCanvas,this.renderableEffectsManager=new CVEffects(this),this.searchEffectTransforms()},createContent:function(){},setBlendMode:function(){var t=this.globalData;if(t.blendMode!==this.data.bm){t.blendMode=this.data.bm;var e=getBlendMode(this.data.bm);t.canvasContext.globalCompositeOperation=e}},createRenderableComponents:function(){this.maskManager=new CVMaskElement(this.data,this),this.transformEffects=this.renderableEffectsManager.getEffects(effectTypes.TRANSFORM_EFFECT)},hideElement:function(){this.hidden||this.isInRange&&!this.isTransparent||(this.hidden=!0)},showElement:function(){this.isInRange&&!this.isTransparent&&(this.hidden=!1,this._isFirstFrame=!0,this.maskManager._isFirstFrame=!0)},clearCanvas:function(t){t.clearRect(this.transformCanvas.tx,this.transformCanvas.ty,this.transformCanvas.w*this.transformCanvas.sx,this.transformCanvas.h*this.transformCanvas.sy)},prepareLayer:function(){if(this.data.tt>=1){var t=this.buffers[0].getContext("2d");this.clearCanvas(t),t.drawImage(this.canvasContext.canvas,0,0),this.currentTransform=this.canvasContext.getTransform(),this.canvasContext.setTransform(1,0,0,1,0,0),this.clearCanvas(this.canvasContext),this.canvasContext.setTransform(this.currentTransform)}},exitLayer:function(){if(this.data.tt>=1){var t=this.buffers[1],e=t.getContext("2d");if(this.clearCanvas(e),e.drawImage(this.canvasContext.canvas,0,0),this.canvasContext.setTransform(1,0,0,1,0,0),this.clearCanvas(this.canvasContext),this.canvasContext.setTransform(this.currentTransform),this.comp.getElementById("tp"in this.data?this.data.tp:this.data.ind-1).renderFrame(!0),this.canvasContext.setTransform(1,0,0,1,0,0),this.data.tt>=3&&!document._isProxy){var i=assetLoader.getLumaCanvas(this.canvasContext.canvas);i.getContext("2d").drawImage(this.canvasContext.canvas,0,0),this.clearCanvas(this.canvasContext),this.canvasContext.drawImage(i,0,0)}this.canvasContext.globalCompositeOperation=operationsMap[this.data.tt],this.canvasContext.drawImage(t,0,0),this.canvasContext.globalCompositeOperation="destination-over",this.canvasContext.drawImage(this.buffers[0],0,0),this.canvasContext.setTransform(this.currentTransform),this.canvasContext.globalCompositeOperation="source-over"}},renderFrame:function(t){if(!this.hidden&&!this.data.hd&&(1!==this.data.td||t)){this.renderTransform(),this.renderRenderable(),this.renderLocalTransform(),this.setBlendMode();var e=0===this.data.ty;this.prepareLayer(),this.globalData.renderer.save(e),this.globalData.renderer.ctxTransform(this.finalTransform.localMat.props),this.globalData.renderer.ctxOpacity(this.finalTransform.localOpacity),this.renderInnerContent(),this.globalData.renderer.restore(e),this.exitLayer(),this.maskManager.hasMasks&&this.globalData.renderer.restore(!0),this._isFirstFrame&&(this._isFirstFrame=!1)}},destroy:function(){this.canvasContext=null,this.data=null,this.globalData=null,this.maskManager.destroy()},mHelper:new Matrix},CVBaseElement.prototype.hide=CVBaseElement.prototype.hideElement,CVBaseElement.prototype.show=CVBaseElement.prototype.showElement,CVShapeData.prototype.setAsAnimated=SVGShapeData.prototype.setAsAnimated,extendPrototype([BaseElement,TransformElement,CVBaseElement,IShapeElement,HierarchyElement,FrameElement,RenderableElement],CVShapeElement),CVShapeElement.prototype.initElement=RenderableDOMElement.prototype.initElement,CVShapeElement.prototype.transformHelper={opacity:1,_opMdf:!1},CVShapeElement.prototype.dashResetter=[],CVShapeElement.prototype.createContent=function(){this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,!0,[])},CVShapeElement.prototype.createStyleElement=function(t,e){var i={data:t,type:t.ty,preTransforms:this.transformsManager.addTransformSequence(e),transforms:[],elements:[],closed:!0===t.hd},r={};return("fl"===t.ty||"st"===t.ty?(r.c=PropertyFactory.getProp(this,t.c,1,255,this),r.c.k||(i.co="rgb("+bmFloor(r.c.v[0])+","+bmFloor(r.c.v[1])+","+bmFloor(r.c.v[2])+")")):"gf"!==t.ty&&"gs"!==t.ty||(r.s=PropertyFactory.getProp(this,t.s,1,null,this),r.e=PropertyFactory.getProp(this,t.e,1,null,this),r.h=PropertyFactory.getProp(this,t.h||{k:0},0,.01,this),r.a=PropertyFactory.getProp(this,t.a||{k:0},0,degToRads,this),r.g=new GradientProperty(this,t.g,this)),r.o=PropertyFactory.getProp(this,t.o,0,.01,this),"st"===t.ty||"gs"===t.ty)?(i.lc=lineCapEnum[t.lc||2],i.lj=lineJoinEnum[t.lj||2],1==t.lj&&(i.ml=t.ml),r.w=PropertyFactory.getProp(this,t.w,0,null,this),r.w.k||(i.wi=r.w.v),t.d&&(r.d=new DashProperty(this,t.d,"canvas",this),r.d.k||(i.da=r.d.dashArray,i.do=r.d.dashoffset[0]))):i.r=2===t.r?"evenodd":"nonzero",this.stylesList.push(i),r.style=i,r},CVShapeElement.prototype.createGroupElement=function(){return{it:[],prevViewData:[]}},CVShapeElement.prototype.createTransformElement=function(t){return{transform:{opacity:1,_opMdf:!1,key:this.transformsManager.getNewKey(),op:PropertyFactory.getProp(this,t.o,0,.01,this),mProps:TransformPropertyFactory.getTransformProperty(this,t,this)}}},CVShapeElement.prototype.createShapeElement=function(t){var e=new CVShapeData(this,t,this.stylesList,this.transformsManager);return this.shapes.push(e),this.addShapeToModifiers(e),e},CVShapeElement.prototype.reloadShapes=function(){this._isFirstFrame=!0;var t,e=this.itemsData.length;for(t=0;t<e;t+=1)this.prevViewData[t]=this.itemsData[t];for(this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,!0,[]),e=this.dynamicProperties.length,t=0;t<e;t+=1)this.dynamicProperties[t].getValue();this.renderModifiers(),this.transformsManager.processSequences(this._isFirstFrame)},CVShapeElement.prototype.addTransformToStyleList=function(t){var e,i=this.stylesList.length;for(e=0;e<i;e+=1)this.stylesList[e].closed||this.stylesList[e].transforms.push(t)},CVShapeElement.prototype.removeTransformFromStyleList=function(){var t,e=this.stylesList.length;for(t=0;t<e;t+=1)this.stylesList[t].closed||this.stylesList[t].transforms.pop()},CVShapeElement.prototype.closeStyles=function(t){var e,i=t.length;for(e=0;e<i;e+=1)t[e].closed=!0},CVShapeElement.prototype.searchShapes=function(t,e,i,r,s){var n,a,o,h,l,p,f=t.length-1,u=[],c=[],m=[].concat(s);for(n=f;n>=0;n-=1){if((h=this.searchProcessedElement(t[n]))?e[n]=i[h-1]:t[n]._shouldRender=r,"fl"===t[n].ty||"st"===t[n].ty||"gf"===t[n].ty||"gs"===t[n].ty)h?e[n].style.closed=!1:e[n]=this.createStyleElement(t[n],m),u.push(e[n].style);else if("gr"===t[n].ty){if(h)for(o=e[n].it.length,a=0;a<o;a+=1)e[n].prevViewData[a]=e[n].it[a];else e[n]=this.createGroupElement(t[n]);this.searchShapes(t[n].it,e[n].it,e[n].prevViewData,r,m)}else"tr"===t[n].ty?(h||(p=this.createTransformElement(t[n]),e[n]=p),m.push(e[n]),this.addTransformToStyleList(e[n])):"sh"===t[n].ty||"rc"===t[n].ty||"el"===t[n].ty||"sr"===t[n].ty?h||(e[n]=this.createShapeElement(t[n])):"tm"===t[n].ty||"rd"===t[n].ty||"pb"===t[n].ty||"zz"===t[n].ty||"op"===t[n].ty?(h?(l=e[n]).closed=!1:((l=ShapeModifiers.getModifier(t[n].ty)).init(this,t[n]),e[n]=l,this.shapeModifiers.push(l)),c.push(l)):"rp"===t[n].ty&&(h?(l=e[n]).closed=!0:(l=ShapeModifiers.getModifier(t[n].ty),e[n]=l,l.init(this,t,n,e),this.shapeModifiers.push(l),r=!1),c.push(l));this.addProcessedElement(t[n],n+1)}for(this.removeTransformFromStyleList(),this.closeStyles(u),f=c.length,n=0;n<f;n+=1)c[n].closed=!0},CVShapeElement.prototype.renderInnerContent=function(){this.transformHelper.opacity=1,this.transformHelper._opMdf=!1,this.renderModifiers(),this.transformsManager.processSequences(this._isFirstFrame),this.renderShape(this.transformHelper,this.shapesData,this.itemsData,!0)},CVShapeElement.prototype.renderShapeTransform=function(t,e){(t._opMdf||e.op._mdf||this._isFirstFrame)&&(e.opacity=t.opacity,e.opacity*=e.op.v,e._opMdf=!0)},CVShapeElement.prototype.drawLayer=function(){var t,e,i,r,s,n,a,o,h,l=this.stylesList.length,p=this.globalData.renderer,f=this.globalData.canvasContext;for(t=0;t<l;t+=1)if(("st"!==(o=(h=this.stylesList[t]).type)&&"gs"!==o||0!==h.wi)&&h.data._shouldRender&&0!==h.coOp&&0!==this.globalData.currentGlobalAlpha){for(p.save(),n=h.elements,"st"===o||"gs"===o?(p.ctxStrokeStyle("st"===o?h.co:h.grd),p.ctxLineWidth(h.wi),p.ctxLineCap(h.lc),p.ctxLineJoin(h.lj),p.ctxMiterLimit(h.ml||0)):p.ctxFillStyle("fl"===o?h.co:h.grd),p.ctxOpacity(h.coOp),"st"!==o&&"gs"!==o&&f.beginPath(),p.ctxTransform(h.preTransforms.finalTransform.props),i=n.length,e=0;e<i;e+=1){for("st"!==o&&"gs"!==o||(f.beginPath(),h.da&&(f.setLineDash(h.da),f.lineDashOffset=h.do)),s=(a=n[e].trNodes).length,r=0;r<s;r+=1)"m"===a[r].t?f.moveTo(a[r].p[0],a[r].p[1]):"c"===a[r].t?f.bezierCurveTo(a[r].pts[0],a[r].pts[1],a[r].pts[2],a[r].pts[3],a[r].pts[4],a[r].pts[5]):f.closePath();"st"!==o&&"gs"!==o||(p.ctxStroke(),h.da&&f.setLineDash(this.dashResetter))}"st"!==o&&"gs"!==o&&this.globalData.renderer.ctxFill(h.r),p.restore()}},CVShapeElement.prototype.renderShape=function(t,e,i,r){var s,n;for(n=t,s=e.length-1;s>=0;s-=1)"tr"===e[s].ty?(n=i[s].transform,this.renderShapeTransform(t,n)):"sh"===e[s].ty||"el"===e[s].ty||"rc"===e[s].ty||"sr"===e[s].ty?this.renderPath(e[s],i[s]):"fl"===e[s].ty?this.renderFill(e[s],i[s],n):"st"===e[s].ty?this.renderStroke(e[s],i[s],n):"gf"===e[s].ty||"gs"===e[s].ty?this.renderGradientFill(e[s],i[s],n):"gr"===e[s].ty?this.renderShape(n,e[s].it,i[s].it):e[s].ty;r&&this.drawLayer()},CVShapeElement.prototype.renderStyledShape=function(t,e){if(this._isFirstFrame||e._mdf||t.transforms._mdf){var i,r,s,n=t.trNodes,a=e.paths,o=a._length;n.length=0;var h=t.transforms.finalTransform;for(s=0;s<o;s+=1){var l=a.shapes[s];if(l&&l.v){for(r=l._length,i=1;i<r;i+=1)1===i&&n.push({t:"m",p:h.applyToPointArray(l.v[0][0],l.v[0][1],0)}),n.push({t:"c",pts:h.applyToTriplePoints(l.o[i-1],l.i[i],l.v[i])});1===r&&n.push({t:"m",p:h.applyToPointArray(l.v[0][0],l.v[0][1],0)}),l.c&&r&&(n.push({t:"c",pts:h.applyToTriplePoints(l.o[i-1],l.i[0],l.v[0])}),n.push({t:"z"}))}}t.trNodes=n}},CVShapeElement.prototype.renderPath=function(t,e){if(!0!==t.hd&&t._shouldRender){var i,r=e.styledShapes.length;for(i=0;i<r;i+=1)this.renderStyledShape(e.styledShapes[i],e.sh)}},CVShapeElement.prototype.renderFill=function(t,e,i){var r=e.style;(e.c._mdf||this._isFirstFrame)&&(r.co="rgb("+bmFloor(e.c.v[0])+","+bmFloor(e.c.v[1])+","+bmFloor(e.c.v[2])+")"),(e.o._mdf||i._opMdf||this._isFirstFrame)&&(r.coOp=e.o.v*i.opacity)},CVShapeElement.prototype.renderGradientFill=function(t,e,i){var r,s=e.style;if(!s.grd||e.g._mdf||e.s._mdf||e.e._mdf||1!==t.t&&(e.h._mdf||e.a._mdf)){var n,a=this.globalData.canvasContext,o=e.s.v,h=e.e.v;if(1===t.t)r=a.createLinearGradient(o[0],o[1],h[0],h[1]);else{var l=Math.sqrt(Math.pow(o[0]-h[0],2)+Math.pow(o[1]-h[1],2)),p=Math.atan2(h[1]-o[1],h[0]-o[0]),f=e.h.v;f>=1?f=.99:f<=-1&&(f=-.99);var u=l*f,c=Math.cos(p+e.a.v)*u+o[0],m=Math.sin(p+e.a.v)*u+o[1];r=a.createRadialGradient(c,m,0,o[0],o[1],l)}var d=t.g.p,g=e.g.c,v=1;for(n=0;n<d;n+=1)e.g._hasOpacity&&e.g._collapsable&&(v=e.g.o[2*n+1]),r.addColorStop(g[4*n]/100,"rgba("+g[4*n+1]+","+g[4*n+2]+","+g[4*n+3]+","+v+")");s.grd=r}s.coOp=e.o.v*i.opacity},CVShapeElement.prototype.renderStroke=function(t,e,i){var r=e.style,s=e.d;s&&(s._mdf||this._isFirstFrame)&&(r.da=s.dashArray,r.do=s.dashoffset[0]),(e.c._mdf||this._isFirstFrame)&&(r.co="rgb("+bmFloor(e.c.v[0])+","+bmFloor(e.c.v[1])+","+bmFloor(e.c.v[2])+")"),(e.o._mdf||i._opMdf||this._isFirstFrame)&&(r.coOp=e.o.v*i.opacity),(e.w._mdf||this._isFirstFrame)&&(r.wi=e.w.v)},CVShapeElement.prototype.destroy=function(){this.shapesData=null,this.globalData=null,this.canvasContext=null,this.stylesList.length=0,this.itemsData.length=0},extendPrototype([BaseElement,TransformElement,CVBaseElement,HierarchyElement,FrameElement,RenderableElement,ITextElement],CVTextElement),CVTextElement.prototype.tHelper=createTag("canvas").getContext("2d"),CVTextElement.prototype.buildNewText=function(){var t=this.textProperty.currentData;this.renderedLetters=createSizedArray(t.l?t.l.length:0);var e=!1;t.fc?(e=!0,this.values.fill=this.buildColor(t.fc)):this.values.fill="rgba(0,0,0,0)",this.fill=e;var i=!1;t.sc&&(i=!0,this.values.stroke=this.buildColor(t.sc),this.values.sWidth=t.sw);var r,s,n,a,o,h,l,p,f,u,c,m,d=this.globalData.fontManager.getFontByName(t.f),g=t.l,v=this.mHelper;this.stroke=i,this.values.fValue=t.finalSize+"px "+this.globalData.fontManager.getFontByName(t.f).fFamily,s=t.finalText.length;var y=this.data.singleShape,b=.001*t.tr*t.finalSize,x=0,_=0,A=!0,k=0;for(r=0;r<s;r+=1){a=(n=this.globalData.fontManager.getCharData(t.finalText[r],d.fStyle,this.globalData.fontManager.getFontByName(t.f).fFamily))&&n.data||{},v.reset(),y&&g[r].n&&(x=-b,_+=t.yOffset,_+=+!!A,A=!1),f=(l=a.shapes?a.shapes[0].it:[]).length,v.scale(t.finalSize/100,t.finalSize/100),y&&this.applyTextPropertiesToMatrix(t,v,g[r].line,x,_),c=createSizedArray(f-1);var w=0;for(p=0;p<f;p+=1)if("sh"===l[p].ty){for(h=l[p].ks.k.i.length,u=l[p].ks.k,m=[],o=1;o<h;o+=1)1===o&&m.push(v.applyToX(u.v[0][0],u.v[0][1],0),v.applyToY(u.v[0][0],u.v[0][1],0)),m.push(v.applyToX(u.o[o-1][0],u.o[o-1][1],0),v.applyToY(u.o[o-1][0],u.o[o-1][1],0),v.applyToX(u.i[o][0],u.i[o][1],0),v.applyToY(u.i[o][0],u.i[o][1],0),v.applyToX(u.v[o][0],u.v[o][1],0),v.applyToY(u.v[o][0],u.v[o][1],0));m.push(v.applyToX(u.o[o-1][0],u.o[o-1][1],0),v.applyToY(u.o[o-1][0],u.o[o-1][1],0),v.applyToX(u.i[0][0],u.i[0][1],0),v.applyToY(u.i[0][0],u.i[0][1],0),v.applyToX(u.v[0][0],u.v[0][1],0),v.applyToY(u.v[0][0],u.v[0][1],0)),c[w]=m,w+=1}y&&(x+=g[r].l,x+=b),this.textSpans[k]?this.textSpans[k].elem=c:this.textSpans[k]={elem:c},k+=1}},CVTextElement.prototype.renderInnerContent=function(){this.validateText(),this.canvasContext.font=this.values.fValue,this.globalData.renderer.ctxLineCap("butt"),this.globalData.renderer.ctxLineJoin("miter"),this.globalData.renderer.ctxMiterLimit(4),this.data.singleShape||this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag);var t,e,i,r,s,n,a,o=this.textAnimator.renderedLetters,h=this.textProperty.currentData.l;e=h.length;var l,p,f=null,u=null,c=null,m=this.globalData.renderer;for(t=0;t<e;t+=1)if(!h[t].n){if((a=o[t])&&(m.save(),m.ctxTransform(a.p),m.ctxOpacity(a.o)),this.fill){for(a&&a.fc?f!==a.fc&&(m.ctxFillStyle(a.fc),f=a.fc):f!==this.values.fill&&(f=this.values.fill,m.ctxFillStyle(this.values.fill)),r=(l=this.textSpans[t].elem).length,this.globalData.canvasContext.beginPath(),i=0;i<r;i+=1)for(n=(p=l[i]).length,this.globalData.canvasContext.moveTo(p[0],p[1]),s=2;s<n;s+=6)this.globalData.canvasContext.bezierCurveTo(p[s],p[s+1],p[s+2],p[s+3],p[s+4],p[s+5]);this.globalData.canvasContext.closePath(),m.ctxFill()}if(this.stroke){for(a&&a.sw?c!==a.sw&&(c=a.sw,m.ctxLineWidth(a.sw)):c!==this.values.sWidth&&(c=this.values.sWidth,m.ctxLineWidth(this.values.sWidth)),a&&a.sc?u!==a.sc&&(u=a.sc,m.ctxStrokeStyle(a.sc)):u!==this.values.stroke&&(u=this.values.stroke,m.ctxStrokeStyle(this.values.stroke)),r=(l=this.textSpans[t].elem).length,this.globalData.canvasContext.beginPath(),i=0;i<r;i+=1)for(n=(p=l[i]).length,this.globalData.canvasContext.moveTo(p[0],p[1]),s=2;s<n;s+=6)this.globalData.canvasContext.bezierCurveTo(p[s],p[s+1],p[s+2],p[s+3],p[s+4],p[s+5]);this.globalData.canvasContext.closePath(),m.ctxStroke()}a&&this.globalData.renderer.restore()}},extendPrototype([BaseElement,TransformElement,CVBaseElement,HierarchyElement,FrameElement,RenderableElement],CVImageElement),CVImageElement.prototype.initElement=SVGShapeElement.prototype.initElement,CVImageElement.prototype.prepareFrame=IImageElement.prototype.prepareFrame,CVImageElement.prototype.createContent=function(){if(this.img.width&&(this.assetData.w!==this.img.width||this.assetData.h!==this.img.height)){var t=createTag("canvas");t.width=this.assetData.w,t.height=this.assetData.h;var e,i,r=t.getContext("2d"),s=this.img.width,n=this.img.height,a=s/n,o=this.assetData.w/this.assetData.h,h=this.assetData.pr||this.globalData.renderConfig.imagePreserveAspectRatio;a>o&&"xMidYMid slice"===h||a<o&&"xMidYMid slice"!==h?e=(i=n)*o:i=(e=s)/o,r.drawImage(this.img,(s-e)/2,(n-i)/2,e,i,0,0,this.assetData.w,this.assetData.h),this.img=t}},CVImageElement.prototype.renderInnerContent=function(){this.canvasContext.drawImage(this.img,0,0)},CVImageElement.prototype.destroy=function(){this.img=null},extendPrototype([BaseElement,TransformElement,CVBaseElement,HierarchyElement,FrameElement,RenderableElement],CVSolidElement),CVSolidElement.prototype.initElement=SVGShapeElement.prototype.initElement,CVSolidElement.prototype.prepareFrame=IImageElement.prototype.prepareFrame,CVSolidElement.prototype.renderInnerContent=function(){this.globalData.renderer.ctxFillStyle(this.data.sc),this.globalData.renderer.ctxFillRect(0,0,this.data.sw,this.data.sh)},extendPrototype([BaseRenderer],CanvasRendererBase),CanvasRendererBase.prototype.createShape=function(t){return new CVShapeElement(t,this.globalData,this)},CanvasRendererBase.prototype.createText=function(t){return new CVTextElement(t,this.globalData,this)},CanvasRendererBase.prototype.createImage=function(t){return new CVImageElement(t,this.globalData,this)},CanvasRendererBase.prototype.createSolid=function(t){return new CVSolidElement(t,this.globalData,this)},CanvasRendererBase.prototype.createNull=SVGRenderer.prototype.createNull,CanvasRendererBase.prototype.ctxTransform=function(t){1===t[0]&&0===t[1]&&0===t[4]&&1===t[5]&&0===t[12]&&0===t[13]||this.canvasContext.transform(t[0],t[1],t[4],t[5],t[12],t[13])},CanvasRendererBase.prototype.ctxOpacity=function(t){this.canvasContext.globalAlpha*=t<0?0:t},CanvasRendererBase.prototype.ctxFillStyle=function(t){this.canvasContext.fillStyle=t},CanvasRendererBase.prototype.ctxStrokeStyle=function(t){this.canvasContext.strokeStyle=t},CanvasRendererBase.prototype.ctxLineWidth=function(t){this.canvasContext.lineWidth=t},CanvasRendererBase.prototype.ctxLineCap=function(t){this.canvasContext.lineCap=t},CanvasRendererBase.prototype.ctxLineJoin=function(t){this.canvasContext.lineJoin=t},CanvasRendererBase.prototype.ctxMiterLimit=function(t){this.canvasContext.miterLimit=t},CanvasRendererBase.prototype.ctxFill=function(t){this.canvasContext.fill(t)},CanvasRendererBase.prototype.ctxFillRect=function(t,e,i,r){this.canvasContext.fillRect(t,e,i,r)},CanvasRendererBase.prototype.ctxStroke=function(){this.canvasContext.stroke()},CanvasRendererBase.prototype.reset=function(){this.renderConfig.clearCanvas?this.contextData.reset():this.canvasContext.restore()},CanvasRendererBase.prototype.save=function(){this.canvasContext.save()},CanvasRendererBase.prototype.restore=function(t){this.renderConfig.clearCanvas?(t&&(this.globalData.blendMode="source-over"),this.contextData.restore(t)):this.canvasContext.restore()},CanvasRendererBase.prototype.configAnimation=function(t){if(this.animationItem.wrapper){this.animationItem.container=createTag("canvas");var e=this.animationItem.container.style;e.width="100%",e.height="100%";var i="0px 0px 0px";e.transformOrigin=i,e.mozTransformOrigin=i,e.webkitTransformOrigin=i,e["-webkit-transform"]=i,e.contentVisibility=this.renderConfig.contentVisibility,this.animationItem.wrapper.appendChild(this.animationItem.container),this.canvasContext=this.animationItem.container.getContext("2d"),this.renderConfig.className&&this.animationItem.container.setAttribute("class",this.renderConfig.className),this.renderConfig.id&&this.animationItem.container.setAttribute("id",this.renderConfig.id)}else this.canvasContext=this.renderConfig.context;this.contextData.setContext(this.canvasContext),this.data=t,this.layers=t.layers,this.transformCanvas={w:t.w,h:t.h,sx:0,sy:0,tx:0,ty:0},this.setupGlobalData(t,document.body),this.globalData.canvasContext=this.canvasContext,this.globalData.renderer=this,this.globalData.isDashed=!1,this.globalData.progressiveLoad=this.renderConfig.progressiveLoad,this.globalData.transformCanvas=this.transformCanvas,this.elements=createSizedArray(t.layers.length),this.updateContainerSize()},CanvasRendererBase.prototype.updateContainerSize=function(t,e){var i,r,s,n;if(this.reset(),t?(i=t,r=e,this.canvasContext.canvas.width=i,this.canvasContext.canvas.height=r):(this.animationItem.wrapper&&this.animationItem.container?(i=this.animationItem.wrapper.offsetWidth,r=this.animationItem.wrapper.offsetHeight):(i=this.canvasContext.canvas.width,r=this.canvasContext.canvas.height),this.canvasContext.canvas.width=i*this.renderConfig.dpr,this.canvasContext.canvas.height=r*this.renderConfig.dpr),-1!==this.renderConfig.preserveAspectRatio.indexOf("meet")||-1!==this.renderConfig.preserveAspectRatio.indexOf("slice")){var a=this.renderConfig.preserveAspectRatio.split(" "),o=a[1]||"meet",h=a[0]||"xMidYMid",l=h.substr(0,4),p=h.substr(4);s=i/r,(n=this.transformCanvas.w/this.transformCanvas.h)>s&&"meet"===o||n<s&&"slice"===o?(this.transformCanvas.sx=i/(this.transformCanvas.w/this.renderConfig.dpr),this.transformCanvas.sy=i/(this.transformCanvas.w/this.renderConfig.dpr)):(this.transformCanvas.sx=r/(this.transformCanvas.h/this.renderConfig.dpr),this.transformCanvas.sy=r/(this.transformCanvas.h/this.renderConfig.dpr)),this.transformCanvas.tx="xMid"===l&&(n<s&&"meet"===o||n>s&&"slice"===o)?(i-this.transformCanvas.w*(r/this.transformCanvas.h))/2*this.renderConfig.dpr:"xMax"===l&&(n<s&&"meet"===o||n>s&&"slice"===o)?(i-this.transformCanvas.w*(r/this.transformCanvas.h))*this.renderConfig.dpr:0,this.transformCanvas.ty="YMid"===p&&(n>s&&"meet"===o||n<s&&"slice"===o)?(r-this.transformCanvas.h*(i/this.transformCanvas.w))/2*this.renderConfig.dpr:"YMax"===p&&(n>s&&"meet"===o||n<s&&"slice"===o)?(r-this.transformCanvas.h*(i/this.transformCanvas.w))*this.renderConfig.dpr:0}else"none"===this.renderConfig.preserveAspectRatio?(this.transformCanvas.sx=i/(this.transformCanvas.w/this.renderConfig.dpr),this.transformCanvas.sy=r/(this.transformCanvas.h/this.renderConfig.dpr)):(this.transformCanvas.sx=this.renderConfig.dpr,this.transformCanvas.sy=this.renderConfig.dpr),this.transformCanvas.tx=0,this.transformCanvas.ty=0;this.transformCanvas.props=[this.transformCanvas.sx,0,0,0,0,this.transformCanvas.sy,0,0,0,0,1,0,this.transformCanvas.tx,this.transformCanvas.ty,0,1],this.ctxTransform(this.transformCanvas.props),this.canvasContext.beginPath(),this.canvasContext.rect(0,0,this.transformCanvas.w,this.transformCanvas.h),this.canvasContext.closePath(),this.canvasContext.clip(),this.renderFrame(this.renderedFrame,!0)},CanvasRendererBase.prototype.destroy=function(){var t;for(this.renderConfig.clearCanvas&&this.animationItem.wrapper&&(this.animationItem.wrapper.innerText=""),t=(this.layers?this.layers.length:0)-1;t>=0;t-=1)this.elements[t]&&this.elements[t].destroy&&this.elements[t].destroy();this.elements.length=0,this.globalData.canvasContext=null,this.animationItem.container=null,this.destroyed=!0},CanvasRendererBase.prototype.renderFrame=function(t,e){if((this.renderedFrame!==t||!0!==this.renderConfig.clearCanvas||e)&&!this.destroyed&&-1!==t){this.renderedFrame=t,this.globalData.frameNum=t-this.animationItem._isFirstFrame,this.globalData.frameId+=1,this.globalData._mdf=!this.renderConfig.clearCanvas||e,this.globalData.projectInterface.currentFrame=t;var i,r=this.layers.length;for(this.completeLayers||this.checkLayers(t),i=r-1;i>=0;i-=1)(this.completeLayers||this.elements[i])&&this.elements[i].prepareFrame(t-this.layers[i].st);if(this.globalData._mdf){for(!0===this.renderConfig.clearCanvas?this.canvasContext.clearRect(0,0,this.transformCanvas.w,this.transformCanvas.h):this.save(),i=r-1;i>=0;i-=1)(this.completeLayers||this.elements[i])&&this.elements[i].renderFrame();!0!==this.renderConfig.clearCanvas&&this.restore()}}},CanvasRendererBase.prototype.buildItem=function(t){var e=this.elements;if(!e[t]&&99!==this.layers[t].ty){var i=this.createItem(this.layers[t],this,this.globalData);e[t]=i,i.initExpressions()}},CanvasRendererBase.prototype.checkPendingElements=function(){for(;this.pendingElements.length;)this.pendingElements.pop().checkParenting()},CanvasRendererBase.prototype.hide=function(){this.animationItem.container.style.display="none"},CanvasRendererBase.prototype.show=function(){this.animationItem.container.style.display="block"},CVContextData.prototype.duplicate=function(){var t=2*this._length,e=0;for(e=this._length;e<t;e+=1)this.stack[e]=new CanvasContext;this._length=t},CVContextData.prototype.reset=function(){this.cArrPos=0,this.cTr.reset(),this.stack[this.cArrPos].opacity=1},CVContextData.prototype.restore=function(t){this.cArrPos-=1;var e,i=this.stack[this.cArrPos],r=i.transform,s=this.cTr.props;for(e=0;e<16;e+=1)s[e]=r[e];if(t){this.nativeContext.restore();var n=this.stack[this.cArrPos+1];this.appliedFillStyle=n.fillStyle,this.appliedStrokeStyle=n.strokeStyle,this.appliedLineWidth=n.lineWidth,this.appliedLineCap=n.lineCap,this.appliedLineJoin=n.lineJoin,this.appliedMiterLimit=n.miterLimit}this.nativeContext.setTransform(r[0],r[1],r[4],r[5],r[12],r[13]),(t||-1!==i.opacity&&this.currentOpacity!==i.opacity)&&(this.nativeContext.globalAlpha=i.opacity,this.currentOpacity=i.opacity),this.currentFillStyle=i.fillStyle,this.currentStrokeStyle=i.strokeStyle,this.currentLineWidth=i.lineWidth,this.currentLineCap=i.lineCap,this.currentLineJoin=i.lineJoin,this.currentMiterLimit=i.miterLimit},CVContextData.prototype.save=function(t){t&&this.nativeContext.save();var e=this.cTr.props;this._length<=this.cArrPos&&this.duplicate();var i,r=this.stack[this.cArrPos];for(i=0;i<16;i+=1)r.transform[i]=e[i];this.cArrPos+=1;var s=this.stack[this.cArrPos];s.opacity=r.opacity,s.fillStyle=r.fillStyle,s.strokeStyle=r.strokeStyle,s.lineWidth=r.lineWidth,s.lineCap=r.lineCap,s.lineJoin=r.lineJoin,s.miterLimit=r.miterLimit},CVContextData.prototype.setOpacity=function(t){this.stack[this.cArrPos].opacity=t},CVContextData.prototype.setContext=function(t){this.nativeContext=t},CVContextData.prototype.fillStyle=function(t){this.stack[this.cArrPos].fillStyle!==t&&(this.currentFillStyle=t,this.stack[this.cArrPos].fillStyle=t)},CVContextData.prototype.strokeStyle=function(t){this.stack[this.cArrPos].strokeStyle!==t&&(this.currentStrokeStyle=t,this.stack[this.cArrPos].strokeStyle=t)},CVContextData.prototype.lineWidth=function(t){this.stack[this.cArrPos].lineWidth!==t&&(this.currentLineWidth=t,this.stack[this.cArrPos].lineWidth=t)},CVContextData.prototype.lineCap=function(t){this.stack[this.cArrPos].lineCap!==t&&(this.currentLineCap=t,this.stack[this.cArrPos].lineCap=t)},CVContextData.prototype.lineJoin=function(t){this.stack[this.cArrPos].lineJoin!==t&&(this.currentLineJoin=t,this.stack[this.cArrPos].lineJoin=t)},CVContextData.prototype.miterLimit=function(t){this.stack[this.cArrPos].miterLimit!==t&&(this.currentMiterLimit=t,this.stack[this.cArrPos].miterLimit=t)},CVContextData.prototype.transform=function(t){this.transformMat.cloneFromProps(t);var e=this.cTr;this.transformMat.multiply(e),e.cloneFromProps(this.transformMat.props);var i=e.props;this.nativeContext.setTransform(i[0],i[1],i[4],i[5],i[12],i[13])},CVContextData.prototype.opacity=function(t){var e=this.stack[this.cArrPos].opacity;e*=t<0?0:t,this.stack[this.cArrPos].opacity!==e&&(this.currentOpacity!==t&&(this.nativeContext.globalAlpha=t,this.currentOpacity=t),this.stack[this.cArrPos].opacity=e)},CVContextData.prototype.fill=function(t){this.appliedFillStyle!==this.currentFillStyle&&(this.appliedFillStyle=this.currentFillStyle,this.nativeContext.fillStyle=this.appliedFillStyle),this.nativeContext.fill(t)},CVContextData.prototype.fillRect=function(t,e,i,r){this.appliedFillStyle!==this.currentFillStyle&&(this.appliedFillStyle=this.currentFillStyle,this.nativeContext.fillStyle=this.appliedFillStyle),this.nativeContext.fillRect(t,e,i,r)},CVContextData.prototype.stroke=function(){this.appliedStrokeStyle!==this.currentStrokeStyle&&(this.appliedStrokeStyle=this.currentStrokeStyle,this.nativeContext.strokeStyle=this.appliedStrokeStyle),this.appliedLineWidth!==this.currentLineWidth&&(this.appliedLineWidth=this.currentLineWidth,this.nativeContext.lineWidth=this.appliedLineWidth),this.appliedLineCap!==this.currentLineCap&&(this.appliedLineCap=this.currentLineCap,this.nativeContext.lineCap=this.appliedLineCap),this.appliedLineJoin!==this.currentLineJoin&&(this.appliedLineJoin=this.currentLineJoin,this.nativeContext.lineJoin=this.appliedLineJoin),this.appliedMiterLimit!==this.currentMiterLimit&&(this.appliedMiterLimit=this.currentMiterLimit,this.nativeContext.miterLimit=this.appliedMiterLimit),this.nativeContext.stroke()},extendPrototype([CanvasRendererBase,ICompElement,CVBaseElement],CVCompElement),CVCompElement.prototype.renderInnerContent=function(){var t,e=this.canvasContext;for(e.beginPath(),e.moveTo(0,0),e.lineTo(this.data.w,0),e.lineTo(this.data.w,this.data.h),e.lineTo(0,this.data.h),e.lineTo(0,0),e.clip(),t=this.layers.length-1;t>=0;t-=1)(this.completeLayers||this.elements[t])&&this.elements[t].renderFrame()},CVCompElement.prototype.destroy=function(){var t;for(t=this.layers.length-1;t>=0;t-=1)this.elements[t]&&this.elements[t].destroy();this.layers=null,this.elements=null},CVCompElement.prototype.createComp=function(t){return new CVCompElement(t,this.globalData,this)},extendPrototype([CanvasRendererBase],CanvasRenderer),CanvasRenderer.prototype.createComp=function(t){return new CVCompElement(t,this.globalData,this)},HBaseElement.prototype={checkBlendMode:function(){},initRendererElement:function(){this.baseElement=createTag(this.data.tg||"div"),this.data.hasMask?(this.svgElement=createNS("svg"),this.layerElement=createNS("g"),this.maskedElement=this.layerElement,this.svgElement.appendChild(this.layerElement),this.baseElement.appendChild(this.svgElement)):this.layerElement=this.baseElement,styleDiv(this.baseElement)},createContainerElements:function(){this.renderableEffectsManager=new CVEffects(this),this.transformedElement=this.baseElement,this.maskedElement=this.layerElement,this.data.ln&&this.layerElement.setAttribute("id",this.data.ln),this.data.cl&&this.layerElement.setAttribute("class",this.data.cl),0!==this.data.bm&&this.setBlendMode()},renderElement:function(){var t=this.transformedElement?this.transformedElement.style:{};if(this.finalTransform._matMdf){var e=this.finalTransform.mat.toCSS();t.transform=e,t.webkitTransform=e}this.finalTransform._opMdf&&(t.opacity=this.finalTransform.mProp.o.v)},renderFrame:function(){this.data.hd||this.hidden||(this.renderTransform(),this.renderRenderable(),this.renderElement(),this.renderInnerContent(),this._isFirstFrame&&(this._isFirstFrame=!1))},destroy:function(){this.layerElement=null,this.transformedElement=null,this.matteElement&&(this.matteElement=null),this.maskManager&&(this.maskManager.destroy(),this.maskManager=null)},createRenderableComponents:function(){this.maskManager=new MaskElement(this.data,this,this.globalData)},addEffects:function(){},setMatte:function(){}},HBaseElement.prototype.getBaseElement=SVGBaseElement.prototype.getBaseElement,HBaseElement.prototype.destroyBaseElement=HBaseElement.prototype.destroy,HBaseElement.prototype.buildElementParenting=BaseRenderer.prototype.buildElementParenting,extendPrototype([BaseElement,TransformElement,HBaseElement,HierarchyElement,FrameElement,RenderableDOMElement],HSolidElement),HSolidElement.prototype.createContent=function(){var t;this.data.hasMask?((t=createNS("rect")).setAttribute("width",this.data.sw),t.setAttribute("height",this.data.sh),t.setAttribute("fill",this.data.sc),this.svgElement.setAttribute("width",this.data.sw),this.svgElement.setAttribute("height",this.data.sh)):((t=createTag("div")).style.width=this.data.sw+"px",t.style.height=this.data.sh+"px",t.style.backgroundColor=this.data.sc),this.layerElement.appendChild(t)},extendPrototype([BaseElement,TransformElement,HSolidElement,SVGShapeElement,HBaseElement,HierarchyElement,FrameElement,RenderableElement],HShapeElement),HShapeElement.prototype._renderShapeFrame=HShapeElement.prototype.renderInnerContent,HShapeElement.prototype.createContent=function(){var t;if(this.baseElement.style.fontSize=0,this.data.hasMask)this.layerElement.appendChild(this.shapesContainer),t=this.svgElement;else{t=createNS("svg");var e=this.comp.data?this.comp.data:this.globalData.compSize;t.setAttribute("width",e.w),t.setAttribute("height",e.h),t.appendChild(this.shapesContainer),this.layerElement.appendChild(t)}this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.shapesContainer,0,[],!0),this.filterUniqueShapes(),this.shapeCont=t},HShapeElement.prototype.getTransformedPoint=function(t,e){var i,r=t.length;for(i=0;i<r;i+=1)e=t[i].mProps.v.applyToPointArray(e[0],e[1],0);return e},HShapeElement.prototype.calculateShapeBoundingBox=function(t,e){var i,r,s,n,a,o=t.sh.v,h=t.transformers,l=o._length;if(!(l<=1)){for(i=0;i<l-1;i+=1)r=this.getTransformedPoint(h,o.v[i]),s=this.getTransformedPoint(h,o.o[i]),n=this.getTransformedPoint(h,o.i[i+1]),a=this.getTransformedPoint(h,o.v[i+1]),this.checkBounds(r,s,n,a,e);o.c&&(r=this.getTransformedPoint(h,o.v[i]),s=this.getTransformedPoint(h,o.o[i]),n=this.getTransformedPoint(h,o.i[0]),a=this.getTransformedPoint(h,o.v[0]),this.checkBounds(r,s,n,a,e))}},HShapeElement.prototype.checkBounds=function(t,e,i,r,s){this.getBoundsOfCurve(t,e,i,r);var n=this.shapeBoundingBox;s.x=bmMin(n.left,s.x),s.xMax=bmMax(n.right,s.xMax),s.y=bmMin(n.top,s.y),s.yMax=bmMax(n.bottom,s.yMax)},HShapeElement.prototype.shapeBoundingBox={left:0,right:0,top:0,bottom:0},HShapeElement.prototype.tempBoundingBox={x:0,xMax:0,y:0,yMax:0,width:0,height:0},HShapeElement.prototype.getBoundsOfCurve=function(t,e,i,r){for(var s,n,a,o,h,l,p,f=[[t[0],r[0]],[t[1],r[1]]],u=0;u<2;++u)n=6*t[u]-12*e[u]+6*i[u],s=-3*t[u]+9*e[u]-9*i[u]+3*r[u],a=3*e[u]-3*t[u],n|=0,a|=0,0==(s|=0)&&0===n||(0===s?(o=-a/n)>0&&o<1&&f[u].push(this.calculateF(o,t,e,i,r,u)):(h=n*n-4*a*s)>=0&&((l=(-n+bmSqrt(h))/(2*s))>0&&l<1&&f[u].push(this.calculateF(l,t,e,i,r,u)),(p=(-n-bmSqrt(h))/(2*s))>0&&p<1&&f[u].push(this.calculateF(p,t,e,i,r,u))));this.shapeBoundingBox.left=bmMin.apply(null,f[0]),this.shapeBoundingBox.top=bmMin.apply(null,f[1]),this.shapeBoundingBox.right=bmMax.apply(null,f[0]),this.shapeBoundingBox.bottom=bmMax.apply(null,f[1])},HShapeElement.prototype.calculateF=function(t,e,i,r,s,n){return bmPow(1-t,3)*e[n]+3*bmPow(1-t,2)*t*i[n]+3*(1-t)*bmPow(t,2)*r[n]+bmPow(t,3)*s[n]},HShapeElement.prototype.calculateBoundingBox=function(t,e){var i,r=t.length;for(i=0;i<r;i+=1)t[i]&&t[i].sh?this.calculateShapeBoundingBox(t[i],e):t[i]&&t[i].it?this.calculateBoundingBox(t[i].it,e):t[i]&&t[i].style&&t[i].w&&this.expandStrokeBoundingBox(t[i].w,e)},HShapeElement.prototype.expandStrokeBoundingBox=function(t,e){var i=0;if(t.keyframes){for(var r=0;r<t.keyframes.length;r+=1){var s=t.keyframes[r].s;s>i&&(i=s)}i*=t.mult}else i=t.v*t.mult;e.x-=i,e.xMax+=i,e.y-=i,e.yMax+=i},HShapeElement.prototype.currentBoxContains=function(t){return this.currentBBox.x<=t.x&&this.currentBBox.y<=t.y&&this.currentBBox.width+this.currentBBox.x>=t.x+t.width&&this.currentBBox.height+this.currentBBox.y>=t.y+t.height},HShapeElement.prototype.renderInnerContent=function(){if(this._renderShapeFrame(),!this.hidden&&(this._isFirstFrame||this._mdf)){var t=this.tempBoundingBox,e=999999;if(t.x=999999,t.xMax=-e,t.y=e,t.yMax=-e,this.calculateBoundingBox(this.itemsData,t),t.width=t.xMax<t.x?0:t.xMax-t.x,t.height=t.yMax<t.y?0:t.yMax-t.y,!this.currentBoxContains(t)){var i=!1;if(this.currentBBox.w!==t.width&&(this.currentBBox.w=t.width,this.shapeCont.setAttribute("width",t.width),i=!0),this.currentBBox.h!==t.height&&(this.currentBBox.h=t.height,this.shapeCont.setAttribute("height",t.height),i=!0),i||this.currentBBox.x!==t.x||this.currentBBox.y!==t.y){this.currentBBox.w=t.width,this.currentBBox.h=t.height,this.currentBBox.x=t.x,this.currentBBox.y=t.y,this.shapeCont.setAttribute("viewBox",this.currentBBox.x+" "+this.currentBBox.y+" "+this.currentBBox.w+" "+this.currentBBox.h);var r=this.shapeCont.style,s="translate("+this.currentBBox.x+"px,"+this.currentBBox.y+"px)";r.transform=s,r.webkitTransform=s}}}},extendPrototype([BaseElement,TransformElement,HBaseElement,HierarchyElement,FrameElement,RenderableDOMElement,ITextElement],HTextElement),HTextElement.prototype.createContent=function(){if(this.isMasked=this.checkMasks(),this.isMasked){this.renderType="svg",this.compW=this.comp.data.w,this.compH=this.comp.data.h,this.svgElement.setAttribute("width",this.compW),this.svgElement.setAttribute("height",this.compH);var t=createNS("g");this.maskedElement.appendChild(t),this.innerElem=t}else this.renderType="html",this.innerElem=this.layerElement;this.checkParenting()},HTextElement.prototype.buildNewText=function(){var t=this.textProperty.currentData;this.renderedLetters=createSizedArray(t.l?t.l.length:0);var e=this.innerElem.style,i=t.fc?this.buildColor(t.fc):"rgba(0,0,0,0)";e.fill=i,e.color=i,t.sc&&(e.stroke=this.buildColor(t.sc),e.strokeWidth=t.sw+"px");var r,s,n=this.globalData.fontManager.getFontByName(t.f);if(!this.globalData.fontManager.chars)if(e.fontSize=t.finalSize+"px",e.lineHeight=t.finalSize+"px",n.fClass)this.innerElem.className=n.fClass;else{e.fontFamily=n.fFamily;var a=t.fWeight;e.fontStyle=t.fStyle,e.fontWeight=a}var o,h,l,p=t.l;s=p.length;var f,u=this.mHelper,c="",m=0;for(r=0;r<s;r+=1){if(this.globalData.fontManager.chars?(this.textPaths[m]?o=this.textPaths[m]:((o=createNS("path")).setAttribute("stroke-linecap",lineCapEnum[1]),o.setAttribute("stroke-linejoin",lineJoinEnum[2]),o.setAttribute("stroke-miterlimit","4")),this.isMasked||(this.textSpans[m]?l=(h=this.textSpans[m]).children[0]:((h=createTag("div")).style.lineHeight=0,(l=createNS("svg")).appendChild(o),styleDiv(h)))):this.isMasked?o=this.textPaths[m]?this.textPaths[m]:createNS("text"):this.textSpans[m]?(h=this.textSpans[m],o=this.textPaths[m]):(styleDiv(h=createTag("span")),styleDiv(o=createTag("span")),h.appendChild(o)),this.globalData.fontManager.chars){var d,g=this.globalData.fontManager.getCharData(t.finalText[r],n.fStyle,this.globalData.fontManager.getFontByName(t.f).fFamily);if(d=g?g.data:null,u.reset(),d&&d.shapes&&d.shapes.length&&(f=d.shapes[0].it,u.scale(t.finalSize/100,t.finalSize/100),c=this.createPathShape(u,f),o.setAttribute("d",c)),this.isMasked)this.innerElem.appendChild(o);else{if(this.innerElem.appendChild(h),d&&d.shapes){document.body.appendChild(l);var v=l.getBBox();l.setAttribute("width",v.width+2),l.setAttribute("height",v.height+2),l.setAttribute("viewBox",v.x-1+" "+(v.y-1)+" "+(v.width+2)+" "+(v.height+2));var y=l.style,b="translate("+(v.x-1)+"px,"+(v.y-1)+"px)";y.transform=b,y.webkitTransform=b,p[r].yOffset=v.y-1}else l.setAttribute("width",1),l.setAttribute("height",1);h.appendChild(l)}}else if(o.textContent=p[r].val,o.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),this.isMasked)this.innerElem.appendChild(o);else{this.innerElem.appendChild(h);var x=o.style,_="translate3d(0,"+-t.finalSize/1.2+"px,0)";x.transform=_,x.webkitTransform=_}this.isMasked?this.textSpans[m]=o:this.textSpans[m]=h,this.textSpans[m].style.display="block",this.textPaths[m]=o,m+=1}for(;m<this.textSpans.length;)this.textSpans[m].style.display="none",m+=1},HTextElement.prototype.renderInnerContent=function(){var t;if(this.validateText(),this.data.singleShape){if(!this._isFirstFrame&&!this.lettersChangedFlag)return;if(this.isMasked&&this.finalTransform._matMdf){this.svgElement.setAttribute("viewBox",-this.finalTransform.mProp.p.v[0]+" "+-this.finalTransform.mProp.p.v[1]+" "+this.compW+" "+this.compH),t=this.svgElement.style;var e="translate("+-this.finalTransform.mProp.p.v[0]+"px,"+-this.finalTransform.mProp.p.v[1]+"px)";t.transform=e,t.webkitTransform=e}}if(this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag),this.lettersChangedFlag||this.textAnimator.lettersChangedFlag){var i,r,s,n,a,o=0,h=this.textAnimator.renderedLetters,l=this.textProperty.currentData.l;for(r=l.length,i=0;i<r;i+=1)l[i].n?o+=1:(n=this.textSpans[i],a=this.textPaths[i],s=h[o],o+=1,s._mdf.m&&(this.isMasked?n.setAttribute("transform",s.m):(n.style.webkitTransform=s.m,n.style.transform=s.m)),n.style.opacity=s.o,s.sw&&s._mdf.sw&&a.setAttribute("stroke-width",s.sw),s.sc&&s._mdf.sc&&a.setAttribute("stroke",s.sc),s.fc&&s._mdf.fc&&(a.setAttribute("fill",s.fc),a.style.color=s.fc));if(this.innerElem.getBBox&&!this.hidden&&(this._isFirstFrame||this._mdf)){var p=this.innerElem.getBBox();if(this.currentBBox.w!==p.width&&(this.currentBBox.w=p.width,this.svgElement.setAttribute("width",p.width)),this.currentBBox.h!==p.height&&(this.currentBBox.h=p.height,this.svgElement.setAttribute("height",p.height)),this.currentBBox.w!==p.width+2||this.currentBBox.h!==p.height+2||this.currentBBox.x!==p.x-1||this.currentBBox.y!==p.y-1){this.currentBBox.w=p.width+2,this.currentBBox.h=p.height+2,this.currentBBox.x=p.x-1,this.currentBBox.y=p.y-1,this.svgElement.setAttribute("viewBox",this.currentBBox.x+" "+this.currentBBox.y+" "+this.currentBBox.w+" "+this.currentBBox.h),t=this.svgElement.style;var f="translate("+this.currentBBox.x+"px,"+this.currentBBox.y+"px)";t.transform=f,t.webkitTransform=f}}}},extendPrototype([BaseElement,FrameElement,HierarchyElement],HCameraElement),HCameraElement.prototype.setup=function(){var t,e,i,r,s=this.comp.threeDElements.length;for(t=0;t<s;t+=1)if("3d"===(e=this.comp.threeDElements[t]).type){i=e.perspectiveElem.style,r=e.container.style;var n=this.pe.v+"px",a="0px 0px 0px",o="matrix3d(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)";i.perspective=n,i.webkitPerspective=n,r.transformOrigin=a,r.mozTransformOrigin=a,r.webkitTransformOrigin=a,i.transform=o,i.webkitTransform=o}},HCameraElement.prototype.createElements=function(){},HCameraElement.prototype.hide=function(){},HCameraElement.prototype.renderFrame=function(){var t,e,i=this._isFirstFrame;if(this.hierarchy)for(e=this.hierarchy.length,t=0;t<e;t+=1)i=this.hierarchy[t].finalTransform.mProp._mdf||i;if(i||this.pe._mdf||this.p&&this.p._mdf||this.px&&(this.px._mdf||this.py._mdf||this.pz._mdf)||this.rx._mdf||this.ry._mdf||this.rz._mdf||this.or._mdf||this.a&&this.a._mdf){if(this.mat.reset(),this.hierarchy)for(t=e=this.hierarchy.length-1;t>=0;t-=1){var r,s,n,a=this.hierarchy[t].finalTransform.mProp;this.mat.translate(-a.p.v[0],-a.p.v[1],a.p.v[2]),this.mat.rotateX(-a.or.v[0]).rotateY(-a.or.v[1]).rotateZ(a.or.v[2]),this.mat.rotateX(-a.rx.v).rotateY(-a.ry.v).rotateZ(a.rz.v),this.mat.scale(1/a.s.v[0],1/a.s.v[1],1/a.s.v[2]),this.mat.translate(a.a.v[0],a.a.v[1],a.a.v[2])}if(this.p?this.mat.translate(-this.p.v[0],-this.p.v[1],this.p.v[2]):this.mat.translate(-this.px.v,-this.py.v,this.pz.v),this.a){var o=this.p?[this.p.v[0]-this.a.v[0],this.p.v[1]-this.a.v[1],this.p.v[2]-this.a.v[2]]:[this.px.v-this.a.v[0],this.py.v-this.a.v[1],this.pz.v-this.a.v[2]],h=Math.sqrt(Math.pow(o[0],2)+Math.pow(o[1],2)+Math.pow(o[2],2)),l=[o[0]/h,o[1]/h,o[2]/h],p=Math.sqrt(l[2]*l[2]+l[0]*l[0]),f=Math.atan2(l[1],p),u=Math.atan2(l[0],-l[2]);this.mat.rotateY(u).rotateX(-f)}this.mat.rotateX(-this.rx.v).rotateY(-this.ry.v).rotateZ(this.rz.v),this.mat.rotateX(-this.or.v[0]).rotateY(-this.or.v[1]).rotateZ(this.or.v[2]),this.mat.translate(this.globalData.compSize.w/2,this.globalData.compSize.h/2,0),this.mat.translate(0,0,this.pe.v);var c=!this._prevMat.equals(this.mat);if((c||this.pe._mdf)&&this.comp.threeDElements){for(e=this.comp.threeDElements.length,t=0;t<e;t+=1)if("3d"===(r=this.comp.threeDElements[t]).type){if(c){var m=this.mat.toCSS();(n=r.container.style).transform=m,n.webkitTransform=m}this.pe._mdf&&((s=r.perspectiveElem.style).perspective=this.pe.v+"px",s.webkitPerspective=this.pe.v+"px")}this.mat.clone(this._prevMat)}}this._isFirstFrame=!1},HCameraElement.prototype.prepareFrame=function(t){this.prepareProperties(t,!0)},HCameraElement.prototype.destroy=function(){},HCameraElement.prototype.getBaseElement=function(){return null},extendPrototype([BaseElement,TransformElement,HBaseElement,HSolidElement,HierarchyElement,FrameElement,RenderableElement],HImageElement),HImageElement.prototype.createContent=function(){var t=this.globalData.getAssetsPath(this.assetData),e=new Image;this.data.hasMask?(this.imageElem=createNS("image"),this.imageElem.setAttribute("width",this.assetData.w+"px"),this.imageElem.setAttribute("height",this.assetData.h+"px"),this.imageElem.setAttributeNS("http://www.w3.org/1999/xlink","href",t),this.layerElement.appendChild(this.imageElem),this.baseElement.setAttribute("width",this.assetData.w),this.baseElement.setAttribute("height",this.assetData.h)):this.layerElement.appendChild(e),e.crossOrigin="anonymous",e.src=t,this.data.ln&&this.baseElement.setAttribute("id",this.data.ln)},extendPrototype([BaseRenderer],HybridRendererBase),HybridRendererBase.prototype.buildItem=SVGRenderer.prototype.buildItem,HybridRendererBase.prototype.checkPendingElements=function(){for(;this.pendingElements.length;)this.pendingElements.pop().checkParenting()},HybridRendererBase.prototype.appendElementInPos=function(t,e){var i=t.getBaseElement();if(i){var r=this.layers[e];if(r.ddd&&this.supports3d)this.addTo3dContainer(i,e);else if(this.threeDElements)this.addTo3dContainer(i,e);else{for(var s,n,a=0;a<e;)this.elements[a]&&!0!==this.elements[a]&&this.elements[a].getBaseElement&&(n=this.elements[a],s=(this.layers[a].ddd?this.getThreeDContainerByPos(a):n.getBaseElement())||s),a+=1;s?r.ddd&&this.supports3d||this.layerElement.insertBefore(i,s):r.ddd&&this.supports3d||this.layerElement.appendChild(i)}}},HybridRendererBase.prototype.createShape=function(t){return this.supports3d?new HShapeElement(t,this.globalData,this):new SVGShapeElement(t,this.globalData,this)},HybridRendererBase.prototype.createText=function(t){return this.supports3d?new HTextElement(t,this.globalData,this):new SVGTextLottieElement(t,this.globalData,this)},HybridRendererBase.prototype.createCamera=function(t){return this.camera=new HCameraElement(t,this.globalData,this),this.camera},HybridRendererBase.prototype.createImage=function(t){return this.supports3d?new HImageElement(t,this.globalData,this):new IImageElement(t,this.globalData,this)},HybridRendererBase.prototype.createSolid=function(t){return this.supports3d?new HSolidElement(t,this.globalData,this):new ISolidElement(t,this.globalData,this)},HybridRendererBase.prototype.createNull=SVGRenderer.prototype.createNull,HybridRendererBase.prototype.getThreeDContainerByPos=function(t){for(var e=0,i=this.threeDElements.length;e<i;){if(this.threeDElements[e].startPos<=t&&this.threeDElements[e].endPos>=t)return this.threeDElements[e].perspectiveElem;e+=1}return null},HybridRendererBase.prototype.createThreeDContainer=function(t,e){var i,r,s=createTag("div");styleDiv(s);var n=createTag("div");if(styleDiv(n),"3d"===e){(i=s.style).width=this.globalData.compSize.w+"px",i.height=this.globalData.compSize.h+"px";var a="50% 50%";i.webkitTransformOrigin=a,i.mozTransformOrigin=a,i.transformOrigin=a;var o="matrix3d(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)";(r=n.style).transform=o,r.webkitTransform=o}s.appendChild(n);var h={container:n,perspectiveElem:s,startPos:t,endPos:t,type:e};return this.threeDElements.push(h),h},HybridRendererBase.prototype.build3dContainers=function(){var t,e,i=this.layers.length,r="";for(t=0;t<i;t+=1)this.layers[t].ddd&&3!==this.layers[t].ty?"3d"!==r&&(r="3d",e=this.createThreeDContainer(t,"3d")):"2d"!==r&&(r="2d",e=this.createThreeDContainer(t,"2d")),e.endPos=Math.max(e.endPos,t);for(t=(i=this.threeDElements.length)-1;t>=0;t-=1)this.resizerElem.appendChild(this.threeDElements[t].perspectiveElem)},HybridRendererBase.prototype.addTo3dContainer=function(t,e){for(var i=0,r=this.threeDElements.length;i<r;){if(e<=this.threeDElements[i].endPos){for(var s,n=this.threeDElements[i].startPos;n<e;)this.elements[n]&&this.elements[n].getBaseElement&&(s=this.elements[n].getBaseElement()),n+=1;s?this.threeDElements[i].container.insertBefore(t,s):this.threeDElements[i].container.appendChild(t);break}i+=1}},HybridRendererBase.prototype.configAnimation=function(t){var e=createTag("div"),i=this.animationItem.wrapper,r=e.style;r.width=t.w+"px",r.height=t.h+"px",this.resizerElem=e,styleDiv(e),r.transformStyle="flat",r.mozTransformStyle="flat",r.webkitTransformStyle="flat",this.renderConfig.className&&e.setAttribute("class",this.renderConfig.className),i.appendChild(e),r.overflow="hidden";var s=createNS("svg");s.setAttribute("width","1"),s.setAttribute("height","1"),styleDiv(s),this.resizerElem.appendChild(s);var n=createNS("defs");s.appendChild(n),this.data=t,this.setupGlobalData(t,s),this.globalData.defs=n,this.layers=t.layers,this.layerElement=this.resizerElem,this.build3dContainers(),this.updateContainerSize()},HybridRendererBase.prototype.destroy=function(){this.animationItem.wrapper&&(this.animationItem.wrapper.innerText=""),this.animationItem.container=null,this.globalData.defs=null;var t,e=this.layers?this.layers.length:0;for(t=0;t<e;t+=1)this.elements[t]&&this.elements[t].destroy&&this.elements[t].destroy();this.elements.length=0,this.destroyed=!0,this.animationItem=null},HybridRendererBase.prototype.updateContainerSize=function(){var t,e,i,r,s=this.animationItem.wrapper.offsetWidth,n=this.animationItem.wrapper.offsetHeight,a=s/n;this.globalData.compSize.w/this.globalData.compSize.h>a?(t=s/this.globalData.compSize.w,e=s/this.globalData.compSize.w,i=0,r=(n-this.globalData.compSize.h*(s/this.globalData.compSize.w))/2):(t=n/this.globalData.compSize.h,e=n/this.globalData.compSize.h,i=(s-this.globalData.compSize.w*(n/this.globalData.compSize.h))/2,r=0);var o=this.resizerElem.style;o.webkitTransform="matrix3d("+t+",0,0,0,0,"+e+",0,0,0,0,1,0,"+i+","+r+",0,1)",o.transform=o.webkitTransform},HybridRendererBase.prototype.renderFrame=SVGRenderer.prototype.renderFrame,HybridRendererBase.prototype.hide=function(){this.resizerElem.style.display="none"},HybridRendererBase.prototype.show=function(){this.resizerElem.style.display="block"},HybridRendererBase.prototype.initItems=function(){if(this.buildAllItems(),this.camera)this.camera.setup();else{var t,e=this.globalData.compSize.w,i=this.globalData.compSize.h,r=this.threeDElements.length;for(t=0;t<r;t+=1){var s=this.threeDElements[t].perspectiveElem.style;s.webkitPerspective=Math.sqrt(Math.pow(e,2)+Math.pow(i,2))+"px",s.perspective=s.webkitPerspective}}},HybridRendererBase.prototype.searchExtraCompositions=function(t){var e,i=t.length,r=createTag("div");for(e=0;e<i;e+=1)if(t[e].xt){var s=this.createComp(t[e],r,this.globalData.comp,null);s.initExpressions(),this.globalData.projectInterface.registerComposition(s)}},extendPrototype([HybridRendererBase,ICompElement,HBaseElement],HCompElement),HCompElement.prototype._createBaseContainerElements=HCompElement.prototype.createContainerElements,HCompElement.prototype.createContainerElements=function(){this._createBaseContainerElements(),this.data.hasMask?(this.svgElement.setAttribute("width",this.data.w),this.svgElement.setAttribute("height",this.data.h),this.transformedElement=this.baseElement):this.transformedElement=this.layerElement},HCompElement.prototype.addTo3dContainer=function(t,e){for(var i,r=0;r<e;)this.elements[r]&&this.elements[r].getBaseElement&&(i=this.elements[r].getBaseElement()),r+=1;i?this.layerElement.insertBefore(t,i):this.layerElement.appendChild(t)},HCompElement.prototype.createComp=function(t){return this.supports3d?new HCompElement(t,this.globalData,this):new SVGCompElement(t,this.globalData,this)},extendPrototype([HybridRendererBase],HybridRenderer),HybridRenderer.prototype.createComp=function(t){return this.supports3d?new HCompElement(t,this.globalData,this):new SVGCompElement(t,this.globalData,this)};var CompExpressionInterface=function(t){function e(e){for(var i=0,r=t.layers.length;i<r;){if(t.layers[i].nm===e||t.layers[i].ind===e)return t.elements[i].layerInterface;i+=1}return null}return Object.defineProperty(e,"_name",{value:t.data.nm}),e.layer=e,e.pixelAspect=1,e.height=t.data.h||t.globalData.compSize.h,e.width=t.data.w||t.globalData.compSize.w,e.pixelAspect=1,e.frameDuration=1/t.globalData.frameRate,e.displayStartTime=0,e.numLayers=t.layers.length,e};function _typeof$2(t){return(_typeof$2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function seedRandom(t,e){var i=this,r=256,s=e.pow(r,6),n=e.pow(2,52),a=2*n,o=255;function h(t){var e,i=t.length,s=this,n=0,a=s.i=s.j=0,h=s.S=[];for(i||(t=[i++]);n<r;)h[n]=n++;for(n=0;n<r;n++)h[n]=h[a=o&a+t[n%i]+(e=h[n])],h[a]=e;s.g=function(t){for(var e,i=0,n=s.i,a=s.j,h=s.S;t--;)e=h[n=o&n+1],i=i*r+h[o&(h[n]=h[a=o&a+e])+(h[a]=e)];return s.i=n,s.j=a,i}}function l(t,e){return e.i=t.i,e.j=t.j,e.S=t.S.slice(),e}function p(t,e){var i,r=[],s=_typeof$2(t);if(e&&"object"==s)for(i in t)try{r.push(p(t[i],e-1))}catch(t){}return r.length?r:"string"==s?t:t+"\0"}function f(t,e){for(var i,r=t+"",s=0;s<r.length;)e[o&s]=o&(i^=19*e[o&s])+r.charCodeAt(s++);return u(e)}function u(t){return String.fromCharCode.apply(0,t)}e.seedrandom=function(o,c,m){var d=[],g=f(p((c=!0===c?{entropy:!0}:c||{}).entropy?[o,u(t)]:null===o?function(){try{var e=new Uint8Array(r);return(i.crypto||i.msCrypto).getRandomValues(e),u(e)}catch(e){var s=i.navigator,n=s&&s.plugins;return[+new Date,i,n,i.screen,u(t)]}}():o,3),d),v=new h(d),y=function(){for(var t=v.g(6),e=s,i=0;t<n;)t=(t+i)*r,e*=r,i=v.g(1);for(;t>=a;)t/=2,e/=2,i>>>=1;return(t+i)/e};return y.int32=function(){return 0|v.g(4)},y.quick=function(){return v.g(4)/0x100000000},y.double=y,f(u(v.S),t),(c.pass||m||function(t,i,r,s){return s&&(s.S&&l(s,v),t.state=function(){return l(v,{})}),r?(e.random=t,i):t})(y,g,"global"in c?c.global:this==e,c.state)},f(e.random(),t)}function initialize$2(t){seedRandom([],t)}var propTypes={SHAPE:"shape"};function _typeof$1(t){return(_typeof$1="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var ExpressionManager=function(){var ob={},Math=BMMath,window=null,document=null,XMLHttpRequest=null,fetch=null,frames=null,_lottieGlobal={};function resetFrame(){_lottieGlobal={}}function $bm_isInstanceOfArray(t){return t.constructor===Array||t.constructor===Float32Array}function isNumerable(t,e){return"number"===t||e instanceof Number||"boolean"===t||"string"===t}function $bm_neg(t){var e=_typeof$1(t);if("number"===e||t instanceof Number||"boolean"===e)return-t;if($bm_isInstanceOfArray(t)){var i,r=t.length,s=[];for(i=0;i<r;i+=1)s[i]=-t[i];return s}return t.propType?t.v:-t}initialize$2(BMMath);var easeInBez=BezierFactory.getBezierEasing(.333,0,.833,.833,"easeIn").get,easeOutBez=BezierFactory.getBezierEasing(.167,.167,.667,1,"easeOut").get,easeInOutBez=BezierFactory.getBezierEasing(.33,0,.667,1,"easeInOut").get;function sum(t,e){var i=_typeof$1(t),r=_typeof$1(e);if(isNumerable(i,t)&&isNumerable(r,e)||"string"===i||"string"===r)return t+e;if($bm_isInstanceOfArray(t)&&isNumerable(r,e))return(t=t.slice(0))[0]+=e,t;if(isNumerable(i,t)&&$bm_isInstanceOfArray(e))return(e=e.slice(0))[0]=t+e[0],e;if($bm_isInstanceOfArray(t)&&$bm_isInstanceOfArray(e)){for(var s=0,n=t.length,a=e.length,o=[];s<n||s<a;)("number"==typeof t[s]||t[s]instanceof Number)&&("number"==typeof e[s]||e[s]instanceof Number)?o[s]=t[s]+e[s]:o[s]=void 0===e[s]?t[s]:t[s]||e[s],s+=1;return o}return 0}var add=sum;function sub(t,e){var i=_typeof$1(t),r=_typeof$1(e);if(isNumerable(i,t)&&isNumerable(r,e))return"string"===i&&(t=parseInt(t,10)),"string"===r&&(e=parseInt(e,10)),t-e;if($bm_isInstanceOfArray(t)&&isNumerable(r,e))return(t=t.slice(0))[0]-=e,t;if(isNumerable(i,t)&&$bm_isInstanceOfArray(e))return(e=e.slice(0))[0]=t-e[0],e;if($bm_isInstanceOfArray(t)&&$bm_isInstanceOfArray(e)){for(var s=0,n=t.length,a=e.length,o=[];s<n||s<a;)("number"==typeof t[s]||t[s]instanceof Number)&&("number"==typeof e[s]||e[s]instanceof Number)?o[s]=t[s]-e[s]:o[s]=void 0===e[s]?t[s]:t[s]||e[s],s+=1;return o}return 0}function mul(t,e){var i,r,s,n=_typeof$1(t),a=_typeof$1(e);if(isNumerable(n,t)&&isNumerable(a,e))return t*e;if($bm_isInstanceOfArray(t)&&isNumerable(a,e)){for(i=createTypedArray("float32",s=t.length),r=0;r<s;r+=1)i[r]=t[r]*e;return i}if(isNumerable(n,t)&&$bm_isInstanceOfArray(e)){for(i=createTypedArray("float32",s=e.length),r=0;r<s;r+=1)i[r]=t*e[r];return i}return 0}function div(t,e){var i,r,s,n=_typeof$1(t),a=_typeof$1(e);if(isNumerable(n,t)&&isNumerable(a,e))return t/e;if($bm_isInstanceOfArray(t)&&isNumerable(a,e)){for(i=createTypedArray("float32",s=t.length),r=0;r<s;r+=1)i[r]=t[r]/e;return i}if(isNumerable(n,t)&&$bm_isInstanceOfArray(e)){for(i=createTypedArray("float32",s=e.length),r=0;r<s;r+=1)i[r]=t/e[r];return i}return 0}function mod(t,e){return"string"==typeof t&&(t=parseInt(t,10)),"string"==typeof e&&(e=parseInt(e,10)),t%e}var $bm_sum=sum,$bm_sub=sub,$bm_mul=mul,$bm_div=div,$bm_mod=mod;function clamp(t,e,i){if(e>i){var r=i;i=e,e=r}return Math.min(Math.max(t,e),i)}function radiansToDegrees(t){return t/degToRads}var radians_to_degrees=radiansToDegrees;function degreesToRadians(t){return t*degToRads}var degrees_to_radians=radiansToDegrees,helperLengthArray=[0,0,0,0,0,0];function length(t,e){if("number"==typeof t||t instanceof Number)return e=e||0,Math.abs(t-e);e||(e=helperLengthArray);var i,r=Math.min(t.length,e.length),s=0;for(i=0;i<r;i+=1)s+=Math.pow(e[i]-t[i],2);return Math.sqrt(s)}function normalize(t){return div(t,length(t))}function rgbToHsl(t){var e,i,r=t[0],s=t[1],n=t[2],a=Math.max(r,s,n),o=Math.min(r,s,n),h=(a+o)/2;if(a===o)e=0,i=0;else{var l=a-o;switch(i=h>.5?l/(2-a-o):l/(a+o),a){case r:e=(s-n)/l+6*(s<n);break;case s:e=(n-r)/l+2;break;case n:e=(r-s)/l+4}e/=6}return[e,i,h,t[3]]}function hue2rgb(t,e,i){return i<0&&(i+=1),i>1&&(i-=1),i<1/6?t+6*(e-t)*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function hslToRgb(t){var e,i,r,s=t[0],n=t[1],a=t[2];if(0===n)e=a,r=a,i=a;else{var o=a<.5?a*(1+n):a+n-a*n,h=2*a-o;e=hue2rgb(h,o,s+1/3),i=hue2rgb(h,o,s),r=hue2rgb(h,o,s-1/3)}return[e,i,r,t[3]]}function linear(t,e,i,r,s){if(void 0!==r&&void 0!==s||(r=e,s=i,e=0,i=1),i<e){var n=i;i=e,e=n}if(t<=e)return r;if(t>=i)return s;var a,o=i===e?0:(t-e)/(i-e);if(!r.length)return r+(s-r)*o;var h=r.length,l=createTypedArray("float32",h);for(a=0;a<h;a+=1)l[a]=r[a]+(s[a]-r[a])*o;return l}function random(t,e){if(void 0===e&&(void 0===t?(t=0,e=1):(e=t,t=void 0)),e.length){var i,r=e.length;t||(t=createTypedArray("float32",r));var s=createTypedArray("float32",r),n=BMMath.random();for(i=0;i<r;i+=1)s[i]=t[i]+n*(e[i]-t[i]);return s}return void 0===t&&(t=0),t+BMMath.random()*(e-t)}function createPath(t,e,i,r){var s,n=t.length,a=shapePool.newElement();a.setPathData(!!r,n);var o,h,l=[0,0];for(s=0;s<n;s+=1)o=e&&e[s]?e[s]:l,h=i&&i[s]?i[s]:l,a.setTripleAt(t[s][0],t[s][1],h[0]+t[s][0],h[1]+t[s][1],o[0]+t[s][0],o[1]+t[s][1],s,!0);return a}function initiateExpression(elem,data,property){function noOp(t){return t}if(!elem.globalData.renderConfig.runExpressions)return noOp;var transform,$bm_transform,content,effect,val=data.x,needsVelocity=/velocity(?![\w\d])/.test(val),_needsRandom=-1!==val.indexOf("random"),elemType=elem.data.ty,thisProperty=property;thisProperty.valueAtTime=thisProperty.getValueAtTime,Object.defineProperty(thisProperty,"value",{get:function(){return thisProperty.v}}),elem.comp.frameDuration=1/elem.comp.globalData.frameRate,elem.comp.displayStartTime=0;var loopIn,loop_in,loopOut,loop_out,smooth,toWorld,fromWorld,fromComp,toComp,fromCompToSurface,position,rotation,anchorPoint,scale,thisLayer,thisComp,mask,valueAtTime,velocityAtTime,scoped_bm_rt,inPoint=elem.data.ip/elem.comp.globalData.frameRate,outPoint=elem.data.op/elem.comp.globalData.frameRate,width=elem.data.sw?elem.data.sw:0,height=elem.data.sh?elem.data.sh:0,name=elem.data.nm,expression_function=eval("[function _expression_function(){"+val+";scoped_bm_rt=$bm_rt}]")[0],numKeys=property.kf?data.k.length:0,active=!this.data||!0!==this.data.hd,wiggle=(function(t,e){var i,r,s=this.pv.length?this.pv.length:1,n=createTypedArray("float32",s),a=Math.floor(5*time);for(i=0,r=0;i<a;){for(r=0;r<s;r+=1)n[r]+=-e+2*e*BMMath.random();i+=1}var o=5*time,h=o-Math.floor(o),l=createTypedArray("float32",s);if(s>1){for(r=0;r<s;r+=1)l[r]=this.pv[r]+n[r]+(-e+2*e*BMMath.random())*h;return l}return this.pv+n[0]+(-e+2*e*BMMath.random())*h}).bind(this);function loopInDuration(t,e){return loopIn(t,e,!0)}function loopOutDuration(t,e){return loopOut(t,e,!0)}thisProperty.loopIn&&(loopIn=thisProperty.loopIn.bind(thisProperty),loop_in=loopIn),thisProperty.loopOut&&(loopOut=thisProperty.loopOut.bind(thisProperty),loop_out=loopOut),thisProperty.smooth&&(smooth=thisProperty.smooth.bind(thisProperty)),this.getValueAtTime&&(valueAtTime=this.getValueAtTime.bind(this)),this.getVelocityAtTime&&(velocityAtTime=this.getVelocityAtTime.bind(this));var time,velocity,value,text,textIndex,textTotal,selectorValue,comp=elem.comp.globalData.projectInterface.bind(elem.comp.globalData.projectInterface);function lookAt(t,e){var i=[e[0]-t[0],e[1]-t[1],e[2]-t[2]],r=Math.atan2(i[0],Math.sqrt(i[1]*i[1]+i[2]*i[2]))/degToRads;return[-Math.atan2(i[1],i[2])/degToRads,r,0]}function easeOut(t,e,i,r,s){return applyEase(easeOutBez,t,e,i,r,s)}function easeIn(t,e,i,r,s){return applyEase(easeInBez,t,e,i,r,s)}function ease(t,e,i,r,s){return applyEase(easeInOutBez,t,e,i,r,s)}function applyEase(t,e,i,r,s,n){void 0===s?(s=i,n=r):e=(e-i)/(r-i),e>1?e=1:e<0&&(e=0);var a=t(e);if($bm_isInstanceOfArray(s)){var o,h=s.length,l=createTypedArray("float32",h);for(o=0;o<h;o+=1)l[o]=(n[o]-s[o])*a+s[o];return l}return(n-s)*a+s}function nearestKey(t){var e,i,r,s=data.k.length;if(data.k.length&&"number"!=typeof data.k[0])if(i=-1,(t*=elem.comp.globalData.frameRate)<data.k[0].t)i=1,r=data.k[0].t;else{for(e=0;e<s-1;e+=1){if(t===data.k[e].t){i=e+1,r=data.k[e].t;break}if(t>data.k[e].t&&t<data.k[e+1].t){t-data.k[e].t>data.k[e+1].t-t?(i=e+2,r=data.k[e+1].t):(i=e+1,r=data.k[e].t);break}}-1===i&&(i=e+1,r=data.k[e].t)}else i=0,r=0;var n={};return n.index=i,n.time=r/elem.comp.globalData.frameRate,n}function key(t){if(!data.k.length||"number"==typeof data.k[0])throw Error("The property has no keyframe at index "+t);t-=1,e={time:data.k[t].t/elem.comp.globalData.frameRate,value:[]};var e,i,r,s=Object.prototype.hasOwnProperty.call(data.k[t],"s")?data.k[t].s:data.k[t-1].e;for(r=s.length,i=0;i<r;i+=1)e[i]=s[i],e.value[i]=s[i];return e}function framesToTime(t,e){return e||(e=elem.comp.globalData.frameRate),t/e}function timeToFrames(t,e){return t||0===t||(t=time),e||(e=elem.comp.globalData.frameRate),t*e}function seedRandom(t){BMMath.seedrandom(randSeed+t)}function sourceRectAtTime(){return elem.sourceRectAtTime()}function substring(t,e){return"string"==typeof value?void 0===e?value.substring(t):value.substring(t,e):""}function substr(t,e){return"string"==typeof value?void 0===e?value.substr(t):value.substr(t,e):""}function posterizeTime(t){time=0===t?0:Math.floor(time*t)/t,value=valueAtTime(time)}var parent,index=elem.data.ind,hasParent=!(!elem.hierarchy||!elem.hierarchy.length),randSeed=Math.floor(1e6*Math.random()),globalData=elem.globalData;function executeExpression(t){return value=t,this.frameExpressionId===elem.globalData.frameId&&"textSelector"!==this.propType?value:("textSelector"===this.propType&&(textIndex=this.textIndex,textTotal=this.textTotal,selectorValue=this.selectorValue),thisLayer||(text=elem.layerInterface.text,thisLayer=elem.layerInterface,thisComp=elem.comp.compInterface,toWorld=thisLayer.toWorld.bind(thisLayer),fromWorld=thisLayer.fromWorld.bind(thisLayer),fromComp=thisLayer.fromComp.bind(thisLayer),toComp=thisLayer.toComp.bind(thisLayer),mask=thisLayer.mask?thisLayer.mask.bind(thisLayer):null,fromCompToSurface=fromComp),transform||($bm_transform=transform=elem.layerInterface("ADBE Transform Group"),transform&&(anchorPoint=transform.anchorPoint)),4!==elemType||content||(content=thisLayer("ADBE Root Vectors Group")),effect||(effect=thisLayer(4)),(hasParent=!(!elem.hierarchy||!elem.hierarchy.length))&&!parent&&(parent=elem.hierarchy[0].layerInterface),time=this.comp.renderedFrame/this.comp.globalData.frameRate,_needsRandom&&seedRandom(randSeed+time),needsVelocity&&(velocity=velocityAtTime(time)),expression_function(),this.frameExpressionId=elem.globalData.frameId,scoped_bm_rt=scoped_bm_rt.propType===propTypes.SHAPE?scoped_bm_rt.v:scoped_bm_rt)}return executeExpression.__preventDeadCodeRemoval=[$bm_transform,anchorPoint,time,velocity,inPoint,outPoint,width,height,name,loop_in,loop_out,smooth,toComp,fromCompToSurface,toWorld,fromWorld,mask,position,rotation,scale,thisComp,numKeys,active,wiggle,loopInDuration,loopOutDuration,comp,lookAt,easeOut,easeIn,ease,nearestKey,key,text,textIndex,textTotal,selectorValue,framesToTime,timeToFrames,sourceRectAtTime,substring,substr,posterizeTime,index,globalData],executeExpression}return ob.initiateExpression=initiateExpression,ob.__preventDeadCodeRemoval=[window,document,XMLHttpRequest,fetch,frames,$bm_neg,add,$bm_sum,$bm_sub,$bm_mul,$bm_div,$bm_mod,clamp,radians_to_degrees,degreesToRadians,degrees_to_radians,normalize,rgbToHsl,hslToRgb,linear,random,createPath,_lottieGlobal],ob.resetFrame=resetFrame,ob}(),Expressions=function(){var t={};return t.initExpressions=function(t){var e=0,i=[];t.renderer.compInterface=CompExpressionInterface(t.renderer),t.renderer.globalData.projectInterface.registerComposition(t.renderer),t.renderer.globalData.pushExpression=function(){e+=1},t.renderer.globalData.popExpression=function(){0==(e-=1)&&function(){var t,e=i.length;for(t=0;t<e;t+=1)i[t].release();i.length=0}()},t.renderer.globalData.registerExpressionProperty=function(t){-1===i.indexOf(t)&&i.push(t)}},t.resetFrame=ExpressionManager.resetFrame,t}(),MaskManagerInterface=function(){function t(t,e){this._mask=t,this._data=e}return Object.defineProperty(t.prototype,"maskPath",{get:function(){return this._mask.prop.k&&this._mask.prop.getValue(),this._mask.prop}}),Object.defineProperty(t.prototype,"maskOpacity",{get:function(){return this._mask.op.k&&this._mask.op.getValue(),100*this._mask.op.v}}),function(e){var i,r=createSizedArray(e.viewData.length),s=e.viewData.length;for(i=0;i<s;i+=1)r[i]=new t(e.viewData[i],e.masksProperties[i]);return function(t){for(i=0;i<s;){if(e.masksProperties[i].nm===t)return r[i];i+=1}return null}}}(),ExpressionPropertyInterface=function(){var t={pv:0,v:0,mult:1},e={pv:[0,0,0],v:[0,0,0],mult:1};function i(t,e,i){Object.defineProperty(t,"velocity",{get:function(){return e.getVelocityAtTime(e.comp.currentFrame)}}),t.numKeys=e.keyframes?e.keyframes.length:0,t.key=function(r){if(!t.numKeys)return 0;var s="";s="s"in e.keyframes[r-1]?e.keyframes[r-1].s:"e"in e.keyframes[r-2]?e.keyframes[r-2].e:e.keyframes[r-2].s;var n="unidimensional"===i?new Number(s):Object.assign({},s);return n.time=e.keyframes[r-1].t/e.elem.comp.globalData.frameRate,n.value="unidimensional"===i?s[0]:s,n},t.valueAtTime=e.getValueAtTime,t.speedAtTime=e.getSpeedAtTime,t.velocityAtTime=e.getVelocityAtTime,t.propertyGroup=e.propertyGroup}function r(){return t}return function(s){return s?"unidimensional"===s.propType?function(e){e&&"pv"in e||(e=t);var r=1/e.mult,s=e.pv*r,n=new Number(s);return n.value=s,i(n,e,"unidimensional"),function(){return e.k&&e.getValue(),s=e.v*r,n.value!==s&&((n=new Number(s)).value=s,i(n,e,"unidimensional")),n}}(s):function(t){t&&"pv"in t||(t=e);var r=1/t.mult,s=t.data&&t.data.l||t.pv.length,n=createTypedArray("float32",s),a=createTypedArray("float32",s);return n.value=a,i(n,t,"multidimensional"),function(){t.k&&t.getValue();for(var e=0;e<s;e+=1)a[e]=t.v[e]*r,n[e]=a[e];return n}}(s):r}}(),TransformExpressionInterface=function(t){var e,i,r,s;function n(t){switch(t){case"scale":case"Scale":case"ADBE Scale":case 6:return n.scale;case"rotation":case"Rotation":case"ADBE Rotation":case"ADBE Rotate Z":case 10:return n.rotation;case"ADBE Rotate X":return n.xRotation;case"ADBE Rotate Y":return n.yRotation;case"position":case"Position":case"ADBE Position":case 2:return n.position;case"ADBE Position_0":return n.xPosition;case"ADBE Position_1":return n.yPosition;case"ADBE Position_2":return n.zPosition;case"anchorPoint":case"AnchorPoint":case"Anchor Point":case"ADBE AnchorPoint":case 1:return n.anchorPoint;case"opacity":case"Opacity":case 11:return n.opacity;default:return null}}return Object.defineProperty(n,"rotation",{get:ExpressionPropertyInterface(t.r||t.rz)}),Object.defineProperty(n,"zRotation",{get:ExpressionPropertyInterface(t.rz||t.r)}),Object.defineProperty(n,"xRotation",{get:ExpressionPropertyInterface(t.rx)}),Object.defineProperty(n,"yRotation",{get:ExpressionPropertyInterface(t.ry)}),Object.defineProperty(n,"scale",{get:ExpressionPropertyInterface(t.s)}),t.p?s=ExpressionPropertyInterface(t.p):(e=ExpressionPropertyInterface(t.px),i=ExpressionPropertyInterface(t.py),t.pz&&(r=ExpressionPropertyInterface(t.pz))),Object.defineProperty(n,"position",{get:function(){return t.p?s():[e(),i(),r?r():0]}}),Object.defineProperty(n,"xPosition",{get:ExpressionPropertyInterface(t.px)}),Object.defineProperty(n,"yPosition",{get:ExpressionPropertyInterface(t.py)}),Object.defineProperty(n,"zPosition",{get:ExpressionPropertyInterface(t.pz)}),Object.defineProperty(n,"anchorPoint",{get:ExpressionPropertyInterface(t.a)}),Object.defineProperty(n,"opacity",{get:ExpressionPropertyInterface(t.o)}),Object.defineProperty(n,"skew",{get:ExpressionPropertyInterface(t.sk)}),Object.defineProperty(n,"skewAxis",{get:ExpressionPropertyInterface(t.sa)}),Object.defineProperty(n,"orientation",{get:ExpressionPropertyInterface(t.or)}),n},LayerExpressionInterface=function(){function t(t){var e=new Matrix;return void 0!==t?this._elem.finalTransform.mProp.getValueAtTime(t).clone(e):this._elem.finalTransform.mProp.applyToMatrix(e),e}function e(t,e){var i=this.getMatrix(e);return i.props[12]=0,i.props[13]=0,i.props[14]=0,this.applyPoint(i,t)}function i(t,e){var i=this.getMatrix(e);return this.applyPoint(i,t)}function r(t,e){var i=this.getMatrix(e);return i.props[12]=0,i.props[13]=0,i.props[14]=0,this.invertPoint(i,t)}function s(t,e){var i=this.getMatrix(e);return this.invertPoint(i,t)}function n(t,e){if(this._elem.hierarchy&&this._elem.hierarchy.length){var i,r=this._elem.hierarchy.length;for(i=0;i<r;i+=1)this._elem.hierarchy[i].finalTransform.mProp.applyToMatrix(t)}return t.applyToPointArray(e[0],e[1],e[2]||0)}function a(t,e){if(this._elem.hierarchy&&this._elem.hierarchy.length){var i,r=this._elem.hierarchy.length;for(i=0;i<r;i+=1)this._elem.hierarchy[i].finalTransform.mProp.applyToMatrix(t)}return t.inversePoint(e)}function o(t){var e=new Matrix;if(e.reset(),this._elem.finalTransform.mProp.applyToMatrix(e),this._elem.hierarchy&&this._elem.hierarchy.length){var i,r=this._elem.hierarchy.length;for(i=0;i<r;i+=1)this._elem.hierarchy[i].finalTransform.mProp.applyToMatrix(e)}return e.inversePoint(t)}function h(){return[1,1,1,1]}return function(l){function p(t){switch(t){case"ADBE Root Vectors Group":case"Contents":case 2:return p.shapeInterface;case 1:case 6:case"Transform":case"transform":case"ADBE Transform Group":return f;case 4:case"ADBE Effect Parade":case"effects":case"Effects":return p.effect;case"ADBE Text Properties":return p.textInterface;default:return null}}p.getMatrix=t,p.invertPoint=a,p.applyPoint=n,p.toWorld=i,p.toWorldVec=e,p.fromWorld=s,p.fromWorldVec=r,p.toComp=i,p.fromComp=o,p.sampleImage=h,p.sourceRectAtTime=l.sourceRectAtTime.bind(l),p._elem=l;var f,u=getDescriptor(f=TransformExpressionInterface(l.finalTransform.mProp),"anchorPoint");return Object.defineProperties(p,{hasParent:{get:function(){return l.hierarchy.length}},parent:{get:function(){return l.hierarchy[0].layerInterface}},rotation:getDescriptor(f,"rotation"),scale:getDescriptor(f,"scale"),position:getDescriptor(f,"position"),opacity:getDescriptor(f,"opacity"),anchorPoint:u,anchor_point:u,transform:{get:function(){return f}},active:{get:function(){return l.isInRange}}}),p.startTime=l.data.st,p.index=l.data.ind,p.source=l.data.refId,p.height=0===l.data.ty?l.data.h:100,p.width=0===l.data.ty?l.data.w:100,p.inPoint=l.data.ip/l.comp.globalData.frameRate,p.outPoint=l.data.op/l.comp.globalData.frameRate,p._name=l.data.nm,p.registerMaskInterface=function(t){p.mask=new MaskManagerInterface(t,l)},p.registerEffectsInterface=function(t){p.effect=t},p}}(),propertyGroupFactory=function(t,e){return function(i){return(i=void 0===i?1:i)<=0?t:e(i-1)}},PropertyInterface=function(t,e){var i={_name:t};return function(t){return(t=void 0===t?1:t)<=0?i:e(t-1)}},EffectsExpressionInterface=function(){function t(i,r,s,n){function a(t){for(var e=i.ef,r=0,s=e.length;r<s;){if(t===e[r].nm||t===e[r].mn||t===e[r].ix)return 5===e[r].ty?l[r]:l[r]();r+=1}throw Error()}var o,h=propertyGroupFactory(a,s),l=[],p=i.ef.length;for(o=0;o<p;o+=1)5===i.ef[o].ty?l.push(t(i.ef[o],r.effectElements[o],r.effectElements[o].propertyGroup,n)):l.push(e(r.effectElements[o],i.ef[o].ty,n,h));return"ADBE Color Control"===i.mn&&Object.defineProperty(a,"color",{get:function(){return l[0]()}}),Object.defineProperties(a,{numProperties:{get:function(){return i.np}},_name:{value:i.nm},propertyGroup:{value:h}}),a.enabled=0!==i.en,a.active=a.enabled,a}function e(t,e,i,r){var s=ExpressionPropertyInterface(t.p);return t.p.setGroupProperty&&t.p.setGroupProperty(PropertyInterface("",r)),function(){return 10===e?i.comp.compInterface(t.p.v):s()}}return{createEffectsInterface:function(e,i){if(e.effectsManager){var r,s=[],n=e.data.ef,a=e.effectsManager.effectElements.length;for(r=0;r<a;r+=1)s.push(t(n[r],e.effectsManager.effectElements[r],i,e));var o=e.data.ef||[],h=function(t){for(r=0,a=o.length;r<a;){if(t===o[r].nm||t===o[r].mn||t===o[r].ix)return s[r];r+=1}return null};return Object.defineProperty(h,"numProperties",{get:function(){return o.length}}),h}return null}}}(),ShapePathInterface=function(t,e,i){var r=e.sh;function s(t){return"Shape"===t||"shape"===t||"Path"===t||"path"===t||"ADBE Vector Shape"===t||2===t?s.path:null}var n=propertyGroupFactory(s,i);return r.setGroupProperty(PropertyInterface("Path",n)),Object.defineProperties(s,{path:{get:function(){return r.k&&r.getValue(),r}},shape:{get:function(){return r.k&&r.getValue(),r}},_name:{value:t.nm},ix:{value:t.ix},propertyIndex:{value:t.ix},mn:{value:t.mn},propertyGroup:{value:i}}),s},ShapeExpressionInterface=function(){function t(t,o,c){var m,d=[],g=t?t.length:0;for(m=0;m<g;m+=1)"gr"===t[m].ty?d.push(e(t[m],o[m],c)):"fl"===t[m].ty?d.push(i(t[m],o[m],c)):"st"===t[m].ty?d.push(n(t[m],o[m],c)):"tm"===t[m].ty?d.push(a(t[m],o[m],c)):"tr"===t[m].ty||("el"===t[m].ty?d.push(h(t[m],o[m],c)):"sr"===t[m].ty?d.push(l(t[m],o[m],c)):"sh"===t[m].ty?d.push(ShapePathInterface(t[m],o[m],c)):"rc"===t[m].ty?d.push(p(t[m],o[m],c)):"rd"===t[m].ty?d.push(f(t[m],o[m],c)):"rp"===t[m].ty?d.push(u(t[m],o[m],c)):"gf"===t[m].ty?d.push(r(t[m],o[m],c)):d.push(s(t[m],o[m])));return d}function e(e,i,r){var s=function(t){switch(t){case"ADBE Vectors Group":case"Contents":case 2:return s.content;default:return s.transform}};s.propertyGroup=propertyGroupFactory(s,r);var n=function(e,i,r){var s,n=function(t){for(var e=0,i=s.length;e<i;){if(s[e]._name===t||s[e].mn===t||s[e].propertyIndex===t||s[e].ix===t||s[e].ind===t)return s[e];e+=1}return"number"==typeof t?s[t-1]:null};n.propertyGroup=propertyGroupFactory(n,r),s=t(e.it,i.it,n.propertyGroup),n.numProperties=s.length;var a=o(e.it[e.it.length-1],i.it[i.it.length-1],n.propertyGroup);return n.transform=a,n.propertyIndex=e.cix,n._name=e.nm,n}(e,i,s.propertyGroup),a=o(e.it[e.it.length-1],i.it[i.it.length-1],s.propertyGroup);return s.content=n,s.transform=a,Object.defineProperty(s,"_name",{get:function(){return e.nm}}),s.numProperties=e.np,s.propertyIndex=e.ix,s.nm=e.nm,s.mn=e.mn,s}function i(t,e,i){function r(t){return"Color"===t||"color"===t?r.color:"Opacity"===t||"opacity"===t?r.opacity:null}return Object.defineProperties(r,{color:{get:ExpressionPropertyInterface(e.c)},opacity:{get:ExpressionPropertyInterface(e.o)},_name:{value:t.nm},mn:{value:t.mn}}),e.c.setGroupProperty(PropertyInterface("Color",i)),e.o.setGroupProperty(PropertyInterface("Opacity",i)),r}function r(t,e,i){function r(t){return"Start Point"===t||"start point"===t?r.startPoint:"End Point"===t||"end point"===t?r.endPoint:"Opacity"===t||"opacity"===t?r.opacity:null}return Object.defineProperties(r,{startPoint:{get:ExpressionPropertyInterface(e.s)},endPoint:{get:ExpressionPropertyInterface(e.e)},opacity:{get:ExpressionPropertyInterface(e.o)},type:{get:function(){return"a"}},_name:{value:t.nm},mn:{value:t.mn}}),e.s.setGroupProperty(PropertyInterface("Start Point",i)),e.e.setGroupProperty(PropertyInterface("End Point",i)),e.o.setGroupProperty(PropertyInterface("Opacity",i)),r}function s(){return function(){return null}}function n(t,e,i){var r,s=propertyGroupFactory(l,i),n=propertyGroupFactory(h,s);function a(i){Object.defineProperty(h,t.d[i].nm,{get:ExpressionPropertyInterface(e.d.dataProps[i].p)})}var o=t.d?t.d.length:0,h={};for(r=0;r<o;r+=1)a(r),e.d.dataProps[r].p.setGroupProperty(n);function l(t){return"Color"===t||"color"===t?l.color:"Opacity"===t||"opacity"===t?l.opacity:"Stroke Width"===t||"stroke width"===t?l.strokeWidth:null}return Object.defineProperties(l,{color:{get:ExpressionPropertyInterface(e.c)},opacity:{get:ExpressionPropertyInterface(e.o)},strokeWidth:{get:ExpressionPropertyInterface(e.w)},dash:{get:function(){return h}},_name:{value:t.nm},mn:{value:t.mn}}),e.c.setGroupProperty(PropertyInterface("Color",s)),e.o.setGroupProperty(PropertyInterface("Opacity",s)),e.w.setGroupProperty(PropertyInterface("Stroke Width",s)),l}function a(t,e,i){function r(e){return e===t.e.ix||"End"===e||"end"===e?r.end:e===t.s.ix?r.start:e===t.o.ix?r.offset:null}var s=propertyGroupFactory(r,i);return r.propertyIndex=t.ix,e.s.setGroupProperty(PropertyInterface("Start",s)),e.e.setGroupProperty(PropertyInterface("End",s)),e.o.setGroupProperty(PropertyInterface("Offset",s)),r.propertyIndex=t.ix,r.propertyGroup=i,Object.defineProperties(r,{start:{get:ExpressionPropertyInterface(e.s)},end:{get:ExpressionPropertyInterface(e.e)},offset:{get:ExpressionPropertyInterface(e.o)},_name:{value:t.nm}}),r.mn=t.mn,r}function o(t,e,i){function r(e){return t.a.ix===e||"Anchor Point"===e?r.anchorPoint:t.o.ix===e||"Opacity"===e?r.opacity:t.p.ix===e||"Position"===e?r.position:t.r.ix===e||"Rotation"===e||"ADBE Vector Rotation"===e?r.rotation:t.s.ix===e||"Scale"===e?r.scale:t.sk&&t.sk.ix===e||"Skew"===e?r.skew:t.sa&&t.sa.ix===e||"Skew Axis"===e?r.skewAxis:null}var s=propertyGroupFactory(r,i);return e.transform.mProps.o.setGroupProperty(PropertyInterface("Opacity",s)),e.transform.mProps.p.setGroupProperty(PropertyInterface("Position",s)),e.transform.mProps.a.setGroupProperty(PropertyInterface("Anchor Point",s)),e.transform.mProps.s.setGroupProperty(PropertyInterface("Scale",s)),e.transform.mProps.r.setGroupProperty(PropertyInterface("Rotation",s)),e.transform.mProps.sk&&(e.transform.mProps.sk.setGroupProperty(PropertyInterface("Skew",s)),e.transform.mProps.sa.setGroupProperty(PropertyInterface("Skew Angle",s))),e.transform.op.setGroupProperty(PropertyInterface("Opacity",s)),Object.defineProperties(r,{opacity:{get:ExpressionPropertyInterface(e.transform.mProps.o)},position:{get:ExpressionPropertyInterface(e.transform.mProps.p)},anchorPoint:{get:ExpressionPropertyInterface(e.transform.mProps.a)},scale:{get:ExpressionPropertyInterface(e.transform.mProps.s)},rotation:{get:ExpressionPropertyInterface(e.transform.mProps.r)},skew:{get:ExpressionPropertyInterface(e.transform.mProps.sk)},skewAxis:{get:ExpressionPropertyInterface(e.transform.mProps.sa)},_name:{value:t.nm}}),r.ty="tr",r.mn=t.mn,r.propertyGroup=i,r}function h(t,e,i){function r(e){return t.p.ix===e?r.position:t.s.ix===e?r.size:null}var s=propertyGroupFactory(r,i);r.propertyIndex=t.ix;var n="tm"===e.sh.ty?e.sh.prop:e.sh;return n.s.setGroupProperty(PropertyInterface("Size",s)),n.p.setGroupProperty(PropertyInterface("Position",s)),Object.defineProperties(r,{size:{get:ExpressionPropertyInterface(n.s)},position:{get:ExpressionPropertyInterface(n.p)},_name:{value:t.nm}}),r.mn=t.mn,r}function l(t,e,i){function r(e){return t.p.ix===e?r.position:t.r.ix===e?r.rotation:t.pt.ix===e?r.points:t.or.ix===e||"ADBE Vector Star Outer Radius"===e?r.outerRadius:t.os.ix===e?r.outerRoundness:t.ir&&(t.ir.ix===e||"ADBE Vector Star Inner Radius"===e)?r.innerRadius:t.is&&t.is.ix===e?r.innerRoundness:null}var s=propertyGroupFactory(r,i),n="tm"===e.sh.ty?e.sh.prop:e.sh;return r.propertyIndex=t.ix,n.or.setGroupProperty(PropertyInterface("Outer Radius",s)),n.os.setGroupProperty(PropertyInterface("Outer Roundness",s)),n.pt.setGroupProperty(PropertyInterface("Points",s)),n.p.setGroupProperty(PropertyInterface("Position",s)),n.r.setGroupProperty(PropertyInterface("Rotation",s)),t.ir&&(n.ir.setGroupProperty(PropertyInterface("Inner Radius",s)),n.is.setGroupProperty(PropertyInterface("Inner Roundness",s))),Object.defineProperties(r,{position:{get:ExpressionPropertyInterface(n.p)},rotation:{get:ExpressionPropertyInterface(n.r)},points:{get:ExpressionPropertyInterface(n.pt)},outerRadius:{get:ExpressionPropertyInterface(n.or)},outerRoundness:{get:ExpressionPropertyInterface(n.os)},innerRadius:{get:ExpressionPropertyInterface(n.ir)},innerRoundness:{get:ExpressionPropertyInterface(n.is)},_name:{value:t.nm}}),r.mn=t.mn,r}function p(t,e,i){function r(e){return t.p.ix===e?r.position:t.r.ix===e?r.roundness:t.s.ix===e||"Size"===e||"ADBE Vector Rect Size"===e?r.size:null}var s=propertyGroupFactory(r,i),n="tm"===e.sh.ty?e.sh.prop:e.sh;return r.propertyIndex=t.ix,n.p.setGroupProperty(PropertyInterface("Position",s)),n.s.setGroupProperty(PropertyInterface("Size",s)),n.r.setGroupProperty(PropertyInterface("Rotation",s)),Object.defineProperties(r,{position:{get:ExpressionPropertyInterface(n.p)},roundness:{get:ExpressionPropertyInterface(n.r)},size:{get:ExpressionPropertyInterface(n.s)},_name:{value:t.nm}}),r.mn=t.mn,r}function f(t,e,i){function r(e){return t.r.ix===e||"Round Corners 1"===e?r.radius:null}var s=propertyGroupFactory(r,i),n=e;return r.propertyIndex=t.ix,n.rd.setGroupProperty(PropertyInterface("Radius",s)),Object.defineProperties(r,{radius:{get:ExpressionPropertyInterface(n.rd)},_name:{value:t.nm}}),r.mn=t.mn,r}function u(t,e,i){function r(e){return t.c.ix===e||"Copies"===e?r.copies:t.o.ix===e||"Offset"===e?r.offset:null}var s=propertyGroupFactory(r,i),n=e;return r.propertyIndex=t.ix,n.c.setGroupProperty(PropertyInterface("Copies",s)),n.o.setGroupProperty(PropertyInterface("Offset",s)),Object.defineProperties(r,{copies:{get:ExpressionPropertyInterface(n.c)},offset:{get:ExpressionPropertyInterface(n.o)},_name:{value:t.nm}}),r.mn=t.mn,r}return function(e,i,r){var s;function n(t){if("number"==typeof t)return 0===(t=void 0===t?1:t)?r:s[t-1];for(var e=0,i=s.length;e<i;){if(s[e]._name===t)return s[e];e+=1}return null}return n.propertyGroup=propertyGroupFactory(n,function(){return r}),s=t(e,i,n.propertyGroup),n.numProperties=s.length,n._name="Contents",n}}(),TextExpressionInterface=function(t){var e;function i(t){return"ADBE Text Document"===t?i.sourceText:null}return Object.defineProperty(i,"sourceText",{get:function(){t.textProperty.getValue();var i=t.textProperty.currentData.t;return e&&i===e.value||((e=new String(i)).value=i||new String(i),Object.defineProperty(e,"style",{get:function(){return{fillColor:t.textProperty.currentData.fc}}})),e}}),i};function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var dataInterfaceFactory,FootageInterface=(dataInterfaceFactory=function(t){function e(t){return"Outline"===t?e.outlineInterface():null}return e._name="Outline",e.outlineInterface=function(t){var e="",i=t.getFootageData();function r(t){if(i[t])return e=t,"object"===_typeof(i=i[t])?r:i;var s=t.indexOf(e);if(-1!==s){var n=parseInt(t.substr(s+e.length),10);return"object"===_typeof(i=i[n])?r:i}return""}return function(){return e="",i=t.getFootageData(),r}}(t),e},function(t){function e(t){return"Data"===t?e.dataInterface:null}return e._name="Data",e.dataInterface=dataInterfaceFactory(t),e}),interfaces={layer:LayerExpressionInterface,effects:EffectsExpressionInterface,comp:CompExpressionInterface,shape:ShapeExpressionInterface,text:TextExpressionInterface,footage:FootageInterface};function getInterface(t){return interfaces[t]||null}var expressionHelpers={searchExpressions:function(t,e,i){e.x&&(i.k=!0,i.x=!0,i.initiateExpression=ExpressionManager.initiateExpression,i.effectsSequence.push(i.initiateExpression(t,e,i).bind(i)))},getSpeedAtTime:function(t){var e,i=this.getValueAtTime(t),r=this.getValueAtTime(t+-.01),s=0;if(i.length){for(e=0;e<i.length;e+=1)s+=Math.pow(r[e]-i[e],2);s=100*Math.sqrt(s)}else s=0;return s},getVelocityAtTime:function(t){if(void 0!==this.vel)return this.vel;var e,i,r=-.001,s=this.getValueAtTime(t),n=this.getValueAtTime(t+r);if(s.length)for(e=createTypedArray("float32",s.length),i=0;i<s.length;i+=1)e[i]=(n[i]-s[i])/r;else e=(n-s)/r;return e},getValueAtTime:function(t){return t*=this.elem.globalData.frameRate,(t-=this.offsetTime)!==this._cachingAtTime.lastFrame&&(this._cachingAtTime.lastIndex=this._cachingAtTime.lastFrame<t?this._cachingAtTime.lastIndex:0,this._cachingAtTime.value=this.interpolateValue(t,this._cachingAtTime),this._cachingAtTime.lastFrame=t),this._cachingAtTime.value},getStaticValueAtTime:function(){return this.pv},setGroupProperty:function(t){this.propertyGroup=t}};function addPropertyDecorator(){function t(t,e,i){if(!this.k||!this.keyframes)return this.pv;t=t?t.toLowerCase():"";var r,s,n,a,o,h=this.comp.renderedFrame,l=this.keyframes,p=l[l.length-1].t;if(h<=p)return this.pv;if(i?s=p-(r=e?Math.abs(p-this.elem.comp.globalData.frameRate*e):Math.max(0,p-this.elem.data.ip)):((!e||e>l.length-1)&&(e=l.length-1),r=p-(s=l[l.length-1-e].t)),"pingpong"===t){if(Math.floor((h-s)/r)%2!=0)return this.getValueAtTime((r-(h-s)%r+s)/this.comp.globalData.frameRate,0)}else{if("offset"===t){var f=this.getValueAtTime(s/this.comp.globalData.frameRate,0),u=this.getValueAtTime(p/this.comp.globalData.frameRate,0),c=this.getValueAtTime(((h-s)%r+s)/this.comp.globalData.frameRate,0),m=Math.floor((h-s)/r);if(this.pv.length){for(a=(o=Array(f.length)).length,n=0;n<a;n+=1)o[n]=(u[n]-f[n])*m+c[n];return o}return(u-f)*m+c}if("continue"===t){var d=this.getValueAtTime(p/this.comp.globalData.frameRate,0),g=this.getValueAtTime((p-.001)/this.comp.globalData.frameRate,0);if(this.pv.length){for(a=(o=Array(d.length)).length,n=0;n<a;n+=1)o[n]=d[n]+(d[n]-g[n])*((h-p)/this.comp.globalData.frameRate)/5e-4;return o}return d+(h-p)/.001*(d-g)}}return this.getValueAtTime(((h-s)%r+s)/this.comp.globalData.frameRate,0)}function e(t,e,i){if(!this.k)return this.pv;t=t?t.toLowerCase():"";var r,s,n,a,o,h=this.comp.renderedFrame,l=this.keyframes,p=l[0].t;if(h>=p)return this.pv;if(i?s=p+(r=e?Math.abs(this.elem.comp.globalData.frameRate*e):Math.max(0,this.elem.data.op-p)):((!e||e>l.length-1)&&(e=l.length-1),r=(s=l[e].t)-p),"pingpong"===t){if(Math.floor((p-h)/r)%2==0)return this.getValueAtTime(((p-h)%r+p)/this.comp.globalData.frameRate,0)}else{if("offset"===t){var f=this.getValueAtTime(p/this.comp.globalData.frameRate,0),u=this.getValueAtTime(s/this.comp.globalData.frameRate,0),c=this.getValueAtTime((r-(p-h)%r+p)/this.comp.globalData.frameRate,0),m=Math.floor((p-h)/r)+1;if(this.pv.length){for(a=(o=Array(f.length)).length,n=0;n<a;n+=1)o[n]=c[n]-(u[n]-f[n])*m;return o}return c-(u-f)*m}if("continue"===t){var d=this.getValueAtTime(p/this.comp.globalData.frameRate,0),g=this.getValueAtTime((p+.001)/this.comp.globalData.frameRate,0);if(this.pv.length){for(a=(o=Array(d.length)).length,n=0;n<a;n+=1)o[n]=d[n]+(d[n]-g[n])*(p-h)/.001;return o}return d+(d-g)*(p-h)/.001}}return this.getValueAtTime((r-((p-h)%r+p))/this.comp.globalData.frameRate,0)}function i(t,e){if(!this.k||(t=.5*(t||.4),(e=Math.floor(e||5))<=1))return this.pv;var i,r,s=this.comp.renderedFrame/this.comp.globalData.frameRate,n=s-t,a=e>1?(s+t-n)/(e-1):1,o=0,h=0;for(i=this.pv.length?createTypedArray("float32",this.pv.length):0;o<e;){if(r=this.getValueAtTime(n+o*a),this.pv.length)for(h=0;h<this.pv.length;h+=1)i[h]+=r[h];else i+=r;o+=1}if(this.pv.length)for(h=0;h<this.pv.length;h+=1)i[h]/=e;else i/=e;return i}function r(t){this._transformCachingAtTime||(this._transformCachingAtTime={v:new Matrix});var e=this._transformCachingAtTime.v;if(e.cloneFromProps(this.pre.props),this.appliedTransformations<1){var i=this.a.getValueAtTime(t);e.translate(-i[0]*this.a.mult,-i[1]*this.a.mult,i[2]*this.a.mult)}if(this.appliedTransformations<2){var r=this.s.getValueAtTime(t);e.scale(r[0]*this.s.mult,r[1]*this.s.mult,r[2]*this.s.mult)}if(this.sk&&this.appliedTransformations<3){var s=this.sk.getValueAtTime(t),n=this.sa.getValueAtTime(t);e.skewFromAxis(-s*this.sk.mult,n*this.sa.mult)}if(this.r&&this.appliedTransformations<4){var a=this.r.getValueAtTime(t);e.rotate(-a*this.r.mult)}else if(!this.r&&this.appliedTransformations<4){var o=this.rz.getValueAtTime(t),h=this.ry.getValueAtTime(t),l=this.rx.getValueAtTime(t),p=this.or.getValueAtTime(t);e.rotateZ(-o*this.rz.mult).rotateY(h*this.ry.mult).rotateX(l*this.rx.mult).rotateZ(-p[2]*this.or.mult).rotateY(p[1]*this.or.mult).rotateX(p[0]*this.or.mult)}if(this.data.p&&this.data.p.s){var f=this.px.getValueAtTime(t),u=this.py.getValueAtTime(t);if(this.data.p.z){var c=this.pz.getValueAtTime(t);e.translate(f*this.px.mult,u*this.py.mult,-c*this.pz.mult)}else e.translate(f*this.px.mult,u*this.py.mult,0)}else{var m=this.p.getValueAtTime(t);e.translate(m[0]*this.p.mult,m[1]*this.p.mult,-m[2]*this.p.mult)}return e}function s(){return this.v.clone(new Matrix)}var n=TransformPropertyFactory.getTransformProperty;TransformPropertyFactory.getTransformProperty=function(t,e,i){var a=n(t,e,i);return a.dynamicProperties.length?a.getValueAtTime=r.bind(a):a.getValueAtTime=s.bind(a),a.setGroupProperty=expressionHelpers.setGroupProperty,a};var a=PropertyFactory.getProp;PropertyFactory.getProp=function(r,s,n,o,h){var l=a(r,s,n,o,h);l.kf?l.getValueAtTime=expressionHelpers.getValueAtTime.bind(l):l.getValueAtTime=expressionHelpers.getStaticValueAtTime.bind(l),l.setGroupProperty=expressionHelpers.setGroupProperty,l.loopOut=t,l.loopIn=e,l.smooth=i,l.getVelocityAtTime=expressionHelpers.getVelocityAtTime.bind(l),l.getSpeedAtTime=expressionHelpers.getSpeedAtTime.bind(l),l.numKeys=1===s.a?s.k.length:0,l.propertyIndex=s.ix;var p=0;return 0!==n&&(p=createTypedArray("float32",1===s.a?s.k[0].s.length:s.k.length)),l._cachingAtTime={lastFrame:initialDefaultFrame,lastIndex:0,value:p},expressionHelpers.searchExpressions(r,s,l),l.k&&h.addDynamicProperty(l),l};var o=ShapePropertyFactory.getConstructorFunction(),h=ShapePropertyFactory.getKeyframedConstructorFunction();function l(){}l.prototype={vertices:function(t,e){this.k&&this.getValue();var i,r=this.v;void 0!==e&&(r=this.getValueAtTime(e,0));var s=r._length,n=r[t],a=r.v,o=createSizedArray(s);for(i=0;i<s;i+=1)o[i]="i"===t||"o"===t?[n[i][0]-a[i][0],n[i][1]-a[i][1]]:[n[i][0],n[i][1]];return o},points:function(t){return this.vertices("v",t)},inTangents:function(t){return this.vertices("i",t)},outTangents:function(t){return this.vertices("o",t)},isClosed:function(){return this.v.c},pointOnPath:function(t,e){var i=this.v;void 0!==e&&(i=this.getValueAtTime(e,0)),this._segmentsLength||(this._segmentsLength=bez.getSegmentsLength(i));for(var r,s=this._segmentsLength,n=s.lengths,a=s.totalLength*t,o=0,h=n.length,l=0;o<h;){if(l+n[o].addedLength>a){var p=o,f=i.c&&o===h-1?0:o+1,u=(a-l)/n[o].addedLength;r=bez.getPointInSegment(i.v[p],i.v[f],i.o[p],i.i[f],u,n[o]);break}l+=n[o].addedLength,o+=1}return r||(r=i.c?[i.v[0][0],i.v[0][1]]:[i.v[i._length-1][0],i.v[i._length-1][1]]),r},vectorOnPath:function(t,e,i){1==t?t=this.v.c:0==t&&(t=.999);var r=this.pointOnPath(t,e),s=this.pointOnPath(t+.001,e),n=s[0]-r[0],a=s[1]-r[1],o=Math.sqrt(Math.pow(n,2)+Math.pow(a,2));return 0===o?[0,0]:"tangent"===i?[n/o,a/o]:[-a/o,n/o]},tangentOnPath:function(t,e){return this.vectorOnPath(t,e,"tangent")},normalOnPath:function(t,e){return this.vectorOnPath(t,e,"normal")},setGroupProperty:expressionHelpers.setGroupProperty,getValueAtTime:expressionHelpers.getStaticValueAtTime},extendPrototype([l],o),extendPrototype([l],h),h.prototype.getValueAtTime=function(t){return this._cachingAtTime||(this._cachingAtTime={shapeValue:shapePool.clone(this.pv),lastIndex:0,lastTime:initialDefaultFrame}),t*=this.elem.globalData.frameRate,(t-=this.offsetTime)!==this._cachingAtTime.lastTime&&(this._cachingAtTime.lastIndex=this._cachingAtTime.lastTime<t?this._caching.lastIndex:0,this._cachingAtTime.lastTime=t,this.interpolateShape(t,this._cachingAtTime.shapeValue,this._cachingAtTime)),this._cachingAtTime.shapeValue},h.prototype.initiateExpression=ExpressionManager.initiateExpression;var p=ShapePropertyFactory.getShapeProp;ShapePropertyFactory.getShapeProp=function(t,e,i,r,s){var n=p(t,e,i,r,s);return n.propertyIndex=e.ix,n.lock=!1,3===i?expressionHelpers.searchExpressions(t,e.pt,n):4===i&&expressionHelpers.searchExpressions(t,e.ks,n),n.k&&t.addDynamicProperty(n),n}}function initialize$1(){addPropertyDecorator()}function addDecorator(){TextProperty.prototype.getExpressionValue=function(t,e){var i=this.calculateExpression(e);if(t.t!==i){var r={};return this.copyData(r,t),r.t=i.toString(),r.__complete=!1,r}return t},TextProperty.prototype.searchProperty=function(){var t=this.searchKeyframes(),e=this.searchExpressions();return this.kf=t||e,this.kf},TextProperty.prototype.searchExpressions=function(){return this.data.d.x?(this.calculateExpression=ExpressionManager.initiateExpression.bind(this)(this.elem,this.data.d,this),this.addEffect(this.getExpressionValue.bind(this)),!0):null}}function initialize(){addDecorator()}function SVGComposableEffect(){}SVGComposableEffect.prototype={createMergeNode:function(t,e){var i,r,s=createNS("feMerge");for(s.setAttribute("result",t),r=0;r<e.length;r+=1)(i=createNS("feMergeNode")).setAttribute("in",e[r]),s.appendChild(i),s.appendChild(i);return s}};var linearFilterValue="0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0";function SVGTintFilter(t,e,i,r,s){this.filterManager=e;var n=createNS("feColorMatrix");n.setAttribute("type","matrix"),n.setAttribute("color-interpolation-filters","linearRGB"),n.setAttribute("values",linearFilterValue+" 1 0"),this.linearFilter=n,n.setAttribute("result",r+"_tint_1"),t.appendChild(n),(n=createNS("feColorMatrix")).setAttribute("type","matrix"),n.setAttribute("color-interpolation-filters","sRGB"),n.setAttribute("values","1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"),n.setAttribute("result",r+"_tint_2"),t.appendChild(n),this.matrixFilter=n;var a=this.createMergeNode(r,[s,r+"_tint_1",r+"_tint_2"]);t.appendChild(a)}function SVGFillFilter(t,e,i,r){this.filterManager=e;var s=createNS("feColorMatrix");s.setAttribute("type","matrix"),s.setAttribute("color-interpolation-filters","sRGB"),s.setAttribute("values","1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"),s.setAttribute("result",r),t.appendChild(s),this.matrixFilter=s}function SVGStrokeEffect(t,e,i){this.initialized=!1,this.filterManager=e,this.elem=i,this.paths=[]}function SVGTritoneFilter(t,e,i,r){this.filterManager=e;var s=createNS("feColorMatrix");s.setAttribute("type","matrix"),s.setAttribute("color-interpolation-filters","linearRGB"),s.setAttribute("values","0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0"),t.appendChild(s);var n=createNS("feComponentTransfer");n.setAttribute("color-interpolation-filters","sRGB"),n.setAttribute("result",r),this.matrixFilter=n;var a=createNS("feFuncR");a.setAttribute("type","table"),n.appendChild(a),this.feFuncR=a;var o=createNS("feFuncG");o.setAttribute("type","table"),n.appendChild(o),this.feFuncG=o;var h=createNS("feFuncB");h.setAttribute("type","table"),n.appendChild(h),this.feFuncB=h,t.appendChild(n)}function SVGProLevelsFilter(t,e,i,r){this.filterManager=e;var s=this.filterManager.effectElements,n=createNS("feComponentTransfer");(s[10].p.k||0!==s[10].p.v||s[11].p.k||1!==s[11].p.v||s[12].p.k||1!==s[12].p.v||s[13].p.k||0!==s[13].p.v||s[14].p.k||1!==s[14].p.v)&&(this.feFuncR=this.createFeFunc("feFuncR",n)),(s[17].p.k||0!==s[17].p.v||s[18].p.k||1!==s[18].p.v||s[19].p.k||1!==s[19].p.v||s[20].p.k||0!==s[20].p.v||s[21].p.k||1!==s[21].p.v)&&(this.feFuncG=this.createFeFunc("feFuncG",n)),(s[24].p.k||0!==s[24].p.v||s[25].p.k||1!==s[25].p.v||s[26].p.k||1!==s[26].p.v||s[27].p.k||0!==s[27].p.v||s[28].p.k||1!==s[28].p.v)&&(this.feFuncB=this.createFeFunc("feFuncB",n)),(s[31].p.k||0!==s[31].p.v||s[32].p.k||1!==s[32].p.v||s[33].p.k||1!==s[33].p.v||s[34].p.k||0!==s[34].p.v||s[35].p.k||1!==s[35].p.v)&&(this.feFuncA=this.createFeFunc("feFuncA",n)),(this.feFuncR||this.feFuncG||this.feFuncB||this.feFuncA)&&(n.setAttribute("color-interpolation-filters","sRGB"),t.appendChild(n)),(s[3].p.k||0!==s[3].p.v||s[4].p.k||1!==s[4].p.v||s[5].p.k||1!==s[5].p.v||s[6].p.k||0!==s[6].p.v||s[7].p.k||1!==s[7].p.v)&&((n=createNS("feComponentTransfer")).setAttribute("color-interpolation-filters","sRGB"),n.setAttribute("result",r),t.appendChild(n),this.feFuncRComposed=this.createFeFunc("feFuncR",n),this.feFuncGComposed=this.createFeFunc("feFuncG",n),this.feFuncBComposed=this.createFeFunc("feFuncB",n))}function SVGDropShadowEffect(t,e,i,r,s){var n=e.container.globalData.renderConfig.filterSize,a=e.data.fs||n;t.setAttribute("x",a.x||n.x),t.setAttribute("y",a.y||n.y),t.setAttribute("width",a.width||n.width),t.setAttribute("height",a.height||n.height),this.filterManager=e;var o=createNS("feGaussianBlur");o.setAttribute("in","SourceAlpha"),o.setAttribute("result",r+"_drop_shadow_1"),o.setAttribute("stdDeviation","0"),this.feGaussianBlur=o,t.appendChild(o);var h=createNS("feOffset");h.setAttribute("dx","25"),h.setAttribute("dy","0"),h.setAttribute("in",r+"_drop_shadow_1"),h.setAttribute("result",r+"_drop_shadow_2"),this.feOffset=h,t.appendChild(h);var l=createNS("feFlood");l.setAttribute("flood-color","#00ff00"),l.setAttribute("flood-opacity","1"),l.setAttribute("result",r+"_drop_shadow_3"),this.feFlood=l,t.appendChild(l);var p=createNS("feComposite");p.setAttribute("in",r+"_drop_shadow_3"),p.setAttribute("in2",r+"_drop_shadow_2"),p.setAttribute("operator","in"),p.setAttribute("result",r+"_drop_shadow_4"),t.appendChild(p);var f=this.createMergeNode(r,[r+"_drop_shadow_4",s]);t.appendChild(f)}extendPrototype([SVGComposableEffect],SVGTintFilter),SVGTintFilter.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){var e=this.filterManager.effectElements[0].p.v,i=this.filterManager.effectElements[1].p.v,r=this.filterManager.effectElements[2].p.v/100;this.linearFilter.setAttribute("values",linearFilterValue+" "+r+" 0"),this.matrixFilter.setAttribute("values",i[0]-e[0]+" 0 0 0 "+e[0]+" "+(i[1]-e[1])+" 0 0 0 "+e[1]+" "+(i[2]-e[2])+" 0 0 0 "+e[2]+" 0 0 0 1 0")}},SVGFillFilter.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){var e=this.filterManager.effectElements[2].p.v,i=this.filterManager.effectElements[6].p.v;this.matrixFilter.setAttribute("values","0 0 0 0 "+e[0]+" 0 0 0 0 "+e[1]+" 0 0 0 0 "+e[2]+" 0 0 0 "+i+" 0")}},SVGStrokeEffect.prototype.initialize=function(){var t,e,i,r,s=this.elem.layerElement.children||this.elem.layerElement.childNodes;for(1===this.filterManager.effectElements[1].p.v?(r=this.elem.maskManager.masksProperties.length,i=0):r=(i=this.filterManager.effectElements[0].p.v-1)+1,(e=createNS("g")).setAttribute("fill","none"),e.setAttribute("stroke-linecap","round"),e.setAttribute("stroke-dashoffset",1);i<r;i+=1)t=createNS("path"),e.appendChild(t),this.paths.push({p:t,m:i});if(3===this.filterManager.effectElements[10].p.v){var n=createNS("mask"),a=createElementID();n.setAttribute("id",a),n.setAttribute("mask-type","alpha"),n.appendChild(e),this.elem.globalData.defs.appendChild(n);var o=createNS("g");for(o.setAttribute("mask","url("+getLocationHref()+"#"+a+")");s[0];)o.appendChild(s[0]);this.elem.layerElement.appendChild(o),this.masker=n,e.setAttribute("stroke","#fff")}else if(1===this.filterManager.effectElements[10].p.v||2===this.filterManager.effectElements[10].p.v){if(2===this.filterManager.effectElements[10].p.v)for(s=this.elem.layerElement.children||this.elem.layerElement.childNodes;s.length;)this.elem.layerElement.removeChild(s[0]);this.elem.layerElement.appendChild(e),this.elem.layerElement.removeAttribute("mask"),e.setAttribute("stroke","#fff")}this.initialized=!0,this.pathMasker=e},SVGStrokeEffect.prototype.renderFrame=function(t){this.initialized||this.initialize();var e,i,r,s=this.paths.length;for(e=0;e<s;e+=1)if(-1!==this.paths[e].m&&(i=this.elem.maskManager.viewData[this.paths[e].m],r=this.paths[e].p,(t||this.filterManager._mdf||i.prop._mdf)&&r.setAttribute("d",i.lastPath),t||this.filterManager.effectElements[9].p._mdf||this.filterManager.effectElements[4].p._mdf||this.filterManager.effectElements[7].p._mdf||this.filterManager.effectElements[8].p._mdf||i.prop._mdf)){if(0!==this.filterManager.effectElements[7].p.v||100!==this.filterManager.effectElements[8].p.v){var n,a=.01*Math.min(this.filterManager.effectElements[7].p.v,this.filterManager.effectElements[8].p.v),o=.01*Math.max(this.filterManager.effectElements[7].p.v,this.filterManager.effectElements[8].p.v),h=r.getTotalLength(),l="0 0 0 "+h*a+" ",p=Math.floor(h*(o-a)/(1+2*this.filterManager.effectElements[4].p.v*this.filterManager.effectElements[9].p.v*.01));for(n=0;n<p;n+=1)l+="1 "+2*this.filterManager.effectElements[4].p.v*this.filterManager.effectElements[9].p.v*.01+" ";l+="0 "+10*h+" 0 0"}else l="1 "+2*this.filterManager.effectElements[4].p.v*this.filterManager.effectElements[9].p.v*.01;r.setAttribute("stroke-dasharray",l)}if((t||this.filterManager.effectElements[4].p._mdf)&&this.pathMasker.setAttribute("stroke-width",2*this.filterManager.effectElements[4].p.v),(t||this.filterManager.effectElements[6].p._mdf)&&this.pathMasker.setAttribute("opacity",this.filterManager.effectElements[6].p.v),(1===this.filterManager.effectElements[10].p.v||2===this.filterManager.effectElements[10].p.v)&&(t||this.filterManager.effectElements[3].p._mdf)){var f=this.filterManager.effectElements[3].p.v;this.pathMasker.setAttribute("stroke","rgb("+bmFloor(255*f[0])+","+bmFloor(255*f[1])+","+bmFloor(255*f[2])+")")}},SVGTritoneFilter.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){var e=this.filterManager.effectElements[0].p.v,i=this.filterManager.effectElements[1].p.v,r=this.filterManager.effectElements[2].p.v,s=r[0]+" "+i[0]+" "+e[0],n=r[1]+" "+i[1]+" "+e[1],a=r[2]+" "+i[2]+" "+e[2];this.feFuncR.setAttribute("tableValues",s),this.feFuncG.setAttribute("tableValues",n),this.feFuncB.setAttribute("tableValues",a)}},SVGProLevelsFilter.prototype.createFeFunc=function(t,e){var i=createNS(t);return i.setAttribute("type","table"),e.appendChild(i),i},SVGProLevelsFilter.prototype.getTableValue=function(t,e,i,r,s){for(var n,a,o=0,h=Math.min(t,e),l=Math.max(t,e),p=Array.call(null,{length:256}),f=0,u=s-r,c=e-t;o<=256;)a=(n=o/256)<=h?c<0?s:r:n>=l?c<0?r:s:r+u*Math.pow((n-t)/c,1/i),p[f]=a,f+=1,o+=256/255;return p.join(" ")},SVGProLevelsFilter.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){var e,i=this.filterManager.effectElements;this.feFuncRComposed&&(t||i[3].p._mdf||i[4].p._mdf||i[5].p._mdf||i[6].p._mdf||i[7].p._mdf)&&(e=this.getTableValue(i[3].p.v,i[4].p.v,i[5].p.v,i[6].p.v,i[7].p.v),this.feFuncRComposed.setAttribute("tableValues",e),this.feFuncGComposed.setAttribute("tableValues",e),this.feFuncBComposed.setAttribute("tableValues",e)),this.feFuncR&&(t||i[10].p._mdf||i[11].p._mdf||i[12].p._mdf||i[13].p._mdf||i[14].p._mdf)&&(e=this.getTableValue(i[10].p.v,i[11].p.v,i[12].p.v,i[13].p.v,i[14].p.v),this.feFuncR.setAttribute("tableValues",e)),this.feFuncG&&(t||i[17].p._mdf||i[18].p._mdf||i[19].p._mdf||i[20].p._mdf||i[21].p._mdf)&&(e=this.getTableValue(i[17].p.v,i[18].p.v,i[19].p.v,i[20].p.v,i[21].p.v),this.feFuncG.setAttribute("tableValues",e)),this.feFuncB&&(t||i[24].p._mdf||i[25].p._mdf||i[26].p._mdf||i[27].p._mdf||i[28].p._mdf)&&(e=this.getTableValue(i[24].p.v,i[25].p.v,i[26].p.v,i[27].p.v,i[28].p.v),this.feFuncB.setAttribute("tableValues",e)),this.feFuncA&&(t||i[31].p._mdf||i[32].p._mdf||i[33].p._mdf||i[34].p._mdf||i[35].p._mdf)&&(e=this.getTableValue(i[31].p.v,i[32].p.v,i[33].p.v,i[34].p.v,i[35].p.v),this.feFuncA.setAttribute("tableValues",e))}},extendPrototype([SVGComposableEffect],SVGDropShadowEffect),SVGDropShadowEffect.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){if((t||this.filterManager.effectElements[4].p._mdf)&&this.feGaussianBlur.setAttribute("stdDeviation",this.filterManager.effectElements[4].p.v/4),t||this.filterManager.effectElements[0].p._mdf){var e=this.filterManager.effectElements[0].p.v;this.feFlood.setAttribute("flood-color",rgbToHex(Math.round(255*e[0]),Math.round(255*e[1]),Math.round(255*e[2])))}if((t||this.filterManager.effectElements[1].p._mdf)&&this.feFlood.setAttribute("flood-opacity",this.filterManager.effectElements[1].p.v/255),t||this.filterManager.effectElements[2].p._mdf||this.filterManager.effectElements[3].p._mdf){var i=this.filterManager.effectElements[3].p.v,r=(this.filterManager.effectElements[2].p.v-90)*degToRads,s=i*Math.cos(r),n=i*Math.sin(r);this.feOffset.setAttribute("dx",s),this.feOffset.setAttribute("dy",n)}}};var _svgMatteSymbols=[];function SVGMatte3Effect(t,e,i){this.initialized=!1,this.filterManager=e,this.filterElem=t,this.elem=i,i.matteElement=createNS("g"),i.matteElement.appendChild(i.layerElement),i.matteElement.appendChild(i.transformedElement),i.baseElement=i.matteElement}function SVGGaussianBlurEffect(t,e,i,r){t.setAttribute("x","-100%"),t.setAttribute("y","-100%"),t.setAttribute("width","300%"),t.setAttribute("height","300%"),this.filterManager=e;var s=createNS("feGaussianBlur");s.setAttribute("result",r),t.appendChild(s),this.feGaussianBlur=s}function TransformEffect(){}function SVGTransformEffect(t,e){this.init(e)}function CVTransformEffect(t){this.init(t)}return SVGMatte3Effect.prototype.findSymbol=function(t){for(var e=0,i=_svgMatteSymbols.length;e<i;){if(_svgMatteSymbols[e]===t)return _svgMatteSymbols[e];e+=1}return null},SVGMatte3Effect.prototype.replaceInParent=function(t,e){var i=t.layerElement.parentNode;if(i){for(var r,s=i.children,n=0,a=s.length;n<a&&s[n]!==t.layerElement;)n+=1;n<=a-2&&(r=s[n+1]);var o=createNS("use");o.setAttribute("href","#"+e),r?i.insertBefore(o,r):i.appendChild(o)}},SVGMatte3Effect.prototype.setElementAsMask=function(t,e){if(!this.findSymbol(e)){var i=createElementID(),r=createNS("mask");r.setAttribute("id",e.layerId),r.setAttribute("mask-type","alpha"),_svgMatteSymbols.push(e);var s=t.globalData.defs;s.appendChild(r);var n=createNS("symbol");n.setAttribute("id",i),this.replaceInParent(e,i),n.appendChild(e.layerElement),s.appendChild(n);var a=createNS("use");a.setAttribute("href","#"+i),r.appendChild(a),e.data.hd=!1,e.show()}t.setMatte(e.layerId)},SVGMatte3Effect.prototype.initialize=function(){for(var t=this.filterManager.effectElements[0].p.v,e=this.elem.comp.elements,i=0,r=e.length;i<r;)e[i]&&e[i].data.ind===t&&this.setElementAsMask(this.elem,e[i]),i+=1;this.initialized=!0},SVGMatte3Effect.prototype.renderFrame=function(){this.initialized||this.initialize()},SVGGaussianBlurEffect.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){var e=.3*this.filterManager.effectElements[0].p.v,i=this.filterManager.effectElements[1].p.v,r=3==i?0:e,s=2==i?0:e;this.feGaussianBlur.setAttribute("stdDeviation",r+" "+s);var n=1==this.filterManager.effectElements[2].p.v?"wrap":"duplicate";this.feGaussianBlur.setAttribute("edgeMode",n)}},TransformEffect.prototype.init=function(t){this.effectsManager=t,this.type=effectTypes.TRANSFORM_EFFECT,this.matrix=new Matrix,this.opacity=-1,this._mdf=!1,this._opMdf=!1},TransformEffect.prototype.renderFrame=function(t){if(this._opMdf=!1,this._mdf=!1,t||this.effectsManager._mdf){var e=this.effectsManager.effectElements,i=e[0].p.v,r=e[1].p.v,s=1===e[2].p.v,n=e[3].p.v,a=s?n:e[4].p.v,o=e[5].p.v,h=e[6].p.v,l=e[7].p.v;this.matrix.reset(),this.matrix.translate(-i[0],-i[1],i[2]),this.matrix.scale(.01*a,.01*n,1),this.matrix.rotate(-l*degToRads),this.matrix.skewFromAxis(-o*degToRads,(h+90)*degToRads),this.matrix.translate(r[0],r[1],0),this._mdf=!0,this.opacity!==e[8].p.v&&(this.opacity=e[8].p.v,this._opMdf=!0)}},extendPrototype([TransformEffect],SVGTransformEffect),extendPrototype([TransformEffect],CVTransformEffect),registerRenderer("canvas",CanvasRenderer),registerRenderer("html",HybridRenderer),registerRenderer("svg",SVGRenderer),ShapeModifiers.registerModifier("tm",TrimModifier),ShapeModifiers.registerModifier("pb",PuckerAndBloatModifier),ShapeModifiers.registerModifier("rp",RepeaterModifier),ShapeModifiers.registerModifier("rd",RoundCornersModifier),ShapeModifiers.registerModifier("zz",ZigZagModifier),ShapeModifiers.registerModifier("op",OffsetPathModifier),setExpressionsPlugin(Expressions),setExpressionInterfaces(getInterface),initialize$1(),initialize(),registerEffect$1(20,SVGTintFilter,!0),registerEffect$1(21,SVGFillFilter,!0),registerEffect$1(22,SVGStrokeEffect,!1),registerEffect$1(23,SVGTritoneFilter,!0),registerEffect$1(24,SVGProLevelsFilter,!0),registerEffect$1(25,SVGDropShadowEffect,!0),registerEffect$1(28,SVGMatte3Effect,!1),registerEffect$1(29,SVGGaussianBlurEffect,!0),registerEffect$1(35,SVGTransformEffect,!1),registerEffect(35,CVTransformEffect),lottie})},9516:function(t,e,i){"use strict";i.r(e),i.d(e,{compose:()=>I,createStore:()=>S,bindActionCreators:()=>F,combineReducers:()=>E,applyMiddleware:()=>O});let r="object"==typeof global&&global&&global.Object===Object&&global;var s="object"==typeof self&&self&&self.Object===Object&&self;let n=(r||s||Function("return this")()).Symbol;var a=Object.prototype,o=a.hasOwnProperty,h=a.toString,l=n?n.toStringTag:void 0;let p=function(t){var e=o.call(t,l),i=t[l];try{t[l]=void 0;var r=!0}catch(t){}var s=h.call(t);return r&&(e?t[l]=i:delete t[l]),s};var f=Object.prototype.toString;let u=function(t){return f.call(t)};var c="[object Null]",m="[object Undefined]",d=n?n.toStringTag:void 0;let g=function(t){return null==t?void 0===t?m:c:d&&d in Object(t)?p(t):u(t)},v=function(t,e){return function(i){return t(e(i))}}(Object.getPrototypeOf,Object),y=function(t){return null!=t&&"object"==typeof t};var b="[object Object]",x=Object.prototype,_=Function.prototype.toString,A=x.hasOwnProperty,k=_.call(Object);let w=function(t){if(!y(t)||g(t)!=b)return!1;var e=v(t);if(null===e)return!0;var i=A.call(e,"constructor")&&e.constructor;return"function"==typeof i&&i instanceof i&&_.call(i)==k};var P=i(3485),C={INIT:"@@redux/INIT"};function S(t,e,i){if("function"==typeof e&&void 0===i&&(i=e,e=void 0),void 0!==i){if("function"!=typeof i)throw Error("Expected the enhancer to be a function.");return i(S)(t,e)}if("function"!=typeof t)throw Error("Expected the reducer to be a function.");var r,s=t,n=e,a=[],o=a,h=!1;function l(){o===a&&(o=a.slice())}function p(){return n}function f(t){if("function"!=typeof t)throw Error("Expected listener to be a function.");var e=!0;return l(),o.push(t),function(){if(e){e=!1,l();var i=o.indexOf(t);o.splice(i,1)}}}function u(t){if(!w(t))throw Error("Actions must be plain objects. Use custom middleware for async actions.");if(void 0===t.type)throw Error('Actions may not have an undefined "type" property. Have you misspelled a constant?');if(h)throw Error("Reducers may not dispatch actions.");try{h=!0,n=s(n,t)}finally{h=!1}for(var e=a=o,i=0;i<e.length;i++)e[i]();return t}function c(t){if("function"!=typeof t)throw Error("Expected the nextReducer to be a function.");s=t,u({type:C.INIT})}function m(){var t,e=f;return(t={subscribe:function(t){if("object"!=typeof t)throw TypeError("Expected the observer to be an object.");function i(){t.next&&t.next(p())}return i(),{unsubscribe:e(i)}}})[P.Z]=function(){return this},t}return u({type:C.INIT}),(r={dispatch:u,subscribe:f,getState:p,replaceReducer:c})[P.Z]=m,r}function D(t,e){var i=e&&e.type;return"Given action "+(i&&'"'+i.toString()+'"'||"an action")+', reducer "'+t+'" returned undefined. To ignore an action, you must explicitly return the previous state.'}function T(t){Object.keys(t).forEach(function(e){var i=t[e];if(void 0===i(void 0,{type:C.INIT}))throw Error('Reducer "'+e+'" returned undefined during initialization. If the state passed to the reducer is undefined, you must explicitly return the initial state. The initial state may not be undefined.');if(void 0===i(void 0,{type:"@@redux/PROBE_UNKNOWN_ACTION_"+Math.random().toString(36).substring(7).split("").join(".")}))throw Error('Reducer "'+e+"\" returned undefined when probed with a random type. Don't try to handle "+C.INIT+' or other actions in "redux/*" namespace. They are considered private. Instead, you must return the current state for any unknown actions, unless it is undefined, in which case you must return the initial state, regardless of the action type. The initial state may not be undefined.')})}function E(t){for(var e,i=Object.keys(t),r={},s=0;s<i.length;s++){var n=i[s];"function"==typeof t[n]&&(r[n]=t[n])}var a=Object.keys(r);try{T(r)}catch(t){e=t}return function(){var t=arguments.length<=0||void 0===arguments[0]?{}:arguments[0],i=arguments[1];if(e)throw e;for(var s=!1,n={},o=0;o<a.length;o++){var h=a[o],l=r[h],p=t[h],f=l(p,i);if(void 0===f)throw Error(D(h,i));n[h]=f,s=s||f!==p}return s?n:t}}function M(t,e){return function(){return e(t.apply(void 0,arguments))}}function F(t,e){if("function"==typeof t)return M(t,e);if("object"!=typeof t||null===t)throw Error("bindActionCreators expected an object or a function, instead received "+(null===t?"null":typeof t)+'. Did you write "import ActionCreators from" instead of "import * as ActionCreators from"?');for(var i=Object.keys(t),r={},s=0;s<i.length;s++){var n=i[s],a=t[n];"function"==typeof a&&(r[n]=M(a,e))}return r}function I(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];if(0===e.length)return function(t){return t};if(1===e.length)return e[0];var r=e[e.length-1],s=e.slice(0,-1);return function(){return s.reduceRight(function(t,e){return e(t)},r.apply(void 0,arguments))}}var L=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(t[r]=i[r])}return t};function O(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return function(t){return function(i,r,s){var n=t(i,r,s),a=n.dispatch,o=[],h={getState:n.getState,dispatch:function(t){return a(t)}};return o=e.map(function(t){return t(h)}),a=I.apply(void 0,o)(n.dispatch),L({},n,{dispatch:a})}}}},3485:function(t,e,i){"use strict";var r;function s(t){var e,i=t.Symbol;return"function"==typeof i?i.observable?e=i.observable:(e=i("observable"),i.observable=e):e="@@observable",e}i.d(e,{Z:()=>n}),t=i.hmd(t);let n=s(r="undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==i.g?i.g:t)},1185:function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};e.clone=o,e.addLast=p,e.addFirst=f,e.removeLast=u,e.removeFirst=c,e.insert=m,e.removeAt=d,e.replaceAt=g,e.getIn=v,e.set=y,e.setIn=x,e.update=_,e.updateIn=A,e.merge=k,e.mergeDeep=w,e.mergeIn=P,e.omit=C,e.addDefaults=S;var r="INVALID_ARGS";function s(t){throw Error(t)}function n(t){var e=Object.keys(t);return Object.getOwnPropertySymbols?e.concat(Object.getOwnPropertySymbols(t)):e}var a={}.hasOwnProperty;function o(t){if(Array.isArray(t))return t.slice();for(var e=n(t),i={},r=0;r<e.length;r++){var s=e[r];i[s]=t[s]}return i}function h(t,e,i){var a=i;null==a&&s(r);for(var p=!1,f=arguments.length,u=Array(f>3?f-3:0),c=3;c<f;c++)u[c-3]=arguments[c];for(var m=0;m<u.length;m++){var d=u[m];if(null!=d){var g=n(d);if(g.length)for(var v=0;v<=g.length;v++){var y=g[v];if(!t||void 0===a[y]){var b=d[y];e&&l(a[y])&&l(b)&&(b=h(t,e,a[y],b)),void 0!==b&&b!==a[y]&&(p||(p=!0,a=o(a)),a[y]=b)}}}}return a}function l(t){var e=void 0===t?"undefined":i(t);return null!=t&&("object"===e||"function"===e)}function p(t,e){return Array.isArray(e)?t.concat(e):t.concat([e])}function f(t,e){return Array.isArray(e)?e.concat(t):[e].concat(t)}function u(t){return t.length?t.slice(0,t.length-1):t}function c(t){return t.length?t.slice(1):t}function m(t,e,i){return t.slice(0,e).concat(Array.isArray(i)?i:[i]).concat(t.slice(e))}function d(t,e){return e>=t.length||e<0?t:t.slice(0,e).concat(t.slice(e+1))}function g(t,e,i){if(t[e]===i)return t;for(var r=t.length,s=Array(r),n=0;n<r;n++)s[n]=t[n];return s[e]=i,s}function v(t,e){if(Array.isArray(e)||s(r),null!=t){for(var i=t,n=0;n<e.length;n++){var a=e[n];if(void 0===(i=null!=i?i[a]:void 0))break}return i}}function y(t,e,i){var r="number"==typeof e?[]:{},s=null==t?r:t;if(s[e]===i)return s;var n=o(s);return n[e]=i,n}function b(t,e,i,r){var s=void 0,n=e[r];return s=r===e.length-1?i:b(l(t)&&l(t[n])?t[n]:"number"==typeof e[r+1]?[]:{},e,i,r+1),y(t,n,s)}function x(t,e,i){return e.length?b(t,e,i,0):i}function _(t,e,i){var r=i(null==t?void 0:t[e]);return y(t,e,r)}function A(t,e,i){var r=i(v(t,e));return x(t,e,r)}function k(t,e,i,r,s,n){for(var a=arguments.length,o=Array(a>6?a-6:0),l=6;l<a;l++)o[l-6]=arguments[l];return o.length?h.call.apply(h,[null,!1,!1,t,e,i,r,s,n].concat(o)):h(!1,!1,t,e,i,r,s,n)}function w(t,e,i,r,s,n){for(var a=arguments.length,o=Array(a>6?a-6:0),l=6;l<a;l++)o[l-6]=arguments[l];return o.length?h.call.apply(h,[null,!1,!0,t,e,i,r,s,n].concat(o)):h(!1,!0,t,e,i,r,s,n)}function P(t,e,i,r,s,n,a){var o=v(t,e);null==o&&(o={});for(var l=void 0,p=arguments.length,f=Array(p>7?p-7:0),u=7;u<p;u++)f[u-7]=arguments[u];return x(t,e,l=f.length?h.call.apply(h,[null,!1,!1,o,i,r,s,n,a].concat(f)):h(!1,!1,o,i,r,s,n,a))}function C(t,e){for(var i=Array.isArray(e)?e:[e],r=!1,s=0;s<i.length;s++)if(a.call(t,i[s])){r=!0;break}if(!r)return t;for(var o={},h=n(t),l=0;l<h.length;l++){var p=h[l];i.indexOf(p)>=0||(o[p]=t[p])}return o}function S(t,e,i,r,s,n){for(var a=arguments.length,o=Array(a>6?a-6:0),l=6;l<a;l++)o[l-6]=arguments[l];return o.length?h.call.apply(h,[null,!0,!1,t,e,i,r,s,n].concat(o)):h(!0,!1,t,e,i,r,s,n)}e.default={clone:o,addLast:p,addFirst:f,removeLast:u,removeFirst:c,insert:m,removeAt:d,replaceAt:g,getIn:v,set:y,setIn:x,update:_,updateIn:A,merge:k,mergeDeep:w,mergeIn:P,omit:C,addDefaults:S}}}]);